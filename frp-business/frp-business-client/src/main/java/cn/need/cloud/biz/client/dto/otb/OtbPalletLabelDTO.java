package cn.need.cloud.biz.client.dto.otb;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTB托盘标签 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OtbPalletLabelDTO extends SuperDTO {


    /**
     * 行序号
     */
    private Integer lineNum;

    /**
     * 面单类型
     */
    private String labelType;

    /**
     * label RefNum
     */
    private String labelRefNum;

    /**
     * 纸张类型
     */
    private String paperType;

    /**
     * 数据类型
     */
    private String rawDataType;

    /**
     * label数据类型
     */
    private String labelRawData;

    /**
     * 打印状态
     */
    private String printStatus;

    /**
     * otb托盘id
     */
    private Long otbPalletId;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 文件系统数据类型
     */
    private String fileIdRawDataType;

}