package cn.need.cloud.biz.client.constant.enums.otb;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * Otb包裹枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OtbPackageStatusEnum {
    /**
     * 半箱
     */
    TAIL("Tail"),

    /**
     * 整箱
     */
    FULL("Full"),

    /**
     * 新建
     */
    NEW("New"),

    /**
     * 已打包
     */
    PACKED("Packed"),

    /**
     * 构建打托
     */
    BUILD_PALLET("BuildPallet"),

    /**
     * 构建发货
     */
    BUILD_SHIPMENT("BuildShipment"),

    /**
     * 待渠道确认
     */
    WAIT_CHANNEL_CONFIRM("WaitChannelConfirm"),

    /**
     * 渠道已确认
     */
    CHANNEL_CONFIRM("ChannelConfirmed"),

    /**
     * 构建发货label
     */
    BUILD_SHIPMENT_LABEL("BuildShippingLabel"),

    /**
     * 准备发货
     */
    READY_TO_SHIP("ReadyToShip"),

    /**
     * 已经发货
     */
    SHIPPED("Shipped"),

    /**
     * 取消
     */
    CANCELLED("Cancelled"),

    ;

    @EnumValue
    @JsonValue
    private final String code;

    public static List<String> canRollbackStatuses() {
        return List.of(NEW.getCode(), PACKED.getCode());
    }

    public static List<String> canCancelStatuses() {
        return List.of(NEW.getCode(), PACKED.getCode());
    }
}
