package cn.need.cloud.biz.client.dto.feeconfig;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * 仓库报价费用配置otc详情 dto对象
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FeeConfigOtcDetailDTO extends SuperDTO {


    /**
     * 是否有效(0-无效，1-有效)
     */
    private Boolean activeFlag;

    /**
     * 基础价格
     */
    private BigDecimal baseFee;

    /**
     * 删除原因
     */
    private String deletedNote;

    /**
     * 计费起始阈值（实际数量超过该值时触发计费）
     */
    private BigDecimal feeStartThreshold;

    /**
     * 计费单位类型
     */
    private String feeUnitType;

    /**
     * header表id
     */
    private Long headerId;

    /**
     * 行序号
     */
    private Integer lineNum;

    /**
     * 备注
     */
    private String note;

    /**
     * 结束（不包含）根据condition_type得到值来判断
     */
    private Long sectionEnd;

    /**
     * 开始（包含）根据condition_type得到值来判断
     */
    private Long sectionStart;

    /**
     * 单价
     */
    private BigDecimal unitFee;

    /**
     * 仓库id
     */
    private Long warehouseId;

}