package cn.need.cloud.biz.client.dto.base;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 交易伙伴 DTO对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseTransactionPartnerDTO extends BaseTenantDTO {
    @Serial
    private static final long serialVersionUID = 8628352349152808902L;

    /**
     * 交易伙伴code
     */
    private String transactionPartnerCode;

    /**
     * 交易伙伴id
     */
    @JsonIgnore
    private Long transactionPartnerId;
}
