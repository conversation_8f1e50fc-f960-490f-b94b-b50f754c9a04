package cn.need.cloud.biz.client.dto.req.otb;

import cn.need.cloud.biz.client.dto.req.base.BaseLogisticPartnerAndWarehouseReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serial;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTB装运详情查询请求对象")
public class OtbShipmentDetailQueryReqDTO extends BaseLogisticPartnerAndWarehouseReqDTO {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "refNum")
    @NotNull(message = "refNum cannot be null")
    private String refNum;
}
