package cn.need.cloud.biz.client.constant.enums.inbound;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 入库卸货状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum InboundRequestStatusEnum {

    /**
     * 新建
     */
    NEW("New"),

    /**
     * 同意
     */
    APPROVED("Approved"),

    /**
     * 拒绝
     */
    REJECTED("Rejected"),

    /**
     * 进行中
     */
    PROCESSING("Processing"),

    /**
     * 进行中
     */
    PROCESSED("Processed"),

    /**
     * 取消
     */
    CANCELLED("Cancelled");

    @EnumValue
    @JsonValue
    private final String status;

    public static List<String> getCanAudit() {
        return List.of(NEW.getStatus(), REJECTED.getStatus());
    }

    public static List<String> getCanEdit() {
        return List.of(NEW.getStatus(), REJECTED.getStatus());
    }
}
