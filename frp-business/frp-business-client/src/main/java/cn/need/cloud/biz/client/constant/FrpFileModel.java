package cn.need.cloud.biz.client.constant;

import cn.need.cloud.biz.client.constant.enums.otb.PaperTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@Schema(description = " FRPFileModel")
public class FrpFileModel implements Serializable {

    @Serial
    private static final long serialVersionUID = -6617638116255794567L;
    /**
     * 文件数据
     */
    @Schema(description = "文件数据")
    private String fileData;

    /**
     * 文件扩展名
     */
    @Schema(description = "文件扩展名")
    private String fileExtension;

    /**
     * 文件类型
     */
    @Schema(description = "文件类型")
    private FileDataTypeEnum fileType;

    /**
     * 文件纸张类型
     */
    @Schema(description = "文件纸张类型")
    private PaperTypeEnum paperType;

}
