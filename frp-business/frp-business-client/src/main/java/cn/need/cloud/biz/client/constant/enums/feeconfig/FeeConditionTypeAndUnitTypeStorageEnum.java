package cn.need.cloud.biz.client.constant.enums.feeconfig;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 计费条件类型（决定区间判断的依据）
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FeeConditionTypeAndUnitTypeStorageEnum {

    /**
     * 按天计费
     */
    DAY("Day"),

    /**
     * 按月计费
     */
    MONTH("Month"),

    /**
     * 按年计费
     */
    YEAR("Year"),

    /**
     * 按体积计费
     */
    VOLUME("Volume"),
    /**
     * 按重量计费
     */
    WEIGHT("Weight"),

    /**
     * 按箱计费
     */
    BOX("Box"),
    /**
     * 按件计费
     */
    PCS("Pcs"),
    /**
     * 按品种计费
     */
    SPECIES("Species"),
    /**
     * 按托盘计费
     */
    PALLET("Pallet");

    @EnumValue
    @JsonValue
    private final String code;


}

