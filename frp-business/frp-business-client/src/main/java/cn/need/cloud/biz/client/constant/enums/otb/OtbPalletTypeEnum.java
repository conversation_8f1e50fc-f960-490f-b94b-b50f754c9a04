package cn.need.cloud.biz.client.constant.enums.otb;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Otb打托单类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OtbPalletTypeEnum {
    /**
     *
     */
    SINGLE("Single"),

    /**
     *
     */
    MIXED("Mixed"),
    ;

    @EnumValue
    @JsonValue
    private final String type;
}
