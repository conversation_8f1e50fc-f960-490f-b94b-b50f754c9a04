package cn.need.cloud.biz.client.constant;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;


/***
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Getter
@AllArgsConstructor
public enum FileDataTypeEnum {

    /**
     * Base64StringPNG
     */
    BASE_64_STRING_PNG("Base64StringPNG", "Base64String", "PNG"),

    /**
     * Base64SStringPDF
     */
    BASE_64_STRING_PDF("Base64StringPDF", "Base64String", "PDF"),

    /**
     * Base64StringZPL
     */
    BASE_64_STRING_ZPL("Base64StringZPL", "Base64String", "ZPL"),

    /**
     * BASE_64_STRING_ZIP
     */
    BASE_64_STRING_ZIP("Base64StringZIP", "Base64String", "ZIP"),

    /**
     * FileLink
     */
    FILE_LINK("FileLink", "", "");

    @EnumValue
    private final String type;

    private final String encodeFormat;

    private final String labelFormat;

    public static FileDataTypeEnum typeOf(String type) {
        for (FileDataTypeEnum typeEnum : values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        for (FileDataTypeEnum typeEnum : values()) {
            if (typeEnum.toString().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }

    public static List<String> getAllTypes() {
        return List.of(
                BASE_64_STRING_PDF.getType(),
                BASE_64_STRING_PNG.getType(),
                BASE_64_STRING_ZPL.getType(),
                BASE_64_STRING_ZIP.getType(),
                FILE_LINK.getType()
        );
    }
}
