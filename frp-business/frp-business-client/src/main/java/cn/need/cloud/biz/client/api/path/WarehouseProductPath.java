package cn.need.cloud.biz.client.api.path;

/**
 * <p>
 * 仓库产品路径常量定义
 * </p>
 *
 * <p>
 * 该接口定义了仓库产品相关的内部API路径常量，用于微服务间调用仓库产品服务的各个端点。
 * 包含产品标记为Slap and Go等特殊操作的路径定义。
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/26
 */
public interface WarehouseProductPath {

    String PREFIX = "/client/biz/warehouse-product";

    /**
     * 将产品标记为Slap and Go的API路径
     * <p>
     * 该路径用于将指定产品标记为Slap and Go状态，影响产品在仓库中的处理方式。
     * </p>
     */
    public static final String MARK_PRODUCT_AS_SLAP_AND_GO = "/markProductAsSlapAndGo";


}
