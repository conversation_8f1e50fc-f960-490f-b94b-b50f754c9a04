package cn.need.cloud.biz.client.constant.enums.inbound;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Set;

/**
 * 入库工单状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum InboundWorkOrderStatusEnum {
    /**
     * New
     */
    NEW("New"),

    /**
     * 产品到达仓库门口
     */
    PRODUCT_ARRIVED("ProductArrived"),

    /**
     * 打印WorkOrder单子
     */
    WORK_ORDER_PRINTED("WorkOrderPrinted"),

    /**
     * 卸货中，上架中
     */
    PROCESSING("Processing"),

    /**
     * 完成
     */
    PROCESSED("Processed"),

    /**
     * 取消
     */
    CANCEL("Cancelled"),

    /**
     * 测量
     */
    MEASURE("Measure"),

    UNLOAD("Unload"),

    ;
    @EnumValue
    @JsonValue
    private final String status;

    /**
     * 运输中
     */
    public static Set<String> getInTransit() {
        return Set.of(NEW.status);
    }


    /**
     * 收货中
     */
    public static Set<String> getInReceive() {
        return Set.of(PRODUCT_ARRIVED.status, WORK_ORDER_PRINTED.status, PROCESSING.status);
    }

}
