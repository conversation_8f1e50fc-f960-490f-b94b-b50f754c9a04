package cn.need.cloud.biz.client.constant.enums.product;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品组类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ProductConvertGroupTypeEnum {
    /**
     * 自定义
     */
    CUSTOM("Custom"),

    /**
     * 换Label
     */
    RE_LABEL("ReLabel"),

    /**
     * 多贴
     */
    EXTRA_LABEL("ExtraLabel");
    @EnumValue
    @JsonValue
    private final String type;
}

