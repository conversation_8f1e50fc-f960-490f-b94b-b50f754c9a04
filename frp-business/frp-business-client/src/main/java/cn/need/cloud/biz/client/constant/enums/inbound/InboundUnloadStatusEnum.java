package cn.need.cloud.biz.client.constant.enums.inbound;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 入库卸货状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum InboundUnloadStatusEnum {
    /**
     * New
     */
    NEW("New"),

    /**
     * 创建上架单
     */
    BUILD_PUT_AWAY_SLIP("BuildPutAwaySlip"),

    /**
     * 全部已分配
     */
    ALL_ASSIGNED("AllAssigned"),

    /**
     * 取消
     */
    CANCEL("Cancel"),

    /**
     * 完成
     */
    PROCESSED("Processed");

    @EnumValue
    @JsonValue
    private final String status;
}
