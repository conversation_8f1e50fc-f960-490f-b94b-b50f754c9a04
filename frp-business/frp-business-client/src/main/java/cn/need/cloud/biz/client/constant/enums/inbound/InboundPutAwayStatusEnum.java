package cn.need.cloud.biz.client.constant.enums.inbound;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 入库工单上架状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum InboundPutAwayStatusEnum {
    /**
     * 还没开始
     */
    NEW("New"),

    /**
     * 开始
     */
    PROCESSING("Processing"),

    /**
     * 取消
     */
    CANCELLED("Cancelled"),

    /**
     * 完成
     */
    PROCESSED("Processed"),

    PALLET_PUTAWAY("PalletPutAway"),

    PALLET("Pallet"),
    ;
    @EnumValue
    @JsonValue
    private final String status;
}
