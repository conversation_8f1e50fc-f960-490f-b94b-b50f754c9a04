<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otb.OtbPrepWorkorderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorder">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remove_flag" property="removeFlag"/>
        <result column="otb_workorder_id" property="otbWorkorderId"/>
        <result column="otb_workorder_detail_id" property="otbWorkorderDetailId"/>
        <result column="qty" property="qty"/>
        <result column="inventory_reserve_id" property="inventoryReserveId"/>
        <result column="putaway_qty" property="putawayQty"/>
        <result column="otb_prep_workorder_status" property="otbPrepWorkorderStatus"/>
        <result column="prep_workorder_type" property="prepWorkorderType"/>
        <result column="otb_prep_picking_slip_id" property="otbPrepPickingSlipId"/>
        <result column="version" property="version"/>
        <result column="deleted_note" property="deletedNote"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="ref_num" property="refNum"/>
        <result column="product_id" property="productId"/>
        <result column="product_barcode" property="productBarcode"/>
        <result column="bin_location_id" property="binLocationId"/>
        <result column="prep_workorder_version_int" property="prepWorkorderVersionInt"/>
        <result column="otb_request_id" property="otbRequestId"/>
        <result column="product_channel_sku" property="productChannelSku"/>
        <result column="transaction_partner_id" property="transactionPartnerId"/>
        <result column="prep_workorder_product_type" property="prepWorkorderProductType"/>
        <result column="process_type" property="processType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.otb_workorder_id,
        t.otb_workorder_detail_id,
        t.qty,
        t.inventory_reserve_id,
        t.putaway_qty,
        t.otb_prep_workorder_status,
        t.prep_workorder_type,
        t.otb_prep_picking_slip_id,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.detail_product_type,
        t.product_id,
        t.hazmat_version_ref_num,
        t.product_barcode,
        t.bin_location_id,
        t.prep_workorder_version_int,
        t.otb_request_id,
        t.transaction_partner_id,
        t.process_type,
        t.prep_workorder_product_type,
        t.product_channel_sku,
        t.ship_type,
        t.routing_instruction_file_file_extension,
        t.routing_instruction_file_file_data,
        t.routing_instruction_file_file_type,
        t.routing_instruction_file_paper_type
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OtbPrepWorkorderPageVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="List_From_Where_List"/>
    </select>

    <select id="filterBuild" resultType="cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorder">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="List_From_Where_List"/>
    </select>

    <select id="filterBuildPickingSlipCount" resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM
        <include refid="List_From_Where_List"/>
    </select>

    <!-- 列表查询 -->
    <sql id="List_From_Where_List">
        otb_prep_workorder t LEFT JOIN otb_workorder t2 ON t.otb_workorder_id = t2.id
        WHERE
        <if test="bl != null">
            EXISTS (SELECT t3.otb_prep_workorder_id FROM otb_prep_workorder_detail t3
            LEFT JOIN bin_location_detail t4 ON t4.product_id = t3.product_id
            LEFT JOIN bin_location bl ON bl.id = t4.bin_location_id
            WHERE t3.otb_prep_workorder_id = t.id
                AND prep_workorder_detail_type = 'None'
            <include refid="Bin_Location_Where_List"/>
            GROUP BY t3.otb_prep_workorder_id
            HAVING COUNT(DISTINCT t3.product_id) = COUNT(DISTINCT t4.product_id)
            )
            AND
        </if>
        t.remove_flag = 0
        <if test="qo != null">
            <include refid="Workorder_Where_List"/>
            <include refid="Base_Where_List"/>
        </if>
        <choose>
            <when test="bl != null">
                ORDER BY t2.request_snapshot_ship_window_end, t.ref_num
            </when>
            <otherwise>
                ORDER BY t.id DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 库位条件 -->
    <sql id="Bin_Location_Where_List">
        <if test="bl.warehouseZoneTypeList != null and bl.warehouseZoneTypeList.size > 0 ">
            AND bl.warehouse_zone_type in
            <foreach collection="bl.warehouseZoneTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.binTypeList != null and bl.binTypeList.size > 0 ">
            AND bl.bin_type in
            <foreach collection="bl.binTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.typeList != null and bl.typeList.size > 0 ">
            AND bl.type in
            <foreach collection="bl.typeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.lrowList != null and bl.lrowList.size > 0 ">
            AND bl.lrow in
            <foreach collection="bl.lrowList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.ldepthList != null and bl.ldepthList.size > 0 ">
            AND bl.ldepth in
            <foreach collection="bl.ldepthList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.llevelList != null and bl.llevelList.size > 0 ">
            AND bl.llevel in
            <foreach collection="bl.llevelList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.lsplitsList != null and bl.lsplitsList.size > 0 ">
            AND bl.lsplit in
            <foreach collection="bl.lsplitsList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <!-- 工单条件 -->
    <sql id="Workorder_Where_List">
        <if test="qo!=null">

        AND  EXISTS (SELECT t2.id FROM otb_workorder t2 WHERE
        t.otb_workorder_id = t2.id
        <if test="qo.requestSnapshotRequestRefNumList != null and qo.requestSnapshotRequestRefNumList.size > 0 ">
           AND  t2.request_snapshot_request_ref_num IN
            <foreach collection="qo.requestSnapshotRequestRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.requestSnapshotShipWindowStartStart != null">
            AND t2.request_snapshot_ship_window_start &gt;= #{qo.requestSnapshotShipWindowStartStart}
        </if>
        <if test="qo.requestSnapshotShipWindowStartEnd != null">
            AND t2.request_snapshot_ship_window_start &lt; #{qo.requestSnapshotShipWindowStartEnd}
        </if>
        <if test="qo.requestSnapshotShipWindowEndStart != null">
            AND t2.request_snapshot_ship_window_end &gt;= #{qo.requestSnapshotShipWindowEndStart}
        </if>
        <if test="qo.requestSnapshotShipWindowEndEnd != null">
            AND t2.request_snapshot_ship_window_end &lt; #{qo.requestSnapshotShipWindowEndEnd}
        </if>
        )
        </if>
    </sql>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbPrepWorkorderStatus != null and qo.otbPrepWorkorderStatus != ''">
            AND t.otb_prep_workorder_status = #{qo.otbPrepWorkorderStatus}
        </if>
        <if test="qo.otbPrepWorkorderStatusList != null and qo.otbPrepWorkorderStatusList.size > 0 ">
            AND t.otb_prep_workorder_status in
            <foreach collection="qo.otbPrepWorkorderStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.idList != null and qo.idList.size > 0 ">
            AND t.id in
            <foreach collection="qo.idList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.productId != null">
            AND t.product_id = #{qo.productId}
        </if>
        <if test="qo.productIdList != null and qo.productIdList.size > 0 ">
            AND t.product_id in
            <foreach collection="qo.productIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.prepWorkorderProductType != null">
            AND t.prep_workorder_product_type = #{qo.prepWorkorderProductType}
        </if>
        <if test="qo.otbPrepPickingSlipId != null">
            AND t.otb_prep_picking_slip_id = #{qo.otbPrepPickingSlipId}
        </if>
        <if test="qo.prepWorkorderProductTypeList != null and qo.prepWorkorderProductTypeList.size > 0 ">
            AND t.prep_workorder_product_type in
            <foreach collection="qo.prepWorkorderProductTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.processType != null and qo.processType != ''">
            AND t.process_type = #{qo.processType}
        </if>
    </sql>

    <sql id="Exists_InnerTable">
        <bind name="qo" value="${InnerQoTableAlias}"/>
        <if test="${InnerQoTableAlias}!=null">
            AND EXISTS(SELECT t.id
            FROM otb_prep_workorder t
            WHERE  ${JoinCondition}
            AND t.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.otb.OtbPrepWorkorderMapper.Base_Where_List">

            </include>
            )
        </if>
    </sql>

</mapper>