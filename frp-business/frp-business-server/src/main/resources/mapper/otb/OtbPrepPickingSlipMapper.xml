<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otb.OtbPrepPickingSlipMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otb.OtbPrepPickingSlip">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="assigned_user_id" property="assignedUserId" />
        <result column="pick_to_station" property="pickToStation" />
        <result column="bin_location_id" property="binLocationId" />
        <result column="otb_prep_picking_slip_status" property="otbPrepPickingSlipStatus" />
        <result column="otb_prep_picking_slip_type" property="otbPrepPickingSlipType" />
        <result column="note" property="note" />
        <result column="print_status" property="printStatus" />
        <result column="qty" property="qty" />
        <result column="allocate_putaway_qty" property="allocatePutawayQty" />
        <result column="putaway_qty" property="putawayQty" />
        <result column="description" property="description" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="ref_num" property="refNum" />
        <result column="detail_product_type" property="detailProductType" />
        <result column="product_id" property="productId" />
        <result column="hazmat_version_ref_num" property="hazmatVersionRefNum" />
        <result column="product_barcode" property="productBarcode" />
        <result column="product_channel_sku" property="productChannelSku" />
        <result column="transaction_partner_id" property="transactionPartnerId" />
        <result column="prep_picking_slip_version_int" property="prepPickingSlipVersionInt" />
        <result column="prep_picking_slip_product_type" property="prepPickingSlipProductType" />
        <result column="process_type" property="processType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.assigned_user_id,
        t.pick_to_station,
        t.bin_location_id,
        t.otb_prep_picking_slip_status,
        t.otb_prep_picking_slip_type,
        t.note,
        t.print_status,
        t.qty,
        t.allocate_putaway_qty,
        t.putaway_qty,
        t.description,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.detail_product_type,
        t.product_id,
        t.hazmat_version_ref_num,
        t.process_type,
        t.product_channel_sku,
        t.transaction_partner_id,
        t.prep_picking_slip_version_int,
        t.prep_picking_slip_product_type,
        t.product_barcode,
        t.ship_type,
        t.routing_instruction_file_file_extension,
        t.routing_instruction_file_file_data,
        t.routing_instruction_file_file_type,
        t.routing_instruction_file_paper_type
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OtbPrepPickingSlipPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            otb_prep_picking_slip t
        WHERE
            t.remove_flag = 0
            AND EXISTS(
                SELECT t2.id FROM otb_prep_workorder t2
                LEFT JOIN otb_workorder t3 ON t3.id = t2.otb_workorder_id
                WHERE t2.otb_prep_picking_slip_id = t.id
                <if test="wk != null">
                    <include refid="Workorder_Where_List"/>
                </if>
            )
            <if test="qo != null">
                <include refid="Base_Where_List"/>
            </if>
            <include refid="Base_Order_By_List"/>
    </select>

    <sql id="Workorder_Where_List">
        <if test="wk.requestSnapshotShipWindowStartStart != null">
            AND t3.request_snapshot_ship_window_start &gt;= #{wk.requestSnapshotShipWindowStartStart}
        </if>
        <if test="wk.requestSnapshotShipWindowStartEnd != null">
            AND t3.request_snapshot_ship_window_start &lt; #{wk.requestSnapshotShipWindowStartEnd}
        </if>
        <if test="wk.requestSnapshotShipWindowEndStart != null">
            AND t3.request_snapshot_ship_window_end &gt;= #{wk.requestSnapshotShipWindowEndStart}
        </if>
        <if test="wk.requestSnapshotShipWindowEndEnd != null">
            AND t3.request_snapshot_ship_window_end &lt; #{wk.requestSnapshotShipWindowEndEnd}
        </if>
        <if test="wk.requestSnapshotRequestRefNumList != null and wk.requestSnapshotRequestRefNumList.size > 0 ">
            AND t3.request_snapshot_request_ref_num in
            <foreach collection="wk.requestSnapshotRequestRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.id DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbPrepPickingSlipStatus != null and qo.otbPrepPickingSlipStatus != ''">
            AND t.otb_prep_picking_slip_status = #{qo.otbPrepPickingSlipStatus}
        </if>
        <if test="qo.otbPrepPickingSlipStatusList != null and qo.otbPrepPickingSlipStatusList.size > 0 ">
            AND t.otb_prep_picking_slip_status in
            <foreach collection="qo.otbPrepPickingSlipStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbPrepPickingSlipType != null and qo.otbPrepPickingSlipType != ''">
            AND t.otb_prep_picking_slip_type = #{qo.otbPrepPickingSlipType}
        </if>
        <if test="qo.otbPrepPickingSlipTypeList != null and qo.otbPrepPickingSlipTypeList.size > 0 ">
            AND t.otb_prep_picking_slip_type in
            <foreach collection="qo.otbPrepPickingSlipTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.printStatus != null and qo.printStatus != ''">
            AND t.print_status = #{qo.printStatus}
        </if>
        <if test="qo.printStatusList != null and qo.printStatusList.size > 0 ">
            AND t.print_status in
            <foreach collection="qo.printStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.productId != null">
            AND t.product_id = #{qo.productId}
        </if>
        <if test="qo.productIdList != null and qo.productIdList.size > 0 ">
            AND t.product_id in
            <foreach collection="qo.productIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.prepPickingSlipProductType != null">
            AND t.prep_picking_slip_product_type = #{qo.prepPickingSlipProductType}
        </if>
        <if test="qo.prepPickingSlipProductTypeList != null and qo.prepPickingSlipProductTypeList.size > 0 ">
            AND t.prep_picking_slip_product_type in
            <foreach collection="qo.prepPickingSlipProductTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.processType != null and qo.processType != ''">
            AND t.process_type = #{qo.processType}
        </if>
    </sql>


    <sql id="Exists_InnerTable">
        <bind name="qo" value="${InnerQoTableAlias}"/>

        <if test="${InnerQoTableAlias}!=null">
            AND EXISTS(SELECT t.id
            FROM otb_prep_picking_slip t
            WHERE  ${JoinCondition}
            AND t.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.otb.OtbPrepPickingSlipMapper.Base_Where_List">

            </include>
            )
        </if>
    </sql>
</mapper>