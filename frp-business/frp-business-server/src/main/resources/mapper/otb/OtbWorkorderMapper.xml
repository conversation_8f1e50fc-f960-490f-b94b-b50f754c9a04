<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otb.OtbWorkorderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otb.OtbWorkorder">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remove_flag" property="removeFlag"/>
        <result column="request_snapshot_ref_num" property="requestSnapshotRefNum"/>
        <result column="request_snapshot_note" property="requestSnapshotNote"/>
        <result column="request_snapshot_channel" property="requestSnapshotChannel"/>
        <result column="request_snapshot_transaction_partner_id" property="requestSnapshotTransactionPartnerId"/>
        <result column="request_snapshot_request_ref_num" property="requestSnapshotRequestRefNum"/>
        <result column="otb_workorder_status" property="otbWorkorderStatus"/>
        <result column="otb_request_id" property="otbRequestId"/>
        <result column="version" property="version"/>
        <result column="deleted_note" property="deletedNote"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="ref_num" property="refNum"/>
        <result column="note" property="note"/>
        <result column="otb_request_shipment_status" property="otbRequestShipmentStatus"/>
        <result column="workorder_prep_status" property="workorderPrepStatus"/>
        <result column="request_snapshot_ship_window_start" property="requestSnapshotShipWindowStart"/>
        <result column="request_snapshot_order_num" property="requestSnapshotOrderNum"/>
        <result column="request_snapshot_ship_window_end" property="requestSnapshotShipWindowEnd"/>
        <result column="otb_workorder_type" property="otbWorkorderType"/>
        <result column="workorder_product_type" property="workorderProductType"/>
        <result column="otb_picking_slip_id" property="otbPickingSlipId"/>
        <result column="process_type" property="processType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.request_snapshot_ref_num,
        t.request_snapshot_note,
        t.request_snapshot_channel,
        t.request_snapshot_transaction_partner_id,
        t.request_snapshot_request_ref_num,
        t.otb_workorder_status,
        t.otb_request_id,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.detail_product_type,
        t.note,
        t.otb_request_shipment_status,
        t.workorder_prep_status,
        t.request_snapshot_ship_window_start,
        t.request_snapshot_order_num,
        t.request_snapshot_ship_window_end,
        t.workorder_product_type,
        t.otb_picking_slip_id,
        t.process_type,
        t.otb_workorder_type,
        t.ship_type,
        t.routing_instruction_file_file_extension,
        t.routing_instruction_file_file_data,
        t.routing_instruction_file_file_type,
        t.routing_instruction_file_paper_type
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OtbWorkorderPageVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="List_From_Where_List"/>
    </select>
    <select id="inventoryValid" resultType="java.lang.Long">
        SELECT
        t1.id
        FROM
        otb_workorder t1
        WHERE
        t1.remove_flag=0 and
        <if test="bl!=null">
            EXISTS (
            SELECT
            t2.otb_workorder_id
            FROM
            otb_workorder_detail t2
            LEFT JOIN bin_location_detail t3 ON t2.product_id = t3.product_id
            LEFT JOIN bin_location t4 ON t3.bin_location_id = t4.id
            WHERE
            1 = 1
            AND t2.otb_workorder_id = t1.id
            AND t4.default_flag = 1
            <!--仓库分区-->
            <if test="bl.warehouseZoneTypeList != null and bl.warehouseZoneTypeList.size > 0 ">
                AND t4.warehouse_zone_type in
                <foreach collection="bl.warehouseZoneTypeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <!--库位类型-->
            <if test="bl.binTypeList != null and bl.binTypeList.size > 0 ">
                AND t4.bin_type in
                <foreach collection="bl.binTypeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <!--库位行-->
            <if test="bl.lrowList!=null and bl.lrowList.size()>0">
                AND t4.lrow in
                <foreach collection="bl.lrowList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <!--库位列-->
            <if test="bl.ldepthList!=null and bl.ldepthList.size()>0">
                AND t4.ldepth in
                <foreach collection="bl.ldepthList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <!--库位层-->
            <if test="bl.llevelList!=null and bl.llevelList.size()>0">
                AND t4.llevel in
                <foreach collection="bl.llevelList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <!--库位格-->
            <if test="bl.lsplitsList!=null and bl.lsplitsList.size()>0">
                AND t4.lsplits in
                <foreach collection="bl.lsplitsList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            GROUP BY
            t2.otb_workorder_id
            HAVING
            COUNT(t2.id) >= COUNT(t3.product_id))
        </if>
        <if test="qo!=null">
            <include refid="Base_Where_List"/>
            order by t1.request_snapshot_ship_window_end,t1.ref_num
        </if>
    </select>

    <select id="countByQuery" resultType="java.lang.Integer">
        SELECT
        count(t.id)
        FROM
        otb_workorder t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
    </select>

    <select id="filterBuildPickingSlipCount" resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM
        <include refid="List_From_Where_List"/>
    </select>



    <select id="getIdByQuery" resultType="java.lang.Long">
        SELECT
        t.id
        FROM
        otb_workorder t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
        <include refid="Base_Order_By_List"/>
    </select>

    <select id="filterBuild" resultType="cn.need.cloud.biz.model.entity.otb.OtbWorkorder">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="List_From_Where_List"/>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
        COUNT(*) AS countNum,
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}") AS ${item.name}
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
        FROM
        otb_workorder t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
        GROUP BY
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}")
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
    </select>

    <select id="existUnfinishedOrder" resultType="java.lang.Long">
        select count(t.id)
        from otb_workorder_bin_location owbl
        left join otb_workorder t on t.id=owbl.otb_workorder_id
        <where>
            t.remove_flag = 0
            and owbl.bin_location_id = #{binLocationId}
            and t.otb_workorder_status in
            <foreach collection="workOrderStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </where>

    </select>


    <sql id="List_From_Where_List">
        otb_workorder t
        WHERE
        <if test="bl != null">
            EXISTS (SELECT t2.otb_workorder_id FROM otb_workorder_detail t2
            LEFT JOIN bin_location_detail t3 ON t3.product_id = t2.product_id
            LEFT JOIN bin_location bl ON bl.id = t3.bin_location_id
            WHERE t2.otb_workorder_id = t.id
            <include refid="Bin_Location_Where_List"/>
            GROUP BY t2.otb_workorder_id
            HAVING COUNT(DISTINCT t2.product_id) = COUNT(DISTINCT t3.product_id)
            )
            AND
        </if>
        t.remove_flag = 0
        <if test="qo != null">
            <include refid="Base_Where_List"/>
        </if>
        <if test="ps != null">
            AND t.id IN (
                SELECT ps.otb_workorder_id FROM otb_picking_slip ps
                WHERE
                    ps.remove_flag = 0
                    <if test="ps.refNum != null and ps.refNum != ''">
                        AND ps.ref_num = #{ps.refNum}
                    </if>
                    <if test="ps.refNumList != null and ps.refNumList.size > 0 ">
                        AND ps.ref_num in
                        <foreach collection="ps.refNumList" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
            )
        </if>
        <choose>
            <when test="bl != null">
                ORDER BY t.request_snapshot_ship_window_end, t.ref_num
            </when>
            <otherwise>
                ORDER BY t.id DESC
            </otherwise>
        </choose>
    </sql>

    <sql id="Bin_Location_Where_List">
        <if test="bl.warehouseZoneTypeList != null and bl.warehouseZoneTypeList.size > 0 ">
            AND bl.warehouse_zone_type in
            <foreach collection="bl.warehouseZoneTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.binTypeList != null and bl.binTypeList.size > 0 ">
            AND bl.bin_type in
            <foreach collection="bl.binTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.typeList != null and bl.typeList.size > 0 ">
            AND bl.type in
            <foreach collection="bl.typeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.lrowList != null and bl.lrowList.size > 0 ">
            AND bl.lrow in
            <foreach collection="bl.lrowList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.ldepthList != null and bl.ldepthList.size > 0 ">
            AND bl.ldepth in
            <foreach collection="bl.ldepthList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.llevelList != null and bl.llevelList.size > 0 ">
            AND bl.llevel in
            <foreach collection="bl.llevelList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.lsplitsList != null and bl.lsplitsList.size > 0 ">
            AND bl.lsplit in
            <foreach collection="bl.lsplitsList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.request_snapshot_ship_window_end,t.ref_num DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.workorderProductTypeList != null and qo.workorderProductTypeList.size > 0 ">
            AND t.workorder_product_type in
            <foreach collection="qo.workorderProductTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!--出库工单id-->
        <if test="qo.idList != null and qo.idList.size > 0 ">
            AND t.id in
            <foreach collection="qo.idList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.createTimeStart != null">
            AND t.create_time &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.requestSnapshotRefNum != null and qo.requestSnapshotRefNum != ''">
            AND t.request_snapshot_ref_num = #{qo.requestSnapshotRefNum}
        </if>
        <if test="qo.requestSnapshotRefNumList != null and qo.requestSnapshotRefNumList.size > 0 ">
            AND t.request_snapshot_ref_num in
            <foreach collection="qo.requestSnapshotRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.requestSnapshotChannel != null and qo.requestSnapshotChannel != ''">
            AND t.request_snapshot_channel = #{qo.requestSnapshotChannel}
        </if>
        <if test="qo.requestSnapshotChannelList != null and qo.requestSnapshotChannelList.size > 0 ">
            AND t.request_snapshot_channel in
            <foreach collection="qo.requestSnapshotChannelList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="qo.otbWorkorderStatus != null and qo.otbWorkorderStatus != ''">
            AND t.otb_workorder_status = #{qo.otbWorkorderStatus}
        </if>
        <if test="qo.otbWorkorderStatusList != null and qo.otbWorkorderStatusList.size > 0 ">
            AND t.otb_workorder_status in
            <foreach collection="qo.otbWorkorderStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.idList != null and qo.idList.size > 0 ">
            AND t.id in
            <foreach collection="qo.idList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbRequestShipmentStatus != null and qo.otbRequestShipmentStatus != ''">
            AND t.otb_request_shipment_status = #{qo.otbRequestShipmentStatus}
        </if>
        <if test="qo.otbRequestShipmentStatusList != null and qo.otbRequestShipmentStatusList.size > 0 ">
            AND t.otb_request_shipment_status in
            <foreach collection="qo.otbRequestShipmentStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.workorderPrepStatus != null and qo.workorderPrepStatus != ''">
            AND t.workorder_prep_status = #{qo.workorderPrepStatus}
        </if>
        <if test="qo.workorderPrepStatusList != null and qo.workorderPrepStatusList.size > 0 ">
            AND t.workorder_prep_status in
            <foreach collection="qo.workorderPrepStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.requestSnapshotShipWindowStartStart != null">
            AND t.request_snapshot_ship_window_start &gt;= #{qo.requestSnapshotShipWindowStartStart}
        </if>
        <if test="qo.requestSnapshotShipWindowStartEnd != null">
            AND t.request_snapshot_ship_window_start &lt; #{qo.requestSnapshotShipWindowStartEnd}
        </if>
        <if test="qo.requestSnapshotShipWindowEndStart != null">
            AND t.request_snapshot_ship_window_end &gt;= #{qo.requestSnapshotShipWindowEndStart}
        </if>
        <if test="qo.requestSnapshotShipWindowEndEnd != null">
            AND t.request_snapshot_ship_window_end &lt; #{qo.requestSnapshotShipWindowEndEnd}
        </if>
        <if test="qo.otbWorkorderType != null and qo.otbWorkorderType != ''">
            AND t.otb_workorder_type = #{qo.otbWorkorderType}
        </if>
        <if test="qo.otbWorkorderTypeList != null and qo.otbWorkorderTypeList.size > 0 ">
            AND t.otb_workorder_type in
            <foreach collection="qo.otbWorkorderTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.requestSnapshotRequestRefNumList != null and qo.requestSnapshotRequestRefNumList.size > 0 ">
            AND t.request_snapshot_request_ref_num in
            <foreach collection="qo.requestSnapshotRequestRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.requestSnapshotShipWindowStart != null">
            AND t.request_snapshot_ship_window_start = #{qo.requestSnapshotShipWindowStart}
        </if>
        <if test="qo.requestSnapshotShipWindowEnd != null">
            AND t.request_snapshot_ship_window_end = #{qo.requestSnapshotShipWindowEnd}
        </if>
        <if test="qo.otbRequestId != null and qo.otbRequestId != ''">
            AND t.otb_request_id = #{qo.otbRequestId}
        </if>
        <if test="qo.processType != null and qo.processType != ''">
            AND t.process_type = #{qo.processType}
        </if>
    </sql>

    <sql id="Exists_InnerTable">
        <bind name="qo" value="${InnerQoTableAlias}"/>
        <if test="${InnerQoTableAlias}!=null">
            AND EXISTS(SELECT t.id
            FROM otb_workorder t
            WHERE  ${JoinCondition}
            AND t.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.otb.OtbWorkorderMapper.Base_Where_List">

            </include>
            )
        </if>
    </sql>

</mapper>