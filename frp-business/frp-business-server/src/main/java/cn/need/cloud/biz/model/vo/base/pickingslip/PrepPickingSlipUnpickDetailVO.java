package cn.need.cloud.biz.model.vo.base.pickingslip;

import cn.need.framework.common.core.lang.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTC预拣货单详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "拣货单 Unpick详情 vo对象")
public class PrepPickingSlipUnpickDetailVO extends BasePickingSlipUnpickDetailVO {


    @Schema(description = "prepWorkorderBinLocation主键")
    private Long prepWorkorderBinLocationId;
    /**
     * 发货到c端工单详情id
     */
    @Schema(description = "Prep工单详情")
    @JsonIgnore
    private Long prepWorkorderDetailId;

    /**
     * 发货到c端工单id
     */
    @Schema(description = "Prep工单")
    private Long prepWorkorderId;

    /**
     * 发货到c端拣货id
     */
    @Schema(description = "发货到c端拣货id")
    @JsonIgnore
    private Long prepPickingSlipId;


    @Schema(description = "拣货单详情id")
    @JsonIgnore
    private Long prepPickingSlipDetailId;

    /**
     * 发货 数量
     */
    @Schema(description = "上架 数量")
    private Integer putawayQty;

    public Integer getPutawayQty() {
        return ObjectUtil.nullToDefault(putawayQty, 0);
    }

    @Schema(description = "可 Rollback 数量")
    public Integer getCanRollbackQty() {
        return this.getPickedQty() - this.getPutawayQty();
    }

    @Override
    public Long getUnpickId() {
        return this.getPrepWorkorderBinLocationId();
    }
}