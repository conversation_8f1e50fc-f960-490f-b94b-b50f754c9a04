package cn.need.cloud.biz.model.query.base.putawayslip;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * OTC上架单 Query对象
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NormalPutawaySlipQuery extends PutawaySlipQuery {

    // region pickingSlipId

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id")
    private Long pickingSlipId;

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id 集合")
    @Condition(value = Keyword.IN, fields = {"pickingSlipId"})
    private List<Long> pickingSlipIdList;

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"pickingSlipId"})
    private List<Long> pickingSlipIdNiList;

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id值类型集合")
    private List<String> pickingSlipIdValueTypeList;

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id 大于")
    @Condition(value = Keyword.GT, fields = {"pickingSlipId"})
    private Long pickingSlipIdGt;

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id 大于等于")
    @Condition(value = Keyword.GE, fields = {"pickingSlipId"})
    private Long pickingSlipIdGe;

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id 小于")
    @Condition(value = Keyword.LT, fields = {"pickingSlipId"})
    private Long pickingSlipIdLt;

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id 小于等于")
    @Condition(value = Keyword.LE, fields = {"pickingSlipId"})
    private Long pickingSlipIdLe;

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"pickingSlipId"})
    private Long pickingSlipIdLike;

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"pickingSlipId"})
    private Long pickingSlipIdLikeLeft;

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"pickingSlipId"})
    private Long pickingSlipIdLikeRight;

    // endregion pickingSlipId

    // region workorderId

    /**
     * 工单id
     */
    @Schema(description = "工单id")
    private Long workorderId;

    /**
     * 工单id
     */
    @Schema(description = "工单id 集合")
    @Condition(value = Keyword.IN, fields = {"workorderId"})
    private List<Long> workorderIdList;

    /**
     * 工单id
     */
    @Schema(description = "工单id 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"workorderId"})
    private List<Long> workorderIdNiList;

    /**
     * 工单id
     */
    @Schema(description = "工单id值类型集合")
    private List<String> workorderIdValueTypeList;

    /**
     * 工单id
     */
    @Schema(description = "工单id 大于")
    @Condition(value = Keyword.GT, fields = {"workorderId"})
    private Long workorderIdGt;

    /**
     * 工单id
     */
    @Schema(description = "工单id 大于等于")
    @Condition(value = Keyword.GE, fields = {"workorderId"})
    private Long workorderIdGe;

    /**
     * 工单id
     */
    @Schema(description = "工单id 小于")
    @Condition(value = Keyword.LT, fields = {"workorderId"})
    private Long workorderIdLt;

    /**
     * 工单id
     */
    @Schema(description = "工单id 小于等于")
    @Condition(value = Keyword.LE, fields = {"workorderId"})
    private Long workorderIdLe;

    /**
     * 工单id
     */
    @Schema(description = "工单id 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"workorderId"})
    private Long workorderIdLike;

    /**
     * 工单id
     */
    @Schema(description = "工单id 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"workorderId"})
    private Long workorderIdLikeLeft;

    /**
     * 工单id
     */
    @Schema(description = "工单id 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"workorderId"})
    private Long workorderIdLikeRight;

    // endregion workorderId
}