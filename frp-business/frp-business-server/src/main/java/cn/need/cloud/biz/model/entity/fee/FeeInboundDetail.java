package cn.need.cloud.biz.model.entity.fee;

import cn.need.cloud.biz.model.entity.base.RefNumModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;


/**
 * <p>
 * 费用详情inbound
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("fee_inbound_detail")
public class FeeInboundDetail extends RefNumModel {


    @Serial
    private static final long serialVersionUID = 3595568126310668264L;
    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 费用
     */
    @TableField("fee")
    private BigDecimal fee;

    /**
     * 费用配置明细id
     */
    @TableField("fee_config_detail_id")
    private Long feeConfigDetailId;

    /**
     * 费用配置id
     */
    @TableField("fee_config_id")
    private Long feeConfigId;

    /**
     * header表id
     */
    @TableField("header_id")
    private Long headerId;

    /**
     * 行序号
     */
    @TableField("line_num")
    private Integer lineNum;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 唯一标识码
     */
    @TableField("ref_num")
    private String refNum;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

}
