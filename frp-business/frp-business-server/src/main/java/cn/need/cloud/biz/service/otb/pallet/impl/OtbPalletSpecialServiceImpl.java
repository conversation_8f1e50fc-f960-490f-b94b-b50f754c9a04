package cn.need.cloud.biz.service.otb.pallet.impl;

import cn.need.cloud.biz.client.constant.ErrorConstant;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.ProcessType;
import cn.need.cloud.biz.client.constant.enums.otb.OtbPackageStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbPalletStatusEnum;
import cn.need.cloud.biz.model.bo.base.WorkorderProcessBO;
import cn.need.cloud.biz.model.entity.otb.OtbPallet;
import cn.need.cloud.biz.model.entity.otb.OtbShipment;
import cn.need.cloud.biz.model.param.otb.update.pallet.OtbPalletBatchRollbackUpdateParam;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbPackageAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbPalletAuditLogHelper;
import cn.need.cloud.biz.service.otb.pallet.OtbPalletService;
import cn.need.cloud.biz.service.otb.pallet.OtbPalletSpecialService;
import cn.need.cloud.biz.service.otb.pkg.OtbPackageService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderService;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.model.IdModel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * OtbPalletSpecialServiceImpl
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Service
@Slf4j
@AllArgsConstructor
public class OtbPalletSpecialServiceImpl implements OtbPalletSpecialService {

    private final OtbPalletService otbPalletService;
    private final OtbWorkorderService otbWorkorderService;
    private final OtbPackageService otbPackageService;

    @Override
    public boolean batchRollback(OtbPalletBatchRollbackUpdateParam param) {
        var cancelList = otbPalletService.listByIds(param.getIdList());

        Validate.notEmpty(cancelList, "ShipmentId: {} is not exist", param.getIdList());

        cancelList.forEach(obj -> {
            // 流程校验
            ProcessType.checkAbnormal(obj.getProcessType(), obj.refNumLog(), "batchRollback");

            // 校验
            Validate.isTrue(OtbPalletStatusEnum.canRollbackStatus().contains(obj.getOtbPalletStatus()),
                    ErrorConstant.STATUS_ERROR_FORMAT,
                    obj.refNumLog(), "batchRollback",
                    OtbPalletStatusEnum.canRollbackStatus(), obj.getOtbPalletStatus()
            );

            // 更新状态
            obj.setOtbPalletStatus(OtbPalletStatusEnum.CANCELLED.getStatus());
        });

        // 更新
        Validate.isTrue(otbPalletService.updateBatch(cancelList) == cancelList.size(), "Update Pallet status failed");

        var workorderIds = StreamUtils.distinctMap(cancelList, OtbPallet::getOtbWorkorderId);
        var workorderMap = StreamUtils.toMap(otbWorkorderService.listByIds(workorderIds), IdModel::getId);

        // 日志
        cancelList.forEach(cancel -> {
            // 包裹 记录日志
            OtbPalletAuditLogHelper.recordLog(cancel, cancel.getOtbPalletStatus(),
                    BaseTypeLogEnum.PROCESS_TYPE.getType(),
                    param.getNote(),
                    StringUtil.format("Rollback By {}", workorderMap.get(cancel.getOtbWorkorderId()).refNumLog())
            );
        });

        // 更新Package 状态、palletId
        var packageList = otbPackageService.listByPalletIdList(param.getIdList());
        packageList.forEach(obj -> obj.setOtbPackageStatus(OtbPackageStatusEnum.PACKED.getCode()));
        otbPackageService.updateBatchWithNull(packageList);

        // 记录日志
        OtbPackageAuditLogHelper.recordLog(packageList);

        return true;
    }

    @Override
    public void processTriggering(WorkorderProcessBO process) {
        List<Long> workorderIds = process.getIdList();

        if (ObjectUtil.isEmpty(workorderIds)) {
            return;
        }
        List<OtbPallet> palletList = otbPalletService.listByWorkorderIds(workorderIds);

        String type = process.getProcessType().getType();
        palletList.forEach(obj -> obj.setProcessType(type));

        Validate.isTrue(otbPalletService.updateBatch(palletList) == palletList.size(),
                "Update Pallet status {} fail ", type
        );

        // 记录日志
        OtbPalletAuditLogHelper.recordLog(palletList, type, BaseTypeLogEnum.PROCESS_TYPE.getType(), process.getNote(), null);
    }

    @Override
    public void rollbackByShipment(List<OtbShipment> cancelList) {
        var shipmentIds = StreamUtils.distinctMap(cancelList, IdModel::getId);
        var palletList = otbPalletService.listByShipmentId(shipmentIds);
        palletList.forEach(obj -> obj.setOtbPalletStatus(OtbPalletStatusEnum.PALLETED.getStatus()));

        Validate.isTrue(otbPalletService.updateBatch(palletList) == palletList.size(),
                "Update Package rollback is fail"
        );

        OtbPalletAuditLogHelper.recordLog(palletList);
    }
}
