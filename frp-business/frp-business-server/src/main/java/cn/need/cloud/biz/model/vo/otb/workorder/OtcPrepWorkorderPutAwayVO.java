package cn.need.cloud.biz.model.vo.otb.workorder;

import cn.need.cloud.biz.model.vo.base.BasePutAwayVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/***
 * 工单上架详情
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OtcPrepWorkorderPutAwayVO extends BasePutAwayVO {

    /**
     * 工单详情
     */
    private Long otcWorkorderDetailId;

    /**
     * 工单
     */
    private Long otcWorkorderId;

    /**
     * 总数量
     */
    private Integer qty;

}
