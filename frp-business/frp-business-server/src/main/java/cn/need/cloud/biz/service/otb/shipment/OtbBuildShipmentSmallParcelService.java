package cn.need.cloud.biz.service.otb.shipment;

import cn.need.cloud.biz.model.param.otb.create.shipment.OtbShipmentCreateParam;
import cn.need.cloud.biz.model.vo.otb.shipment.OtbShipmentVO;

/**
 * <p>
 * OTB小件装运build service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
public interface OtbBuildShipmentSmallParcelService {
    /**
     * 构建小件发货单
     *
     * @param param 构建参数
     * @return 打印信息
     */
    OtbShipmentVO buildSmallParcel(OtbShipmentCreateParam param);

}
