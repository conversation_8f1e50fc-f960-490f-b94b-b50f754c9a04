package cn.need.cloud.biz.model.param.otc.create.pickingslip.prep;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;


/**
 * OTC预提货单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC预提货单 vo对象")
public class OtcPrepPickingSlipCreateParam implements Serializable {


    @Serial
    private static final long serialVersionUID = 4994118681801693417L;
    /**
     * 分配人
     */
    @Schema(description = "分配人")
    private Long assignedUserId;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * 预拣货状态
     */
    @Schema(description = "预拣货状态")
    private String prepPickingSlipStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;


    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    @Size(max = 64, message = "hazmatVersionRefNum cannot exceed 64 characters")
    private String hazmatVersionRefNum;

    /**
     * 已经分配的上架数量
     */
    @Schema(description = "已经分配的上架数量")
    private Integer allocatePutawayQty;

    /**
     * 上架数量
     */
    @Schema(description = "上架数量")
    private Integer putawayQty;

    /**
     * 订单类型
     */
    @Schema(description = "订单类型")
    private String orderType;

    /**
     * 拣货id
     */
    @Schema(description = "拣货id")
    private Long otcPickingSlipId;

    /**
     * 拣货到那里
     */
    @Schema(description = "拣货到那里")
    private String pickToStation;

    /**
     * 是否有特定运输要求
     */
    @Schema(description = "是否有特定运输要求")
    private Boolean hasCusShipRequire;

    /**
     * 现场包装标志
     */
    @Schema(description = "现场包装标志")
    private Boolean onSitePackFlag;

    /**
     * 预拣货类型
     */
    @Schema(description = "预拣货类型")
    private String prepPickingSlipType;

    /**
     * 锁定前
     */
    @Schema(description = "锁定前")
    private String lockedBefore;

}