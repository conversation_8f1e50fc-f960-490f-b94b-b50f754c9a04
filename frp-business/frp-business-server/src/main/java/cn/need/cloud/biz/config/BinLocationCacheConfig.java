package cn.need.cloud.biz.config;


import cn.need.cloud.biz.cache.BinLocationCacheRepertory;
import cn.need.cloud.biz.cache.BinLocationCacheService;
import cn.need.cloud.biz.cache.impl.RedisBinLocationCacheRepertoryImpl;
import cn.need.cloud.biz.cache.impl.RedisBinLocationCacheServiceImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;


/**
 * 日志缓存配置
 *
 * <AUTHOR>
 */
@Configuration
public class BinLocationCacheConfig {

    /**
     * 注入日志配置缓存操作工具
     */
    @Bean
    @SuppressWarnings({"rawtypes", "unchecked"})
    public BinLocationCacheService binLocationCacheService(RedisTemplate redisTemplate) {
        return new RedisBinLocationCacheServiceImpl(redisTemplate);
    }

    /**
     * 注入日志配置缓存操作工具
     */
    @Bean
    @SuppressWarnings({"rawtypes", "unchecked"})
    public BinLocationCacheRepertory binLocationCacheRepertory(RedisTemplate redisTemplate) {
        return new RedisBinLocationCacheRepertoryImpl(redisTemplate);
    }
}
