package cn.need.cloud.biz.service.base.putawayslip;

import cn.need.cloud.biz.client.constant.enums.base.PutAwaySlipStatus;
import cn.need.cloud.biz.model.bo.base.pickingslip.PickingSlipUnpickBO;
import cn.need.cloud.biz.model.entity.base.RefNumModel;
import cn.need.cloud.biz.model.entity.base.putawayslip.PrepPutawaySlipModel;
import cn.need.cloud.biz.model.entity.base.putawayslip.PutawaySlipDetailModel;
import cn.need.cloud.biz.model.vo.base.pickingslip.BasePickingSlipUnpickDetailVO;
import cn.need.framework.common.core.lang.ObjectUtil;

import java.util.Collections;
import java.util.List;
import java.util.function.BiConsumer;

/**
 * <p>
 * OTC上架单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
public interface PrepPutawaySlipService<T extends PrepPutawaySlipModel> extends PutawaySlipService<T> {

    @Override
    default <D extends PutawaySlipDetailModel, PSUD extends BasePickingSlipUnpickDetailVO,
            R extends PickingSlipUnpickBO<T, D, PSUD>> void unpick(R query, BiConsumer<T, List<D>> consumer) {

        BiConsumer<T, List<D>> headerConsumer = (putawaySlip, details) -> {
            RefNumModel pickingSlip = query.getPickingSlip();
            RefNumModel workorder = query.getWorkorder();

            putawaySlip.setPrepPickingSlipId(pickingSlip.getId());
            putawaySlip.setPrepWorkorderId(workorder.getId());

            consumer.accept(putawaySlip, details);
        };

        PutawaySlipService.super.unpick(query, headerConsumer);
    }

    /**
     * 根据工单返回可用的 上架单
     *
     * @param workorderIds 工单
     * @return /
     */
    @Override
    default List<T> listAvailableByWorkorderIds(List<Long> workorderIds) {
        if (ObjectUtil.isEmpty(workorderIds)) {
            return Collections.emptyList();
        }
        return this.query()
                .in("prep_workorder_id", workorderIds)
                .in("putaway_slip_status", PutAwaySlipStatus.NEW.getStatus(), PutAwaySlipStatus.PROCESSING.getStatus())
                .list();
    }
}