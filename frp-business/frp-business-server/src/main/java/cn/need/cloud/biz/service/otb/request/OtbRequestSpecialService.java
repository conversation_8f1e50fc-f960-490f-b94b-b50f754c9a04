package cn.need.cloud.biz.service.otb.request;

import cn.need.cloud.biz.model.param.otb.update.request.OtbRequestCancelParam;

import java.util.List;

/**
 * <p>
 * OTB请求 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
public interface OtbRequestSpecialService {

    /**
     * 取消
     *
     * @param param 包含取消操作所需参数的对象，如笔记ID、操作用户等
     * @return 返回表示取消操作结果的整数，具体含义根据业务需求定义，例如：0表示成功，其他值表示不同的错误情况
     */
    Integer cancel(OtbRequestCancelParam param);

    /**
     * 完成取消
     *
     * @param requestIdList 包含需要取消的请求ID列表
     */
    void finishCancel(List<Long> requestIdList);

    /**
     * 回滚Shipment
     *
     * @param requestIds /
     */
    void rollbackByShipment(List<Long> requestIds);
}
