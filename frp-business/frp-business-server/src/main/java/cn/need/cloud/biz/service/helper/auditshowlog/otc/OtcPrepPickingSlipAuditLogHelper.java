package cn.need.cloud.biz.service.helper.auditshowlog.otc;

import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPickingSlip;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.biz.util.log.AuditLogUtil;

import java.util.List;

/**
 * Prep拣货单日志辅助类
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
public class OtcPrepPickingSlipAuditLogHelper {

    /**
     * 记录日志
     *
     * @param pickingSlipList Prep拣货单
     * @param status          状态
     * @param description     描述
     * @param note            Note
     */
    public static void recordLog(List<OtcPrepPickingSlip> pickingSlipList, String status, String description, String note) {
        pickingSlipList.forEach(workorder -> recordLog(workorder, status, description, note));
    }

    /**
     * 记录日志
     *
     * @param pickingSlipList 拣货单
     * @param description     描述
     * @param note            Note
     */
    public static void recordLog(List<OtcPrepPickingSlip> pickingSlipList, String description, String note) {
        pickingSlipList.forEach(workorder -> recordLog(workorder, description, note));
    }

    /**
     * 记录日志
     *
     * @param pickingSlipList Prep拣货单
     * @param status          状态
     * @param description     描述
     * @param note            Note
     */
    public static void recordLog(List<OtcPrepPickingSlip> pickingSlipList, String status, String description, String note, String type) {
        pickingSlipList.forEach(workorder -> recordLog(workorder, status, description, note, type));
    }

    /**
     * 记录日志
     *
     * @param pickingSlipList Prep拣货单
     */
    public static void recordLog(List<OtcPrepPickingSlip> pickingSlipList) {
        pickingSlipList.forEach(workorder -> recordLog(workorder, workorder.getPrepPickingSlipStatus(), null, null));
    }

    /**
     * 记录日志
     *
     * @param pickingSlip Prep拣货单
     */
    public static void recordLog(OtcPrepPickingSlip pickingSlip) {
        recordLog(pickingSlip, pickingSlip.getPrepPickingSlipStatus(), null, null);
    }

    /**
     * 记录日志
     *
     * @param pickingSlip Prep拣货单
     * @param description 描述
     * @param note        Note
     */
    public static void recordLog(OtcPrepPickingSlip pickingSlip, String description, String note) {
        recordLog(pickingSlip, pickingSlip.getPrepPickingSlipStatus(), description, note);
    }

    /**
     * 记录日志
     *
     * @param pickingSlip Prep拣货单
     * @param status      状态
     * @param description 描述
     * @param note        Note
     */
    public static void recordLog(OtcPrepPickingSlip pickingSlip, String status, String description, String note) {
        recordLog(pickingSlip, status, description, note, BaseTypeLogEnum.STATUS.getType());
    }

    /**
     * 记录日志
     *
     * @param pickingSlip Prep拣货单
     * @param status      状态
     * @param description 描述
     * @param note        Note
     */
    public static void recordLog(OtcPrepPickingSlip pickingSlip, String status, String description, String note, String type) {
        AuditShowLog showLog = AuditLogUtil.commonLog(pickingSlip)
                .with(AuditShowLog::setEvent, status)
                .with(AuditShowLog::setDescription, description)
                .with(AuditShowLog::setNote, note)
                .with(AuditShowLog::setType, type)
                .build();
        AuditLogHolder.record(showLog);
    }
}
