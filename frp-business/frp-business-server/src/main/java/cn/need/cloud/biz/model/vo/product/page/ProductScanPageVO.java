package cn.need.cloud.biz.model.vo.product.page;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 产品扫描 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "产品扫描 vo对象")
public class ProductScanPageVO implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 扫描编号
     */
    @Schema(description = "扫描编号")
    private String scanNum;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    // /**
    //  * 关联id
    //  */
    // @Schema(description = "关联id")
    // private Long refId;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id")
    private Long transactionPartnerId;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String productType;

    /**
     * 产品属性
     */
    @Schema(description = "产品属性")
    private String productAttribute;

    /**
     * 多箱行号
     */
    @Schema(description = "多箱行号")
    private Integer lineNum;


    /**
     * 是否是系统默认
     */
    @Schema(description = "是否是系统默认")
    private boolean defaultFlag;

}