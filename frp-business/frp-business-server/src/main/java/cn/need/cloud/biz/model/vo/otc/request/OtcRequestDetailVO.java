package cn.need.cloud.biz.model.vo.otc.request;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * OTC请求详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTC请求详情 vo对象")
public class OtcRequestDetailVO extends BaseSuperVO {


    @Serial
    private static final long serialVersionUID = 2526373831593523019L;
    /**
     * OTC请求ID
     */
    @Schema(description = "OTC请求ID")
    private Long otcRequestId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * finishQty
     */
    @Schema(description = "finishQty")
    private Integer finishQty;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    private String hazmatVersionRefNum;

}