package cn.need.cloud.biz.service.inbound.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.BaseShowLogStatusEnum;
import cn.need.cloud.biz.client.constant.enums.feeconfig.FeeStatusEnum;
import cn.need.cloud.biz.client.constant.enums.inbound.InboundRequestStatusEnum;
import cn.need.cloud.biz.client.constant.enums.warehouse.WarehousePrefixEnum;
import cn.need.cloud.biz.converter.inbound.InboundRequestConverter;
import cn.need.cloud.biz.mapper.inbound.InboundRequestMapper;
import cn.need.cloud.biz.model.bo.inbound.InboundRequestAuditContextBO;
import cn.need.cloud.biz.model.entity.inbound.InboundRequest;
import cn.need.cloud.biz.model.entity.inbound.InboundRequestDetail;
import cn.need.cloud.biz.model.param.inbound.create.InboundRequestCreateParam;
import cn.need.cloud.biz.model.param.inbound.create.InboundRequestDetailCreateParam;
import cn.need.cloud.biz.model.param.inbound.update.InboundRequestUpdateParam;
import cn.need.cloud.biz.model.query.inbound.InboundRequestQuery;
import cn.need.cloud.biz.model.vo.base.BasePartnerVO;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.inbound.request.InboundRequestAuditVO;
import cn.need.cloud.biz.model.vo.inbound.request.InboundRequestDetailVO;
import cn.need.cloud.biz.model.vo.inbound.request.InboundRequestVO;
import cn.need.cloud.biz.model.vo.page.InboundRequestPageVO;
import cn.need.cloud.biz.service.helper.auditshowlog.inbound.InboundRequestAuditLogHelper;
import cn.need.cloud.biz.service.inbound.InboundRequestDetailService;
import cn.need.cloud.biz.service.inbound.InboundRequestService;
import cn.need.cloud.biz.util.*;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.cloud.upms.cache.TenantCacheService;
import cn.need.cloud.upms.cache.bean.TenantCache;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.api.NoteParam;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import cn.need.framework.starter.warehouse.util.WarehouseUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 入库请求服务实现类
 * </p>
 * <p>
 * 该类实现了入库请求相关的业务逻辑，包括创建、更新、查询入库请求及其详情等功能。
 * 入库请求（InboundRequest）是指供应商或合作伙伴向仓库申请入库的请求单，
 * 是入库业务流程的起点，包含了预计到达时间、货物明细等信息。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class InboundRequestServiceImpl extends SuperServiceImpl<InboundRequestMapper, InboundRequest> implements InboundRequestService {

    /**
     * 入库请求详情服务，用于管理入库请求的详细信息
     */
    @Resource
    private InboundRequestDetailService inboundRequestDetailService;

    /**
     * 租户缓存服务，用于获取租户相关信息
     */
    @Resource
    private TenantCacheService tenantCacheService;


    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    /**
     * 根据参数创建新入库请求
     * <p>
     * 该方法在事务中执行，确保数据一致性。创建入库请求时会进行以下步骤：
     * 1. 验证参数有效性，包括预计到达时间和行号
     * 2. 转换并初始化入库请求实体
     * 3. 插入入库请求记录到数据库
     * 4. 处理并保存入库请求详情
     * 5. 记录审计日志
     * </p>
     *
     * @param createParam 入库请求创建参数，包含入库请求的基本信息和详情
     * @return 创建的入库请求ID
     * @throws BusinessException 如果参数为空或验证失败，则抛出业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public InboundRequest insertByParam(InboundRequestCreateParam createParam) {
        // 检查传入入库请求参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        validEstimateArrivalDate(createParam.getEstimateArrivalDate());
        // 校验lineNum
        validLineNum(createParam.getDetails());
        // 获取入库请求转换器实例，用于将入库请求参数对象转换为实体对象
        InboundRequestConverter converter = Converters.get(InboundRequestConverter.class);
        // 将入库请求参数对象转换为实体对象并初始化
        InboundRequest entity = initInboundRequest(converter.toEntity(createParam));
        // 插入入库请求实体对象到数据库
        super.insert(entity);
        //传入入库请求实体
        List<InboundRequestDetail> inboundRequestDetails = BeanUtil.copyNew(createParam.getDetails(), InboundRequestDetail.class);
        Validate.notEmpty(inboundRequestDetails, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "product details"));
        // 新增逻辑：判断是否有Hazmat类型产品
        boolean hasHazmat = inboundRequestDetails.stream()
                .anyMatch(detail -> "Hazmat".equals(detail.getProductType())); // 根据实际字段名调整

        // 设置entity的productType
        entity.setProductType(hasHazmat ? "Hazmat" : "Normal");

        //填充请求单id以及仓库id
        inboundRequestDetails.forEach(item -> item.setInboundRequestId(entity.getId()));
        //持久化
        inboundRequestDetailService.insertBatch(inboundRequestDetails);
        //记录日志
        InboundRequestAuditLogHelper.recordLog(entity);
        // 返回入库请求实体
        return entity;
    }


    /**
     * 根据参数更新入库请求
     * <p>
     * 该方法在事务中执行，确保数据一致性。更新入库请求时会进行以下步骤：
     * 1. 验证参数有效性
     * 2. 检查入库请求状态是否允许更新（只有NEW或REJECTED状态才能更新）
     * 3. 验证预计到达时间和行号
     * 4. 转换并更新入库请求实体
     * 5. 更新入库请求详情
     * 6. 记录更改日志
     * </p>
     *
     * @param updateParam 入库请求更新参数，包含需要更新的字段和详情
     * @return 更新的记录数
     * @throws BusinessException 如果参数为空、ID为空或验证失败，则抛出业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public InboundRequest updateByParam(InboundRequestUpdateParam updateParam) {
        // 检查传入入库请求参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        InboundRequest inboundRequest = super.getById(updateParam.getId());
        //校验输入状态
        if (!InboundRequestStatusEnum.getCanEdit().contains(inboundRequest.getInboundRequestStatus())) {
            throw new BusinessException(String.format(ErrorMessages.STATUS_INVALID_OPERATION, "edit", "InboundRequest", inboundRequest.getInboundRequestStatus()));
        }
        //校验预计到达时间
        validEstimateArrivalDate(updateParam.getEstimateArrivalDate());
        // 校验lineNum
        validLineNum(BeanUtil.copyNew(updateParam.getDetails(), InboundRequestDetailCreateParam.class));
        // 获取入库请求转换器实例，用于将入库请求参数对象转换为实体对象
        InboundRequestConverter converter = Converters.get(InboundRequestConverter.class);
        // 将入库请求参数对象转换为实体对象
        InboundRequest entity = converter.toEntity(updateParam);
        //状态校验
        List<String> list = Lists.arrayList(InboundRequestStatusEnum.NEW.getStatus(), InboundRequestStatusEnum.REJECTED.getStatus());
        Validate.isTrue(list.contains(entity.getInboundRequestStatus()),
                String.format(ErrorMessages.STATUS_INVALID_OPERATION, "edit", "InboundRequest", entity.getInboundRequestStatus()));
        //requestRefNum去空格
        if (ObjectUtil.isNotEmpty(entity.getRequestRefNum())) {
            entity.setRequestRefNum(FormatUtil.removeSpecialCharacters(entity.getRequestRefNum()));
        }
        //获取更新前的旧vo数据
        InboundRequestVO oldVo = detailById(updateParam.getId());
        // 执行更新入库请求操作
        super.update(entity);
        //更新入库请求详情
        List<InboundRequestDetail> inboundRequestDetailList = BeanUtil.copyNew(updateParam.getDetails(), InboundRequestDetail.class);

        inboundRequestDetailList.forEach(inboundRequestDetail -> {
            inboundRequestDetail.setInboundRequestId(oldVo.getId());
            inboundRequestDetail.setWarehouseId(oldVo.getWarehouseId());
        });

        //更新入库请求详情
        inboundRequestDetailService.updateDetail(inboundRequestDetailList, oldVo.getId(), oldVo.getWarehouseId());
        //获取更新后的新vo数据
        InboundRequestVO newVo = detailById(updateParam.getId());

        //记录日志
        InboundRequestAuditLogHelper.recordWithModified(BeanUtil.copyNew(newVo, InboundRequest.class), BaseShowLogStatusEnum.MODIFIED.getStatus(), ModifyCompareUtil.recordModifyLog(newVo, oldVo));

        return entity;
    }

    /**
     * 根据查询条件获取入库请求列表
     * <p>
     * 该方法用于按照指定的查询条件查询入库请求列表。
     * </p>
     *
     * @param query 查询条件
     * @return 入库请求分页视图对象列表
     */
    @Override
    public List<InboundRequestPageVO> listByQuery(InboundRequestQuery query) {
        return mapper.listByQuery(query);
    }

    /**
     * 根据查询条件分页获取入库请求
     * <p>
     * 该方法按照指定的查询条件和分页参数查询入库请求列表。
     * 查询结果会填充交易合作伙伴信息。
     * </p>
     *
     * @param search 包含查询条件和分页参数的搜索对象
     * @return 包含分页信息的入库请求分页视图对象
     * <p>
     * TODO: 代码中没有处理搜索条件中的特殊字符，可能导致SQL注入风险
     * 优化建议：对搜索条件进行安全处理，移除或转义特殊字符
     */
    @Override
    public PageData<InboundRequestPageVO> pageByQuery(PageSearch<InboundRequestQuery> search) {
        //获取分页参数
        Page<InboundRequest> page = Conditions.page(search, entityClass);
        //入库请求单分页列表
        List<InboundRequestPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        //填充合作伙伴信息
        filledTransactionPartnerInfo(dataList);
        //返回分页列表
        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取入库请求详情
     * <p>
     * 该方法用于获取指定ID的入库请求详细信息，包括其关联的详情信息。
     * 方法会忽略仓库上下文，允许查询任何仓库的入库请求。
     * </p>
     *
     * @param id 入库请求ID
     * @return 入库请求视图对象，包含详细信息
     * @throws BusinessException 如果找不到指定ID的入库请求，则抛出业务异常
     */
    @Override
    public InboundRequestVO detailById(Long id) {
        //过滤仓库
        WarehouseContextHolder.setIgnore(Boolean.TRUE);
        InboundRequest entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in InboundRequest");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "InboundRequest", id));
        }
        return buildInboundRequestVO(entity);
    }

    /**
     * 根据参考编号获取入库请求详情
     * <p>
     * 该方法用于获取指定参考编号的入库请求详细信息，包括其关联的详情信息。
     * 方法会忽略仓库上下文，允许查询任何仓库的入库请求。
     * </p>
     *
     * @param refNum 入库请求参考编号
     * @return 入库请求视图对象，包含详细信息
     * @throws BusinessException 如果找不到指定参考编号的入库请求，则抛出业务异常
     *                           <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         TODO: 方法中调用了未定义的getByRefNum方法，需要补充实现
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         优化建议：实现getByRefNum方法或使用Lambda查询
     */
    @Override
    public InboundRequestVO detailByRefNum(String refNum) {
        //过滤仓库
        WarehouseContextHolder.setIgnore(Boolean.TRUE);
        InboundRequest entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("RefNum: " + refNum + " not found in InboundRequest");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "InboundRequest", "refNum", refNum));
        }
        return buildInboundRequestVO(entity);
    }

    /**
     * 获取特定列的唯一值列表，用于下拉框展示
     * <p>
     * 该方法用于获取入库请求中指定列的唯一值，通常用于构建下拉选择框。
     * </p>
     *
     * @param query 查询条件，包含列名列表
     * @return 下拉框视图对象列表
     */
    @Override
    public List<DropProVO> distinctValue(InboundRequestQuery query) {

        return DropListUtil.dropProList(
                query.getColumnNameList(),
                InboundRequest.class,
                item -> mapper.dropProList(item, query)
        );

    }

    /**
     * 取消请求单状态
     * 该方法用于取消请求单，传入入库请求单id 备注，变更状态
     *
     * @param cancelParam 取消信息
     * @param status      状态信息
     * @return 影响行数
     */
    @Override
    public Integer updateStatus(NoteParam cancelParam, String status) {
        //创建请求单容器
        InboundRequest inboundRequest = getById(cancelParam.getId());
        //填充状态
        inboundRequest.setInboundRequestStatus(status);
        //持久化
        int update = mapper.updateById(inboundRequest);
        //异步记录日志
        InboundRequestAuditLogHelper.recordLog(
                inboundRequest, inboundRequest.getWarehouseId(),
                status,
                cancelParam.getNote(),
                null
        );
        //返回影响行数
        return update;
    }

    @Override
    public void updateRequestStatus(Long inboundRequestId, String status, LocalDateTime arrivedTime) {
        //获取请求单信息
        InboundRequest inboundRequest = super.getById(inboundRequestId);
        //填充入库请求单状态
        inboundRequest.setInboundRequestStatus(status);
        //填充实际到达时间
        if (ObjectUtil.isNotEmpty(arrivedTime)) {
            inboundRequest.setActualArrivalDate(arrivedTime);
        }
        if (InboundRequestStatusEnum.PROCESSING.getStatus().equals(status)) {
            inboundRequest.setProcessStartTime(TimeUtils.now());
        }
        List<String> overStatuses = Arrays.asList(
                InboundRequestStatusEnum.CANCELLED.getStatus(),
                InboundRequestStatusEnum.PROCESSED.getStatus()
        );
        if (overStatuses.contains(status)) {
            inboundRequest.setProcessEndTime(TimeUtils.now());
            inboundRequest.setFeeStatus(FeeStatusEnum.NEW.getStatus());
        }
        //持久化数据库
        mapper.updateById(inboundRequest);
        //记录日志
        InboundRequestAuditLogHelper.recordLog(inboundRequest);
    }

    @Override
    public void updateRequestStatus(InboundRequestAuditContextBO context) {
        //获取前端参数
        InboundRequestAuditVO param = context.getParam();
        //创建入库请求单持久化对象
        InboundRequest inboundRequest = super.getById(param.getId());
        //状态校验
        validStatus(inboundRequest);
        //填充预计到达时间
        inboundRequest.setEstimateArrivalDate(param.getEstimateArrivalDate());
        //填充入库仓库id
        inboundRequest.setWarehouseId(param.getWarehouseId());
        //填充入库请求单状态
        inboundRequest.setInboundRequestStatus(context.getStatus());
        //过滤请求头仓库id
        WarehouseContextHolder.setIgnore(Boolean.TRUE);
        //持久化数据库
        super.update(inboundRequest);
        //记录带有note的日志
        InboundRequestAuditLogHelper.recordLog(
                inboundRequest,
                inboundRequest.getWarehouseId(),
                inboundRequest.getInboundRequestStatus(),
                param.getNote(),
                context.getDescription()
        );
    }


    /// ///////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    @Override
    public Boolean existUnfinishedOrder(Long warehouseId) {
        return WarehouseUtils.execute(warehouseId, () -> {
            List<String> list = Lists.arrayList(InboundRequestStatusEnum.PROCESSED.getStatus(), InboundRequestStatusEnum.CANCELLED.getStatus());
            Long count = lambdaQuery().notIn(InboundRequest::getInboundRequestStatus, list).count();
            return count > 0;
        });
    }

    /**
     * 校验预计到达时间
     *
     * @param estimateArrivalDate 预计到达时间
     */
    private void validEstimateArrivalDate(LocalDateTime estimateArrivalDate) {
        // 校验预计到达时间
        // Long diffTime = FormatUtil.timeCompare(estimateArrivalDate);
        // Validate.isTrue(diffTime <= 7L,
        //         String.format(ErrorMessages.PARAMETER_INVALID, "estimateArrivalDate", "Cannot be earlier than 7 days from the current time"));

        if (ObjectUtil.isEmpty(estimateArrivalDate)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "lastShipDate"));
        }
        if (estimateArrivalDate.isBefore(TimeUtils.now().minusDays(7))) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_INVALID, "lastShipDate", "Cannot be earlier than 7 days from the current time"));
        }
    }

    /**
     * 初始化入库请求对象
     * 此方法用于设置入库请求对象的必要参数，确保其处于有效状态
     *
     * @param entity 入库请求对象，不应为空
     * @return 返回初始化后的入库请求
     * @throws BusinessException 如果传入的入库请求为空，则抛出此异常
     */
    private InboundRequest initInboundRequest(InboundRequest entity) {
        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("InboundRequest cannot be empty");
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "inboundRequest"));
        }
        //填充入库请求单起始状态
        entity.setInboundRequestStatus(InboundRequestStatusEnum.NEW.getStatus());
        //requestRefNum去空格
        if (ObjectUtil.isNotEmpty(entity.getRequestRefNum())) {
            entity.setRequestRefNum(FormatUtil.removeSpecialCharacters(entity.getRequestRefNum()));
        }
        // 生成RefNum
        entity.setRefNum(FormatUtil.substringAfter(RefNumTypeEnum.INBOUND_REQUEST.code, WarehouseContextHolder.getWarehouseId(), WarehousePrefixEnum.PREFIX.getPrefix()));
        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 构建入库请求VO对象
     *
     * @param entity 入库请求对象
     * @return 返回包含详细信息的入库请求VO对象
     */
    private InboundRequestVO buildInboundRequestVO(InboundRequest entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的入库请求VO对象
        InboundRequestVO inboundRequestVO = Converters.get(InboundRequestConverter.class).toVO(entity);
        //获取请求详情
        List<InboundRequestDetailVO> inboundRequestDetailList = inboundRequestDetailService.listByInboundRequestId(inboundRequestVO.getId());
        //获取产品信息
        ProductVersionCacheUtil.filledProductVersion(inboundRequestDetailList);
        //获取仓库信息
        WarehouseCacheUtil.filledWarehouse(inboundRequestVO);
        //填充入库申请单详情
        inboundRequestVO.setDetails(inboundRequestDetailList);
        //填充合作伙伴名称
        TenantCache tenantCache = tenantCacheService.getById(inboundRequestVO.getTransactionPartnerId());
        Validate.notNull(tenantCache, String.format(ErrorMessages.ENTITY_NOT_FOUND, "Tenant", inboundRequestVO.getTransactionPartnerId()));
        inboundRequestVO.setTransactionPartner(BeanUtil.copyNew(tenantCache, BasePartnerVO.class));
        return inboundRequestVO;
    }

    /**
     * 填充合作伙伴名称
     * 该方法用填充合作伙伴名称，传入入库请求列表
     *
     * @param dataList 入库请求列表
     */
    private void filledTransactionPartnerInfo(List<InboundRequestPageVO> dataList) {

        //todo: 改到通用方法
        //获取合作伙伴id
        List<Long> transactionPartnerIds = dataList.stream().map(InboundRequestPageVO::getTransactionPartnerId).toList();
        //填充合作伙伴名称
        List<TenantCache> tenantCacheList = tenantCacheService.listByIds(transactionPartnerIds);
        //按照合作伙伴id映射合作伙伴对象
        Map<Long, TenantCache> tenantCacheMap = ObjectUtil.toMap(tenantCacheList, TenantCache::getId);
        //遍历入库请求单分页列表
        dataList.forEach(item -> {
            //获取合作伙伴对象
            TenantCache tenantCache = tenantCacheMap.get(item.getTransactionPartnerId());
            //判空
            if (ObjectUtil.isNotEmpty(tenantCache)) {
                item.setTransactionPartnerVO(BeanUtil.copyNew(tenantCache, BasePartnerVO.class));
            }
        });
    }

    /**
     * 状态校验
     *
     * @param inboundRequest 入库请求单
     */
    private void validStatus(InboundRequest inboundRequest) {
        //状态容器
        List<String> list = Lists.arrayList(InboundRequestStatusEnum.NEW.getStatus(), InboundRequestStatusEnum.REJECTED.getStatus());
        Validate.isTrue(list.contains(inboundRequest.getInboundRequestStatus()),
                String.format(ErrorMessages.STATUS_INVALID_OPERATION, "audit", "InboundRequest", inboundRequest.getInboundRequestStatus()));
    }

    /**
     * 校验lineNum
     *
     * @param detailList 入库请求详情
     */
    private void validLineNum(List<InboundRequestDetailCreateParam> detailList) {
        List<Integer> list = detailList.stream().map(InboundRequestDetailCreateParam::getLineNum).distinct().toList();
        Validate.isTrue(!list.contains(DataState.DISABLED),
                String.format(ErrorMessages.PARAMETER_INVALID, "lineNum", "Cannot be zero"));
        Validate.isTrue(ObjectUtil.equal(list.size(), detailList.size()),
                String.format(ErrorMessages.BUSINESS_UNIQUE_CONSTRAINT, "InboundRequestDetail", "lineNum", "line numbers"));
    }

}
