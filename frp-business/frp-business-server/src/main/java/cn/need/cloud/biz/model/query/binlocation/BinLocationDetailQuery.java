package cn.need.cloud.biz.model.query.binlocation;

import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库位详情 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "库位详情 query对象")
public class BinLocationDetailQuery extends SuperQuery {

    /**
     * inStockQty
     */
    @Schema(description = "inStockQty")
    private Integer inStockQty;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;


}