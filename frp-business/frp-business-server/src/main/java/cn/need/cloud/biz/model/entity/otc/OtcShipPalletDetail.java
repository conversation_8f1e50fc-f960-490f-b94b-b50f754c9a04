package cn.need.cloud.biz.model.entity.otc;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * OTC运输托盘详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otc_ship_pallet_detail")
public class OtcShipPalletDetail extends SuperModel {


    /**
     * 快递号
     */
    @TableField("tracking_num")
    private String trackingNum;

    /**
     * 打包id
     */
    @TableField("otc_package_id")
    private Long otcPackageId;


    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 发货到c端托盘运输id
     */
    @TableField("otc_ship_pallet_id")
    private Long otcShipPalletId;

}
