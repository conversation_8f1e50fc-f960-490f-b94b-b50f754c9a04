package cn.need.cloud.biz.model.query.otb.workorder;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;


/**
 * OTB工单 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTB工单 query对象")
public class OtbWorkorderQuery extends SuperQuery {


    /**
     * 出库工单id
     */
    @Schema(description = "出库工单id")
    private List<Long> idList;

    /**
     * 请求id
     */
    @Schema(description = "请求id")
    private Long otbRequestId;

    /**
     * otb 工单状态
     */
    @Schema(description = "otb 工单状态")
    private String otbWorkorderStatus;

    /**
     * otb 工单状态
     */
    @Schema(description = "otb 工单状态集合")
    @Condition(value = Keyword.IN, fields = {"otbWorkorderStatus"})
    private List<String> otbWorkorderStatusList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private Set<String> refNumList;

    /**
     * request快照RefNum
     */
    @Schema(description = "request快照RefNum")
    private String requestSnapshotRefNum;

    /**
     * request快照RefNum
     */
    @Schema(description = "request快照RefNum集合")
    @Condition(value = Keyword.IN, fields = {"requestSnapshotRefNum"})
    private List<String> requestSnapshotRefNumList;

    /**
     * request快照渠道
     */
    @Schema(description = "request快照渠道")
    private String requestSnapshotChannel;

    /**
     * request快照渠道
     */
    @Schema(description = "request快照渠道集合")
    @Condition(value = Keyword.IN, fields = {"requestSnapshotChannel"})
    private List<String> requestSnapshotChannelList;

    /**
     * 工单产品类型
     */
    @Schema(description = "工单产品类型")
    private List<String> workorderProductTypeList;


    /**
     * 请求快照 发货窗口结束时间开始
     */
    @Schema(description = "请求快照 发货窗口结束时间开始")
    @Condition(Keyword.GE)
    private LocalDateTime requestSnapshotShipWindowEndStart;
    /**
     * 请求快照 发货窗口结束时间结束
     */
    @Schema(description = "请求快照 发货窗口结束时间结束")
    @Condition(Keyword.LE)
    private LocalDateTime requestSnapshotShipWindowEndEnd;

    /**
     * 请求快照 发货窗口开始时间开始
     */
    @Schema(description = "请求快照 发货窗口开始时间开始")
    @Condition(Keyword.GE)
    private LocalDateTime requestSnapshotShipWindowStartStart;
    /**
     * 请求快照 发货窗口开始时间结束
     */
    @Schema(description = "请求快照 发货窗口开始时间结束")
    @Condition(Keyword.LE)
    private LocalDateTime requestSnapshotShipWindowStartEnd;

    /**
     * otb 请求装运状态
     */
    @Schema(description = "otb 请求装运状态")
    private String otbRequestShipmentStatus;

    /**
     * otb 请求装运状态
     */
    @Schema(description = "otb 请求装运状态集合")
    @Condition(value = Keyword.IN, fields = {"otbRequestShipmentStatus"})
    private List<String> otbRequestShipmentStatusList;

    /**
     * otb 工单 预状态
     */
    @Schema(description = "otb 工单 预状态")
    private String workorderPrepStatus;

    /**
     * otb 工单 预状态
     */
    @Schema(description = "otb 工单 预状态集合")
    @Condition(value = Keyword.IN, fields = {"workorderPrepStatus"})
    private List<String> workorderPrepStatusList;

    /**
     * 订单类型，LTL,SmallParcel
     */
    @Schema(description = "订单类型，LTL,SmallParcel")
    private String otbWorkorderType;

    /**
     * 订单类型，LTL,SmallParcel
     */
    @Schema(description = "订单类型，LTL,SmallParcel集合")
    @Condition(value = Keyword.IN, fields = {"otbWorkorderType"})
    private List<String> otbWorkorderTypeList;

    /**
     * 请求快照RequestRefnum
     */
    @Schema(description = "请求快照RequestRefnum集合")
    @Condition(value = Keyword.IN, fields = {"requestSnapshotRequestRefNum"})
    private Set<String> requestSnapshotRequestRefNumList;

    /**
     * 请求快照 发货窗口开始时间
     */
    @Schema(description = "请求快照 发货窗口开始时间")
    private LocalDateTime requestSnapshotShipWindowStart;

    /**
     * 请求快照 发货窗口结束时间
     */
    @Schema(description = "请求快照 发货窗口结束时间")
    private LocalDateTime requestSnapshotShipWindowEnd;

    @Schema(description = "流程类型")
    private String processType;
}