package cn.need.cloud.biz.model.entity.setting;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 打印设置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("printer_settings")
public class PrinterSettings extends SuperModel {


    /**
     * 表字段：label_printer
     * 用于存储标签打印机的相关信息
     */
    @TableField("label_printer")
    private String labelPrinter;

    /**
     * 表字段：1*3_label_print
     * 用于存储1x3标签打印机的具体信息
     */
    @TableField("one_by_three_label_printer")
    private String oneByThreeLabelPrinter;

    /**
     * 表字段：regular_printer
     * 用于存储常规打印机的信息
     */
    @TableField("regular_printer")
    private String regularPrinter;

    /**
     * 表字段：mobile_label_printer
     * 用于存储移动标签打印机的详情
     */
    @TableField("mobile_label_printer")
    private String mobileLabelPrinter;

    /**
     * 表字段：label_printer
     * 用于存储移动CLodop打印插件的信息
     */
    @TableField("mobile_c_lodop_ip")
    private String mobileCLodopIp;

    /**
     * 表字段：scanner_plugin_ip
     * 用于存储扫描插件的IP地址
     */
    @TableField("scanner_plugin_ip")
    private String scannerPluginIp;

    /**
     * 表字段：scanner
     * 用于存储扫描仪的相关信息
     */
    @TableField("scanner")
    private String scanner;

}
