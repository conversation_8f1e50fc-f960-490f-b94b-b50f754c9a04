package cn.need.cloud.biz.model.vo.base.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 请求单确认页Detail对象
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@Schema(description = "请求单确认对象")
public class RequestConfirmVO implements Serializable {

    @Schema(description = "请求单id")
    private Long id;

    @Schema(description = "请求单状态")
    private String requestStatus;

    @Schema(description = "refNum")
    private String refNum;
}
