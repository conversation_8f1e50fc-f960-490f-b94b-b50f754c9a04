package cn.need.cloud.biz.service.otb.shipment.impl;


import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.PrintStatusEnum;
import cn.need.cloud.biz.client.constant.enums.base.ShowLogEnum;
import cn.need.cloud.biz.client.constant.enums.otb.*;
import cn.need.cloud.biz.model.entity.otb.OtbPackage;
import cn.need.cloud.biz.model.entity.otb.OtbPallet;
import cn.need.cloud.biz.model.entity.otb.OtbRoutingInstruction;
import cn.need.cloud.biz.model.entity.otb.OtbShipment;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.vo.otb.pallet.OtbPalletVO;
import cn.need.cloud.biz.model.vo.otb.shipment.OtbSignedBolFileVO;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbPackageAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbPalletAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbShipmentAuditLogHelper;
import cn.need.cloud.biz.service.otb.pallet.OtbPalletService;
import cn.need.cloud.biz.service.otb.pkg.OtbPackageService;
import cn.need.cloud.biz.service.otb.ri.OtbRoutingInstructionService;
import cn.need.cloud.biz.service.otb.shipment.OtbShipmentLtlService;
import cn.need.cloud.biz.service.otb.shipment.OtbShipmentService;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * OTB大件装运 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
@Service
public class OtbShipmentLtlServiceImpl implements OtbShipmentLtlService {
    @Resource
    private OtbShipmentService otbShipmentService;
    @Resource
    private OtbPalletService otbPalletService;
    @Resource
    private OtbPackageService otbPackageService;
    @Resource
    private OtbRoutingInstructionService otbRoutingInstructionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void printedBolFile(PrintQuery printQuery) {
//获取发货单
        OtbShipment otbShipment = otbShipmentService.getById(printQuery.getId());
        //记录日志
        OtbShipmentAuditLogHelper.recordWithStatus(
                otbShipment,
                StringUtil.format("{} {}", ShowLogEnum.BOI_PRINT.getStatus(), printQuery.getPrintStatus()),
                BaseTypeLogEnum.OPERATION.getType(),
                printQuery.getNote());
        List<String> list = Lists.arrayList(OtbShipmentTypeEnum.LTL.getType(), OtbShipmentTypeEnum.CREATE_PALLET.getType());
        Validate.isTrue(list.contains(otbShipment.getOtbShipmentType()), "StatusError" + otbShipment.getOtbShipmentType());
        boolean flag = StringUtil.equals(printQuery.getPrintStatus(), PrintStatusEnum.SUCCESS.getStatus())
                && StringUtils.equals(otbShipment.getOtbShipmentStatus(), OtbShipmentStatusEnum.CHANNEL_CONFIRMED.getStatus());
        if (flag) {
            otbShipmentService.markProcessing(otbShipment);
        }
        //更新发货单状态
        otbShipmentService.update(otbShipment);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void printedPalletFile(PrintQuery printQuery) {
        //获取发货单
        OtbShipment otbShipment = otbShipmentService.getById(printQuery.getId());
        //状态校验
        Validate.isTrue(StringUtil.equals(otbShipment.getOtbShipmentType(), OtbShipmentTypeEnum.LTL.getType()),
                "StatusError current status is" + otbShipment.getOtbShipmentStatus());
        Validate.isTrue(!StringUtil.equals(otbShipment.getPalletFileStatus(), OtbPalletFileStatusEnum.NONE.getStatus()),
                otbShipment.getPalletFileStatus() + "can not markPrinted");
        if (StringUtil.equals(printQuery.getPrintStatus(), PrintStatusEnum.SUCCESS.getStatus()) ||
                StringUtil.equals(otbShipment.getPalletFileStatus(), OtbPalletFileStatusEnum.NEW.getStatus())) {
            otbShipment.setPalletFileStatus(OtbPalletFileStatusEnum.PRINTED.getStatus());
        }
        //持久化
        otbShipmentService.update(otbShipment);
        //记录日志
        OtbShipmentAuditLogHelper.recordWithStatus(
                otbShipment,
                otbShipment.getPalletFileStatus(),
                BaseTypeLogEnum.OPERATION.getType(),
                null
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void splitPallet(Long otbShipmentId) {
        //获取发货单
        OtbShipment otbShipment = otbShipmentService.getById(otbShipmentId);
        //校验发货单状态
        Validate.isTrue(StringUtil.equals(otbShipment.getOtbShipmentStatus(), OtbShipmentStatusEnum.CHANNEL_CONFIRMED.getStatus()),
                StringUtil.format("StatusError current status is {}", otbShipment.getOtbShipmentStatus()));
        //校验类型
        Validate.isTrue(StringUtil.equals(otbShipment.getOtbShipmentType(), OtbShipmentTypeEnum.SPLIT_PALLET.getType()),
                "TypeError current type is {}", otbShipment.getOtbShipmentType());
        //填充发货单状态及类型
        otbShipment.setOtbShipmentStatus(OtbShipmentStatusEnum.PROCESSING.getStatus());
        otbShipment.setOtbShipmentType(OtbShipmentTypeEnum.SMALL_PARCEL.getType());
        //获取当前发货单下打托单
        List<OtbPalletVO> otbPalletList = otbPalletService.listByShipmentId(otbShipmentId);
        //获取打托单下包裹信息
        List<OtbPackage> otbPackageList = otbPackageService.listByPalletIdList(otbPalletList
                .stream()
                .map(OtbPalletVO::getId)
                .distinct()
                .toList());
        //获取routingInstruction对象
        OtbRoutingInstruction otbRoutingInstruction = otbRoutingInstructionService.getByShipmentId(otbShipmentId);
        Validate.notNull(otbRoutingInstruction, "RoutingInstruction is null ShipmentId:{}", otbShipmentId);
        //遍历打托单
        otbPalletList.forEach(item -> {
            //更新打托单状态及关联id
            item.setOtbPalletStatus(OtbPalletStatusEnum.SPILT_PALLET.getStatus());
            item.setOtbShipmentId(null);
            //记录打托单日志
            OtbPalletAuditLogHelper.recordLog(BeanUtil.copyNew(item, OtbPallet.class));
        });
        //校验包裹
        otbPackageList.forEach(obj -> {
            //包裹打托类型
            obj.setOtbPalletType(OtbPackagePalletTypeEnum.NONE.getType());
            //包裹状态
            obj.setOtbPackageStatus(OtbPackageStatusEnum.BUILD_SHIPMENT.getCode());
            //置空打托状态
            obj.setOtbPalletId(null);
            //填充发货单id
            obj.setOtbShipmentId(otbShipmentId);
            //填充运营信息
            obj.setShipCarrier(otbRoutingInstruction.getShipCarrier());
            obj.setShipMethod(otbRoutingInstruction.getShipMethod());
            obj.setShipApiProfileRefNum(otbRoutingInstruction.getShipApiProfileRefNum());
            //记录日志
            OtbPackageAuditLogHelper.recordLog(obj);
        });
        //持久化
        otbShipmentService.update(otbShipment);
        //持久化
        otbPackageService.updateBatchWithNull(otbPackageList);
        otbPalletService.updateBatchWithNull(otbPalletList);
        //记录发货单日志
        OtbShipmentAuditLogHelper.recordLog(otbShipment);
    }

    @Override
    public void markPalletReadyToShip(Long otbPalletId, Long otbShipmentId) {
        //获取发货单
        OtbShipment otbShipment = otbShipmentService.getById(otbShipmentId);
        //校验发货单类型
        Validate.isTrue(StringUtil.equals(otbShipment.getOtbShipmentType(), OtbShipmentTypeEnum.LTL.getType()),
                "TypeError current type is not  LTL");
        //校验发货单类型
        Validate.isTrue(StringUtil.equals(otbShipment.getOtbShipmentStatus(), OtbShipmentStatusEnum.PROCESSING.getStatus()),
                "StatusError current status is not  Processing");
        //校验打托文件打印状态
        Validate.isTrue(!StringUtil.equals(otbShipment.getPalletFileStatus(), OtbPalletFileStatusEnum.NEW.getStatus()),
                "PalletFileStatusError current status is not  markPalletReadyToShip");
        //获取打托单
        OtbPallet otbPallet = otbPalletService.getById(otbPalletId);
        //校验打托单状态
        Validate.isTrue(StringUtil.equals(otbPallet.getOtbPalletStatus(), OtbPalletStatusEnum.CHANNEL_CONFIRM.getStatus()),
                "PalletStatusError current status is not  ChannelConfirmed");
        //更新打托单状态
        otbPallet.setOtbPalletStatus(OtbPalletStatusEnum.READY_TO_SHIP.getStatus());
        otbPalletService.update(otbPallet);
        //记录日志
        OtbPalletAuditLogHelper.recordLog(otbPallet);
        //获取打托单下的所有包裹
        List<OtbPackage> otbPackageList = otbPackageService.listByPalletIdList(Lists.arrayList(otbPalletId));
        //遍历包裹
        otbPackageList.forEach(item -> {
            item.setOtbPackageStatus(OtbPackageStatusEnum.READY_TO_SHIP.getCode());
            OtbPackageAuditLogHelper.recordLog(item);
        });
        //更新包裹状态
        otbPackageService.updateBatch(otbPackageList);
        //校验是否所有打托都为READY_TO_SHIP
        boolean isAnyShipmentNotReadyToShip = otbPalletService.listByShipmentId(otbShipmentId)
                .stream()
                .anyMatch(item -> ObjectUtil.notEqual(otbPalletId, item.getId()) &&
                        !StringUtil.equals(item.getOtbPalletStatus(), OtbPalletStatusEnum.READY_TO_SHIP.getStatus()) &&
                        ObjectUtil.equal(otbShipmentId, item.getOtbShipmentId()));
        //若打托单全部状态都为READY_TO_SHIP则更新发货单状态
        if (!isAnyShipmentNotReadyToShip) {
            otbShipment.setOtbShipmentStatus(OtbShipmentStatusEnum.READY_TO_SHIP.getStatus());
            otbShipmentService.update(otbShipment);
            //记录日志
            OtbShipmentAuditLogHelper.recordLog(otbShipment);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processed(OtbSignedBolFileVO otbSignedBolFileVO) {
        //获取发货单
        OtbShipment otbShipment = otbShipmentService.getById(otbSignedBolFileVO.getOtbShipmentId());
        //校验发货单类型
        Validate.isTrue(StringUtil.equals(otbShipment.getOtbShipmentType(), OtbShipmentTypeEnum.LTL.getType()),
                "TypeError shipment:{" + otbShipment.getId() + "} type is not  LTL ");
        //校验发货单状态
        Validate.isTrue(StringUtil.equals(otbShipment.getOtbShipmentStatus(), OtbShipmentStatusEnum.READY_TO_SHIP.getStatus()),
                "StatusError shipment:{" + otbShipment.getId() + "} status is not  ReadyToShip");
        //获取当前发货单打托单集合
        List<OtbPalletVO> otbPalletList = otbPalletService.listByShipmentId(otbShipment.getId());
        List<Long> list = otbPalletList
                .stream()
                .filter(item -> !StringUtil.equals(item.getOtbPalletStatus(), OtbPalletStatusEnum.READY_TO_SHIP.getStatus()))
                .map(OtbPalletVO::getId)
                .toList();
        Validate.isTrue(ObjectUtil.isEmpty(list), "PalletStatusError OtbPalletId:{" + JsonUtil.toJson(list) + "} status is not  ReadyToShip");
        //更新打托单状态
        List<OtbPallet> palletList = BeanUtil.copyNew(otbPalletList, OtbPallet.class);
        palletList.forEach(item -> {
            item.setOtbPalletStatus(OtbPalletStatusEnum.SHIPPED.getStatus());
            OtbPalletAuditLogHelper.recordLog(item);
        });
        //获取打托单集合
        List<Long> otbPalletIdList = otbPalletList
                .stream()
                .map(OtbPalletVO::getId)
                .distinct()
                .toList();
        List<OtbPackage> otbPackageList = otbPackageService.listByPalletIdList(otbPalletIdList);
        //遍历包裹
        otbPackageList.forEach(item -> {
            item.setOtbPackageStatus(OtbPackageStatusEnum.SHIPPED.getCode());
            OtbPackageAuditLogHelper.recordLog(item);
        });

        //copy签署文件值
        BeanUtil.copy(otbSignedBolFileVO, otbShipment);
        //发货单更新状态
        otbShipment.setBolNum(otbSignedBolFileVO.getBolNum());
        //持久化
        otbShipmentService.update(otbShipment);
        otbPackageService.updateBatch(otbPackageList);
        otbPalletService.updateBatch(palletList);

        otbShipmentService.markProcessed(otbShipment);
    }
}
