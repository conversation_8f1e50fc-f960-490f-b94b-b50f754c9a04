package cn.need.cloud.biz.mapper.inbound;

import cn.need.cloud.biz.model.entity.inbound.InboundRequestDetail;
import cn.need.cloud.biz.model.query.inbound.InboundRequestDetailQuery;
import cn.need.cloud.biz.model.vo.page.InboundRequestDetailPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 入库请求详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface InboundRequestDetailMapper extends SuperMapper<InboundRequestDetail> {

    /**
     * 根据条件获取入库请求详情列表
     *
     * @param query 查询条件
     * @return 入库请求详情集合
     */
    default List<InboundRequestDetailPageVO> listByQuery(InboundRequestDetailQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取入库请求详情分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 入库请求详情集合
     */
    List<InboundRequestDetailPageVO> listByQuery(@Param("qo") InboundRequestDetailQuery query, @Param("page") Page<?> page);
}