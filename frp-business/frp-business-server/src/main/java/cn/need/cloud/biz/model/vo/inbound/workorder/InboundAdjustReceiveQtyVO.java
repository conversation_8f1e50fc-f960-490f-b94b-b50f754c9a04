package cn.need.cloud.biz.model.vo.inbound.workorder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 调整收货数量 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@Schema(description = "调整收货数量 vo对象")
public class InboundAdjustReceiveQtyVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 入库工单详情id
     */
    @Schema(description = "入库工单详情id")
    private Long inBoundWorkOrderDetailId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 收货数量
     */
    @Schema(description = "收货数量")
    private Integer needReceiveQty;

    /**
     * 入库工单id
     */
    @Schema(description = "入库工单id")
    private Long inBoundWorkOrderId;
}
