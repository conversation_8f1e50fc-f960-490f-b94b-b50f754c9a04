package cn.need.cloud.biz.model.vo.base.auditlog;

import cn.need.cloud.biz.cache.bean.BinLocationCache;
import cn.need.cloud.biz.util.BinLocationCacheUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 库位ShowLog
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
public class BaseBinLocationAuditShowLogVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 库位id
     */
    @JsonIgnore
    private Long binLocationId;

    /**
     * 库位信息
     */
    private BaseBinLocationLogVO binLocationVO;

    /**
     * 产品信息
     */
    public BaseBinLocationLogVO getBinLocationVO() {
        if (ObjectUtil.isEmpty(binLocationVO)) {
            BinLocationCache binLocationCache = BinLocationCacheUtil.getById(binLocationId);

            binLocationVO = BeanUtil.copyNew(binLocationCache, BaseBinLocationLogVO.class);
        }
        return binLocationVO;
    }
}
