package cn.need.cloud.biz.model.param.inbound.create;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 上架 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "上架 vo对象")
public class InboundPutawaySlipCreateParam implements Serializable {


    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;


    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * 上架状态
     */
    @Schema(description = "上架状态")
    private String inboundPutawaySlipStatus;


}