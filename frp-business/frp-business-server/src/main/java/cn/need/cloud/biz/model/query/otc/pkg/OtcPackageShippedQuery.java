package cn.need.cloud.biz.model.query.otc.pkg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;


/**
 * OTC包裹 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC包裹 shipped 发货 query对象")
public class OtcPackageShippedQuery {

    /**
     * 包裹id集合
     */
    @Schema(description = "包裹id集合")
    @NotEmpty(message = "包裹id集合不能为空")
    private List<Long> idList;
}