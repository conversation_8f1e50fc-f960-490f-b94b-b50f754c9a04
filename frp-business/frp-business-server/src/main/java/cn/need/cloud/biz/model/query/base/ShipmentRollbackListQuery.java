package cn.need.cloud.biz.model.query.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * WorkorderRollbackListQuery
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@Schema(description = "包裹 Rollback列表 对象")
public class ShipmentRollbackListQuery implements Serializable {

    @Schema(description = "包裹id集合")
    @NotEmpty(message = "Package idList is not empty")
    @NotNull(message = "Package idList is not null")
    private List<Long> idList;

    @NotNull(message = "workorderId is not null")
    private Long workorderId;
}
