package cn.need.cloud.biz.provider.product;

import cn.hutool.core.collection.CollUtil;
import cn.need.cloud.biz.client.api.path.ProductPath;
import cn.need.cloud.biz.client.api.product.ProductClient;
import cn.need.cloud.biz.client.dto.base.BasePartnerDTO;
import cn.need.cloud.biz.client.dto.base.BaseTransactionPartnerDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseDeleteReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseProductQueryReqDTO;
import cn.need.cloud.biz.client.dto.req.product.*;
import cn.need.cloud.biz.client.dto.resp.base.RefNumWithSupplierSkuRespDTO;
import cn.need.cloud.biz.client.dto.resp.product.ProductDetailRespDTO;
import cn.need.cloud.biz.client.dto.resp.product.ProductHazmatListRespDTO;
import cn.need.cloud.biz.client.dto.resp.product.ProductVersionRespDTO;
import cn.need.cloud.biz.client.dto.resp.product.ProductPageRespDTO;
import cn.need.cloud.biz.client.util.PageUtil;
import cn.need.cloud.biz.model.entity.product.Product;
import cn.need.cloud.biz.model.entity.product.ProductHazmat;
import cn.need.cloud.biz.model.param.product.create.ProductCreateParam;
import cn.need.cloud.biz.model.param.product.update.ProductUpdateParam;
import cn.need.cloud.biz.model.query.product.ProductQuery;
import cn.need.cloud.biz.model.vo.base.BasePartnerVO;
import cn.need.cloud.biz.model.vo.product.ProductVO;
import cn.need.cloud.biz.model.vo.product.ProductVersionVO;
import cn.need.cloud.biz.provider.base.ProductUtil;
import cn.need.cloud.biz.provider.base.TenantUtil;
import cn.need.cloud.biz.service.product.ProductService;
import cn.need.cloud.biz.service.product.ProductSpecialService;
import cn.need.cloud.biz.service.product.ProductVersionService;
import cn.need.cloud.upms.client.api.TenantClient;
import cn.need.cloud.upms.client.dto.TenantDTO;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.util.ApiUtil;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 租户feign接口实现类
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(ProductPath.PREFIX)
public class ProductProvider implements ProductClient {

    @Resource
    private TenantClient tenantClient;
    @Resource
    private ProductService productService;
    @Resource
    private ProductSpecialService productSpecialService;

    @Resource
    private ProductVersionService productVersionService;


    @Override
    @PostMapping(ProductPath.INSERT)
    @IgnoreAuth
    public Result<Long> insert(@RequestBody ProductCreateReqDTO reqDTO) {
        // 填充租户id
        TenantUtil.fillTenant(reqDTO.getTransactionPartner(), reqDTO.getLogisticPartner());
        // 把租户id设置到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        // copy入参
        ProductCreateParam productCreateParam = BeanUtil.copyNew(reqDTO, ProductCreateParam.class);
        productCreateParam.setTransactionPartnerId(reqDTO.getTransactionPartnerId());
        // 返回产品id
        return Result.ok(productService.insertByParam(productCreateParam).getId());
    }

    @Override
    @PostMapping(ProductPath.CREATE_OR_UPDATE)
    public Result<RefNumWithSupplierSkuRespDTO> createOrUpdate(ProductCreateOrUpdateReqDTO reqDTO) {
        // 填充租户id
        TenantUtil.fillTenant(reqDTO.getTransactionPartner(), reqDTO.getLogisticPartner());
        // 把租户id设置到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        // 判断是新增还是编辑
        Product product;
        if (ObjectUtil.isEmpty(reqDTO.getRefNum())) {
            ProductCreateParam productCreateParam = BeanUtil.copyNew(reqDTO, ProductCreateParam.class);
            productCreateParam.setTransactionPartnerId(reqDTO.getTransactionPartnerId());
            //调用新增方法
            product = productService.insertByParam(productCreateParam);
        }else {
            //填充产品id
            ProductUtil.fillProduct(reqDTO.getTransactionPartner(), reqDTO.getProduct());
            // copy入参
            ProductUpdateParam productUpdateParam = BeanUtil.copyNew(reqDTO, ProductUpdateParam.class);
            productUpdateParam.setId(reqDTO.getProductId());
            //调用编辑方法
            product = productService.updateByParam(productUpdateParam);
        }
        RefNumWithSupplierSkuRespDTO respDTO = new RefNumWithSupplierSkuRespDTO();
        respDTO.setRefNum(product.getRefNum());
        respDTO.setSupplierSku(product.getSupplierSku());
        return Result.ok(respDTO);
    }

    @Override
    @PostMapping(ProductPath.UPDATE)
    @IgnoreAuth
    public Result<Integer> update(@RequestBody ProductUpdateReqDTO productUpdateReqDTO) {
        // 填充租户id
        TenantUtil.fillTenant(productUpdateReqDTO);

        // 把租户id设置到上下文
        TenantContextHolder.setTenantId(productUpdateReqDTO.getLogisticPartnerId());

        ProductUtil.fillProduct(productUpdateReqDTO.getLogisticPartner(), productUpdateReqDTO.getProduct());

        // 构建更新产品参数
        productUpdateReqDTO.initId();
        ProductUpdateParam updateParam = BeanUtil.copyNew(productUpdateReqDTO, ProductUpdateParam.class);

        // 更新产品
        productService.updateByParam(updateParam);
        return Result.ok(1);
    }

    @Override
    @PostMapping(ProductPath.DETAIL)
    @IgnoreAuth
    public Result<ProductDetailRespDTO> detail(@RequestBody BaseProductQueryReqDTO queryDTO) {
        // 填充租户信息
        TenantUtil.fillTenant(queryDTO);
        // 将租户信息填充到上下文
        TenantContextHolder.setTenantId(queryDTO.getLogisticPartnerId());
        // 获取产品信息
        ProductUtil.fillProduct(queryDTO.getTransactionPartner(), queryDTO.getProduct());
        // 获取产品详情
        ProductVO productVO = productService.detailById(queryDTO.getProductId());
        ProductDetailRespDTO detailDTO = BeanUtil.copyNew(productVO, ProductDetailRespDTO.class);
        detailDTO.setTransactionPartner(BeanUtil.copyNew(productVO.getTransactionPartnerVO(), BasePartnerDTO.class));
        // 返回结果
        return Result.ok(detailDTO);
    }

    @Override
    @PostMapping(ProductPath.DELETE)
    @IgnoreAuth
    public Result<Integer> remove(@RequestBody BaseDeleteReqDTO baseDeleteReqDTO) {
        // 填充租户id
        TenantUtil.fillTenant(baseDeleteReqDTO);
        // 把租户id设置到上下文
        TenantContextHolder.setTenantId(baseDeleteReqDTO.getLogisticPartnerId());
        // 获取产品基础信息
        ProductUtil.fillProduct(baseDeleteReqDTO.getTransactionPartner(), baseDeleteReqDTO.getProduct());
        // 返回结果
        return Result.ok(productSpecialService.checkAndDelete(baseDeleteReqDTO.getProductId(), baseDeleteReqDTO.getDeletedNote()));
    }

    @Override
    @PostMapping(ProductPath.LIST)
    @IgnoreAuth
    public Result<PageData<ProductPageRespDTO>> list(@RequestBody PageSearch<ProductQueryReqDTO> pageSearch) {
        // 获取传参
        ProductQueryReqDTO condition = pageSearch.getCondition();
        // 填充租户id
        TenantUtil.fillTenant(condition.getLogisticPartner());
        // 把租户id设置到上下文
        TenantContextHolder.setTenantId(condition.getLogisticPartnerId());
        //构建入参
        PageSearch<ProductQuery> search = PageUtil.convert(pageSearch, dto -> BeanUtil.copyNew(dto, ProductQuery.class));
        //组装返回参数
        PageData<ProductVO> pageData = productService.pageByQuery(search);
        PageData<ProductPageRespDTO> data = PageUtil.convert(pageData, vo -> {
            ProductPageRespDTO productPageRespDTO = BeanUtil.copyNew(vo, ProductPageRespDTO.class);
            BasePartnerVO transactionPartnerVO = vo.getTransactionPartnerVO();
            productPageRespDTO.setTransactionPartner(BeanUtil.copyNew(transactionPartnerVO, BasePartnerDTO.class));
            return productPageRespDTO;
        });
        //构建租户信息
        List<ProductPageRespDTO> pageList = data.getRecords();
        //判空
        if (ObjectUtil.isEmpty(pageList)) {
            return Result.ok(data);
        }
        //返回结果
        return Result.ok(data);
    }

    @Override
    @PostMapping(ProductPath.LIST_PRODUCT_DIMENSION_CHANGE_PRODUCT)
    @IgnoreAuth
    public Result<List<ProductVersionRespDTO>> getDimensionChangeProduct(@RequestBody ProductDimensionChangeQueryReqDTO query) {
        // 获取传参
        // 填充租户id
        TenantUtil.fillTenant(query.getLogisticPartner());
        // 把租户id设置到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        List<ProductVersionVO> productVersionVOS =  productVersionService.getDimensionChangeProduct(query.getStartLastModificationTime(),query.getEndLastModificationTime());
        List<ProductVersionRespDTO>  productVersionRespDTOList = BeanUtil.copyNew(productVersionVOS, ProductVersionRespDTO.class);
        //返回结果
        return Result.ok(productVersionRespDTOList);
    }

    /**
     * 根据租户code获取租户id
     *
     * @param queryDTO 租户信息
     */
    private void fillTenantId(BaseTransactionPartnerDTO queryDTO) {
        Set<String> tenantCodeList = CollUtil.newHashSet(
                queryDTO.getTenantCode(),
                queryDTO.getTransactionPartnerCode()
        );
        //获取租户信息
        Result<List<TenantDTO>> result = tenantClient.listByCode(tenantCodeList);
        List<TenantDTO> tenantList = ApiUtil.getResultData(result);
        //遍历租户信息
        tenantList.forEach(item -> tenantCodeList.remove(item.getTenantCode()));
        //校验租户
        Validate.isTrue(
                ObjectUtil.isEmpty(tenantCodeList),
                String.format("The tenant code %s does not exist", tenantCodeList)
        );
        //根据租户code映射租户id
        Map<String, Long> map = ObjectUtil.toMap(tenantList, TenantDTO::getTenantCode, TenantDTO::getId);
        //根据租户code映射租户id
        queryDTO.setTenantId(map.get(queryDTO.getTenantCode()));
        //填充供应商id
        queryDTO.setTransactionPartnerId(map.get(queryDTO.getTransactionPartnerCode()));
    }
}
