package cn.need.cloud.biz.service.helper.auditshowlog.otb;

import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.entity.otb.OtbRoutingInstruction;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.biz.util.log.AuditLogUtil;

import java.util.List;

/**
 * RI日志工具
 *
 * <AUTHOR>
 * @since 2024/10/28
 */
public class OtbRIAuditLogHelper {

    private OtbRIAuditLogHelper() {
    }

    public static void recordLog(List<OtbRoutingInstruction> otbRoutingInstructionList, String type, String note, String description) {
        otbRoutingInstructionList.forEach(item -> recordLog(item, type, note, description));
    }

    public static void recordLog(List<OtbRoutingInstruction> otbRoutingInstructionList) {
        otbRoutingInstructionList.forEach(OtbRIAuditLogHelper::recordLog);
    }

    public static void recordLog(OtbRoutingInstruction otbRoutingInstruction) {
        recordLog(otbRoutingInstruction, BaseTypeLogEnum.STATUS.getType(), null, null);
    }

    public static void recordLog(OtbRoutingInstruction otbRoutingInstruction, String type, String note, String description) {
        recordLog(otbRoutingInstruction, otbRoutingInstruction.getOtbRoutingInstructionStatus(), type, note, description);
    }

    public static void recordLog(OtbRoutingInstruction otbRoutingInstruction, String status, String type, String note, String description) {
        AuditShowLog auditShowLog = AuditLogUtil.commonLog(otbRoutingInstruction)
                .with(AuditShowLog::setEvent, status)
                .with(AuditShowLog::setDescription, description)
                .with(AuditShowLog::setNote, note)
                .with(AuditShowLog::setType, type)
                .build();
        AuditLogHolder.record(auditShowLog);
    }


    public static void recordModifyLog(OtbRoutingInstruction otbRoutingInstruction, String status, String type, String description) {
        recordLog(otbRoutingInstruction, status, type, null, description);
    }
}