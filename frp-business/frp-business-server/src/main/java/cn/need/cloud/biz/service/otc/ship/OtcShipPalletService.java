package cn.need.cloud.biz.service.otc.ship;

import cn.need.cloud.biz.model.entity.otc.OtcShipPallet;
import cn.need.cloud.biz.model.param.otc.create.shippallet.OtcShipPalletWithReturnCreateParam;
import cn.need.cloud.biz.model.query.otc.ship.OtcShipPalletCheckTrackingNumQuery;
import cn.need.cloud.biz.model.query.otc.ship.OtcShipPalletQuery;
import cn.need.cloud.biz.model.vo.otc.OtcShipPalletLabelVO;
import cn.need.cloud.biz.model.vo.otc.OtcShipPalletVO;
import cn.need.cloud.biz.model.vo.page.OtcShipPalletPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

/**
 * <p>
 * OTC运输托盘 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtcShipPalletService extends SuperService<OtcShipPallet> {

    /**
     * 根据查询条件获取OTC运输托盘列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTC运输托盘对象的列表(分页)
     */
    PageData<OtcShipPalletPageVO> pageByQuery(PageSearch<OtcShipPalletQuery> search);

    /**
     * 根据ID获取OTC运输托盘
     *
     * @param id OTC运输托盘ID
     * @return 返回OTC运输托盘VO对象
     */
    OtcShipPalletVO detailById(Long id);

    /**
     * 根据OTC运输托盘唯一编码获取OTC运输托盘
     *
     * @param refNum OTC运输托盘唯一编码
     * @return 返回OTC运输托盘VO对象
     */
    OtcShipPalletVO detailByRefNum(String refNum);


    /**
     * BuildPallet 构建ShipPallet
     *
     * @param createParam 创建参数
     * @return /
     */
    OtcShipPalletVO withReturn(OtcShipPalletWithReturnCreateParam createParam);

    /**
     * Build Pallet Label
     *
     * @param refNum 唯一编码
     * @return /
     */
    OtcShipPalletLabelVO buildShipPalletLabelByRefNum(String refNum);

    /**
     * 校验TrackingNum
     *
     * @param query 校验条件
     * @return 是否正确
     */
    boolean checkTrackingNum(OtcShipPalletCheckTrackingNumQuery query);
}