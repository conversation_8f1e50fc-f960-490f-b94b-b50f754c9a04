package cn.need.cloud.biz.config;

import cn.need.cloud.upms.cache.TenantCacheRepertory;
import cn.need.cloud.upms.cache.TenantCacheService;
import cn.need.cloud.upms.cache.impl.RedisTenantCacheRepertoryImpl;
import cn.need.cloud.upms.cache.impl.RedisTenantCacheServiceImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;


/**
 * 日志缓存配置
 *
 * <AUTHOR>
 */
@Configuration
public class TenantCacheConfig {

    /**
     * 注入日志配置缓存操作工具
     */
    @Bean
    @SuppressWarnings({"rawtypes", "unchecked"})
    public TenantCacheService tenantCacheService(RedisTemplate redisTemplate) {
        return new RedisTenantCacheServiceImpl(redisTemplate);
    }

    /**
     * 注入日志配置缓存操作工具
     */
    @Bean
    @SuppressWarnings({"rawtypes", "unchecked"})
    public TenantCacheRepertory tenantCacheRepertory(RedisTemplate redisTemplate) {
        return new RedisTenantCacheRepertoryImpl(redisTemplate);
    }
}
