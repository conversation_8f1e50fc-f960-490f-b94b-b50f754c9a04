package cn.need.cloud.biz.model.param.base.create;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


/**
 * OTC拣货单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
public class PickingSlipUnpickCreateParam implements Serializable {

    @Schema(description = "工单主键")
    @NotNull(message = "workorderId is not null")
    private Long workorderId;

    @Schema(description = "Unpick Detail集合")
    @Valid
    private List<UnpickDetailCreateParam> detailList;

    @Schema(description = "Note")
    @NotNull(message = "note is not null")
    @NotBlank(message = "note is not blank")
    private String note;
}