package cn.need.cloud.biz.service.otc.workorder.impl;

import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPrepPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPrepPutawaySlipPutAwayDetailBO;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPutawaySlipDetail;
import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorderBinLocation;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderBinLocationService;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderBinLocationSpecialService;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.model.IdModel;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * OTC工单仓储位置 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@AllArgsConstructor
@Service
public class OtcPrepWorkorderBinLocationSpecialServiceImpl implements OtcPrepWorkorderBinLocationSpecialService {

    private final OtcPrepWorkorderBinLocationService otcWorkorderBinLocationService;

    @Override
    public void rollback(OtcPrepPutawaySlipPutAwayBO param) {
        List<Long> wkBinLocationIds = param.getDetailList().stream()
                .map(OtcPrepPutawaySlipPutAwayDetailBO::getPutawaySlipDetail)
                .map(OtcPrepPutawaySlipDetail::getPrepWorkorderBinLocationId)
                .distinct()
                .toList();

        // workorder_bin_location 需要扣减数量
        List<OtcPrepWorkorderBinLocation> workorderBinLocations = otcWorkorderBinLocationService.listByIds(wkBinLocationIds);
        Map<Long, OtcPrepWorkorderBinLocation> wkBinLocationMap = StreamUtils.toMap(workorderBinLocations, IdModel::getId);

        // Rollback
        List<OtcPrepWorkorderBinLocation> rollbackList = param.getDetailList().stream()
                .map(obj -> {
                    OtcPrepPutawaySlipDetail putawaySlipDetail = obj.getPutawaySlipDetail();
                    OtcPrepWorkorderBinLocation workorderBinLocation = wkBinLocationMap.get(putawaySlipDetail.getPrepWorkorderBinLocationId());
                    workorderBinLocation.setQty(workorderBinLocation.getQty() - obj.getPutawayQty());
                    // 扣减至0直接删除
                    boolean needRemove = workorderBinLocation.getQty() == 0;
                    workorderBinLocation.setDeletedNote(needRemove ? param.getNote() : null);
                    workorderBinLocation.setRemoveFlag(needRemove ? 1 : 0);

                    // 绑定
                    obj.setReadyToGoLockedId(workorderBinLocation.getBinLocationDetailLockedId());
                    return workorderBinLocation;
                })
                .toList();

        List<OtcPrepWorkorderBinLocation> updateList = rollbackList.stream().filter(obj -> obj.getRemoveFlag() == 0).toList();
        Validate.isTrue(otcWorkorderBinLocationService.updateBatch(updateList) == updateList.size(),
                "Update WorkOrderBinLocation qty is fail"
        );

        List<Long> removeIds = rollbackList.stream().filter(obj -> obj.getRemoveFlag() == 1).map(IdModel::getId).toList();
        Validate.isTrue(otcWorkorderBinLocationService.removeByIds(removeIds) == removeIds.size(),
                "Delete WorkOrderBinLocation qty is fail"
        );
    }
}