package cn.need.cloud.biz.service.otb.pickingslip;

import cn.need.cloud.biz.model.bo.base.WorkorderProcessBO;
import cn.need.cloud.biz.model.bo.otb.putawayslip.OtbPrepPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.bo.otb.workorder.OtbWorkorderSplitBO;
import cn.need.cloud.biz.model.param.base.update.PickingSlipCancelUpdateParam;
import cn.need.cloud.biz.model.param.base.update.RollbackPutawayUnitsUpdateParam;
import cn.need.cloud.biz.model.param.otb.update.pickingslip.prep.OtbPrepPickingSlipUnpickCreateParam;
import cn.need.cloud.biz.model.vo.base.PrepUnpickVO;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderRollbackPutawayUnitsVO;

import java.util.List;

/**
 * <p>
 * OTC预提货单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbPrepPickingSlipSpecialService {

    /**
     * 触发流程
     *
     * @param query start条件
     */
    void processTriggering(WorkorderProcessBO query);

    /**
     * unpick
     *
     * @param query unpick条件
     */
    void unpick(OtbPrepPickingSlipUnpickCreateParam query);

    /**
     * unpick 拣货单信息
     *
     * @param workorderId 工单信息
     * @return /
     */
    PrepUnpickVO unpickByWorkorderId(Long workorderId);

    /**
     * Rollback
     *
     * @param param 上架参数
     */
    void rollback(OtbPrepPutawaySlipPutAwayBO param);

    /**
     * 拣货单Rollback
     *
     * @param param Rollback参数
     * @return /
     */
    boolean batchCancel(PickingSlipCancelUpdateParam param);

    /**
     * Prep拣货单混滚上架数量
     *
     * @param param 回滚参数
     * @return /
     */
    boolean rollbackPutAwayUnits(RollbackPutawayUnitsUpdateParam param);

    /**
     * Prep拣货单混滚上架数量列表
     *
     * @param prepWorkorderId Prep工单id
     * @return /
     */
    PrepWorkorderRollbackPutawayUnitsVO rollbackPutAwayUnitsList(Long prepWorkorderId);

    /**
     * 拆单
     *
     * @param splitHolders splitHolders
     */
    void split(List<OtbWorkorderSplitBO> splitHolders);
}