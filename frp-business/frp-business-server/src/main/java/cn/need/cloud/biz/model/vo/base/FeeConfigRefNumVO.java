package cn.need.cloud.biz.model.vo.base;

import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * OTC预提货单 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
public class FeeConfigRefNumVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 5999118679518902314L;
    /**
     * 主键id
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 仓库报价id
     */
    @Schema(description = "仓库报价id")
    private Long quoteId;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * getShowString
     */
    public String getShowString() {

        if (ObjectUtil.isEmpty(this.getRefNum())) {
            return StringUtil.format("{} Id: {}", this.getClass().getSimpleName(), this.getId());
        }

        return StringUtil.format("{} RefNum: {}", this.getClass().getSimpleName(), this.getRefNum());
    }
}