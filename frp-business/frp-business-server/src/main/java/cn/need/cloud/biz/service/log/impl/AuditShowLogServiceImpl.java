package cn.need.cloud.biz.service.log.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.log.AuditShowLogConverter;
import cn.need.cloud.biz.mapper.log.AuditShowLogMapper;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.query.log.AuditShowLogQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.log.AuditShowLogPageVO;
import cn.need.cloud.biz.model.vo.log.AuditShowLogVO;
import cn.need.cloud.biz.service.log.AuditShowLogService;
import cn.need.cloud.biz.util.DropListUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
@Service
@DS("log_master")
public class AuditShowLogServiceImpl extends SuperServiceImpl<AuditShowLogMapper, AuditShowLog> implements AuditShowLogService {

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public int insert(AuditShowLog entity) {
        return mapper.insert(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public int insertBatch(Collection<? extends AuditShowLog> collection) {
        return super.insertBatch(collection);
    }

    @Override
    public List<AuditShowLogPageVO> listByQuery(AuditShowLogQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<AuditShowLogPageVO> pageByQuery(PageSearch<AuditShowLogQuery> search) {
        Page<AuditShowLog> page = Conditions.page(search, entityClass);
        List<AuditShowLogPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public AuditShowLogVO detailById(Long id) {
        AuditShowLog entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in AuditShowLog");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "AuditShowLog", id));
        }
        return buildAuditShowLogVO(entity);
    }

    @Override
    public List<DropProVO> distinctValue(AuditShowLogQuery auditShowLogQuery) {
        return DropListUtil.dropProList(
                auditShowLogQuery.getColumnNameList(),
                AuditShowLog.class,
                columnNameList -> mapper.dropProList(columnNameList, auditShowLogQuery)
        );
    }

    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 构建VO对象
     *
     * @param entity 对象
     * @return 返回包含详细信息的VO对象
     */
    private AuditShowLogVO buildAuditShowLogVO(AuditShowLog entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的VO对象
        return Converters.get(AuditShowLogConverter.class).toVO(entity);
    }

}
