package cn.need.cloud.biz.service.fee;

import cn.need.cloud.biz.model.entity.base.RefNumModel;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.fee.BaseFeeVO;
import cn.need.cloud.biz.service.base.RefNumService;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.support.util.SpringUtil;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * RefNumService
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
public interface FeeService<T extends RefNumModel, S extends SuperService<T>> extends
        RefNumService<T, S> {

    default void fillFod(BaseFeeVO fee) {
        fillFod(List.of(fee));
    }

    default void fillFod(Collection<? extends BaseFeeVO> feeList) {
        final List<Long> fodIds = feeList.stream()
                .map(BaseFeeVO::getFeeOriginalDataId)
                .filter(ObjectUtil::isNotEmpty)
                .distinct()
                .toList();

        FeeOriginalDataService feeOriginalDataService = SpringUtil.getBean(FeeOriginalDataService.class);

        if (feeOriginalDataService == null) {
            throw new BusinessException(StringUtil.format("{} FeeOriginalDataService is null", "FeeService"));
        }

        final Map<Long, RefNumVO> fodList = feeOriginalDataService.refNumMapByIds(fodIds);
        for (var feeInboundPageVO : feeList) {
            RefNumVO refNumVO = fodList.get(feeInboundPageVO.getFeeOriginalDataId());
            feeInboundPageVO.setFeeOriginalData(refNumVO);
        }
    }

}
