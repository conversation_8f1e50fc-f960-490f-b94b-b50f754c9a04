package cn.need.cloud.biz.model.vo.otc.pickingslip;

import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.otc.pkg.OtcPackageLabelVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;


/**
 * OTC拣货单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTC拣货单 发货包裹-包裹信息 vo对象")
public class OtcPickingSlipPiecePackageVO extends RefNumVO {

    @Serial
    private static final long serialVersionUID = 8942716668012115719L;

    /**
     * 流程类型
     */
    @Schema(description = "流程类型")
    private String processType;

    /**
     * 包裹状态
     */
    @Schema(description = "包裹状态")
    private String packageStatus;

    /**
     * 快递单号
     */
    @Schema(description = "快递单号")
    private String trackingNum;

    /**
     * 运输方式
     */
    @Schema(description = "运输方式")
    private String shipMethod;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    /**
     * 包裹标签信息
     */
    @Schema(description = "包裹标签信息")
    private List<OtcPackageLabelVO> labelList;

    /**
     * 包裹详情信息
     */
    @Schema(description = "包裹标签信息")
    private List<OtcPickingSlipPiecePackageDetailVO> detailList;

    /**
     * 多盒包裹编码
     */
    @Schema(description = "多盒包裹编码")
    private String packageMultiboxUpc;

    /**
     * 多盒包裹行号
     */
    @Schema(description = "多盒包裹行号")
    private Integer packageMultiboxLineNum;

    /**
     * 多盒包裹产品版本id
     */
    @Schema(description = "多盒包裹产品版本id")
    private Long packageMultiboxProductId;

    /**
     * 多盒包裹版本
     */
    @Schema(description = "多盒包裹版本")
    private Integer packageMultiboxVersionInt;


}