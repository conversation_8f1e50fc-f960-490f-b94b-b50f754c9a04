package cn.need.cloud.biz.service.otc.putawayslip.impl;

import cn.need.cloud.biz.cache.bean.BinLocationCache;
import cn.need.cloud.biz.client.constant.ErrorConstant;
import cn.need.cloud.biz.client.constant.enums.base.PutAwaySlipStatus;
import cn.need.cloud.biz.mapper.otc.OtcPrepPutawaySlipMapper;
import cn.need.cloud.biz.model.bo.otc.pickingslip.OtcPrepPickingSlipUnpickBO;
import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPrepPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPrepPutawaySlipPutAwayDetailBO;
import cn.need.cloud.biz.model.entity.base.putawayslip.PrepPutawaySlipModel;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPutawaySlip;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPutawaySlipDetail;
import cn.need.cloud.biz.model.param.base.update.PutawaySlipCancelUpdateParam;
import cn.need.cloud.biz.model.param.otc.update.putawayslip.OtcPrepPutawaySlipPutAwayUpdateParam;
import cn.need.cloud.biz.model.query.otc.putawayslip.OtcPrepPutawaySlipQuery;
import cn.need.cloud.biz.model.vo.base.BaseBinLocationVO;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.base.putawayslip.PrepPutawaySlipConfirmDetailVO;
import cn.need.cloud.biz.model.vo.base.putawayslip.PrepPutawaySlipConfirmVO;
import cn.need.cloud.biz.model.vo.otc.page.OtcPrepPutawaySlipPageVO;
import cn.need.cloud.biz.model.vo.otc.putawayslip.OtcPrepPutawaySlipDetailVO;
import cn.need.cloud.biz.model.vo.otc.putawayslip.OtcPrepPutawaySlipVO;
import cn.need.cloud.biz.service.binlocation.BinLocationSpecialService;
import cn.need.cloud.biz.service.helper.PutawaySlipHelper;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipSpecialService;
import cn.need.cloud.biz.service.otc.putawayslip.OtcPrepPutawaySlipDetailService;
import cn.need.cloud.biz.service.otc.putawayslip.OtcPrepPutawaySlipService;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderBinLocationSpecialService;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderService;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderSpecialService;
import cn.need.cloud.biz.util.BinLocationCacheUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * <p>
 * OTC上架单 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Service
public class OtcPrepPutawaySlipServiceImpl extends SuperServiceImpl<OtcPrepPutawaySlipMapper, OtcPrepPutawaySlip> implements OtcPrepPutawaySlipService {

    @Resource
    private OtcPrepPutawaySlipDetailService otcPrepPutawaySlipDetailService;
    @Resource
    private BinLocationSpecialService binLocationSpecialService;
    @Resource
    private OtcPrepWorkorderService otcPrepWorkorderService;
    @Resource
    private OtcPrepPickingSlipService otcPrepPickingSlipService;
    @Resource
    private OtcPrepPickingSlipSpecialService otcPrepPickingSlipSpecialService;
    @Resource
    private OtcPrepWorkorderBinLocationSpecialService otcPrepWorkorderBinLocationSpecialService;
    @Resource
    private OtcPrepWorkorderSpecialService otcPrepWorkorderSpecialService;

    @Override
    public PageData<OtcPrepPutawaySlipPageVO> pageByQuery(PageSearch<OtcPrepPutawaySlipQuery> search) {
        Page<OtcPrepPutawaySlip> page = Conditions.page(search, entityClass);
        List<OtcPrepPutawaySlipPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        this.fillPageList(dataList);
        return new PageData<>(dataList, page);
    }

    @Override
    public OtcPrepPutawaySlipVO detailById(Long id) {
        OtcPrepPutawaySlip entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in OtcPrepPutawaySlip");
        }
        return buildOtcPrepPutawaySlipVO(entity);
    }

    @Override
    public OtcPrepPutawaySlipVO detailByRefNum(String refNum) {
        OtcPrepPutawaySlip entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("RefNum: " + refNum + " not found in OtcPrepPutawaySlip");
        }
        return buildOtcPrepPutawaySlipVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancel(PutawaySlipCancelUpdateParam param) {
        this.cancelPutaway(param);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean putAway(OtcPrepPutawaySlipPutAwayUpdateParam putawayParam) {

        OtcPrepPutawaySlipPutAwayBO param = BeanUtil.copyNew(putawayParam, OtcPrepPutawaySlipPutAwayBO.class);
        param.setDetailList(BeanUtil.copyNew(putawayParam.getDetailList(), OtcPrepPutawaySlipPutAwayDetailBO.class));

        // 检查上架
        OtcPrepPutawaySlip putawaySlip = this.getById(param.getId());
        Validate.notNull(putawaySlip, "PutAwaySlipId {} is not exist", param.getId());

        Validate.isTrue(PutAwaySlipStatus.canPutAway(putawaySlip.getPutawaySlipStatus()),
                ErrorConstant.STATUS_ERROR_FORMAT, putawaySlip.refNumLog(), "checkAndPutaway",
                PutAwaySlipStatus.canPutAwayStatuses(), putawaySlip.getPutawaySlipStatus()
        );
        param.setPutawaySlip(putawaySlip);

        List<OtcPrepPutawaySlipDetail> details = otcPrepPutawaySlipDetailService.listByPutawaySlipId(param.getId());
        // 校验上架
        PutawaySlipHelper.checkAndPutaway(param, details);

        // 拣货单 Rollback
        otcPrepPickingSlipSpecialService.rollback(param);

        // 工单 Rollback
        otcPrepWorkorderSpecialService.rollback(param);

        // 工单分配仓储 Rollback
        otcPrepWorkorderBinLocationSpecialService.rollback(param);

        // 库位 Rollback
        binLocationSpecialService.rollback(param.getDetailList());

        Validate.isTrue(super.update(putawaySlip) == 1, "Update PutAwaySlip PutAwayQty is fail");
        Validate.isTrue(otcPrepPutawaySlipDetailService.updateBatch(details) == details.size(),
                "Update PutAwaySlip PutAwayQty is fail"
        );

        return true;
    }

    @Override
    public void unpick(OtcPrepPickingSlipUnpickBO query) {
        query.setPutawaySlipRefNumType(RefNumTypeEnum.OTC_PREP_PUT_AWAY_SLIP);
        this.unpick(query, (putawaySlip, putawaySlipDetailList) -> {
            otcPrepPutawaySlipDetailService.insertBatch(putawaySlipDetailList);
            super.insert(putawaySlip);
        });
    }

    @Override
    public List<PrepPutawaySlipConfirmDetailVO> confirmDetailList(List<Long> prepWorkorderIds) {
        List<OtcPrepPutawaySlip> prepPutawaySlipList = this.listByPrepWorkorderIds(prepWorkorderIds);
        Map<Long, OtcPrepPutawaySlip> prepPutawaySlipMap = StreamUtils.toMap(prepPutawaySlipList, IdModel::getId);

        List<Long> prepPutawaySlipIds = StreamUtils.distinctMap(prepPutawaySlipList, IdModel::getId);

        Map<Long, List<OtcPrepPutawaySlipDetail>> detailsMap = otcPrepPutawaySlipDetailService.groupByPrepPutawaySlipId(prepPutawaySlipIds);

        return detailsMap.entrySet()
                .stream()
                .flatMap(entry -> {
                    OtcPrepPutawaySlip putawaySlip = prepPutawaySlipMap.get(entry.getKey());
                    return entry.getValue()
                            .stream()
                            .map(detail -> {
                                PrepPutawaySlipConfirmDetailVO rollback = BeanUtil.copyNew(detail, PrepPutawaySlipConfirmDetailVO.class);
                                rollback.setPrepPutawaySlip(BeanUtil.copyNew(putawaySlip, PrepPutawaySlipConfirmVO.class));
                                rollback.setBinLocationId(detail.getSourceBinLocationId());
                                return rollback;
                            });
                })
                .toList();
    }

    @Override
    public List<OtcPrepPutawaySlip> listByPrepWorkorderIds(List<Long> prepWorkorderIds) {
        if (ObjectUtil.isEmpty(prepWorkorderIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(PrepPutawaySlipModel::getPrepWorkorderId, prepWorkorderIds).list();
    }

    /**
     * 构建OTC上架单VO对象
     *
     * @param entity OTC上架单对象
     * @return 返回包含详细信息的OTC上架单VO对象
     */
    private OtcPrepPutawaySlipVO buildOtcPrepPutawaySlipVO(OtcPrepPutawaySlip entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }

        // 构建detail
        List<OtcPrepPutawaySlipDetailVO> details = BeanUtil.copyNew(
                otcPrepPutawaySlipDetailService.listByPutawaySlipId(entity.getId()),
                OtcPrepPutawaySlipDetailVO.class
        );
        // 填充仓库
        List<Long> binLocationIds = Stream.concat(
                        details.stream().map(OtcPrepPutawaySlipDetailVO::getSourceBinLocationId),
                        details.stream().map(OtcPrepPutawaySlipDetailVO::getDestBinLocationId)
                )
                .distinct().toList();
        Map<Long, BinLocationCache> cacheMap = StreamUtils.toMap(BinLocationCacheUtil.listByIds(binLocationIds), BinLocationCache::getId);
        details.forEach(obj -> {
            obj.setSourceBaseBinLocationVO(BeanUtil.copyNew(cacheMap.get(obj.getSourceBinLocationId()), BaseBinLocationVO.class));
            obj.setDestBaseBinLocationVO(BeanUtil.copyNew(cacheMap.get(obj.getDestBinLocationId()), BaseBinLocationVO.class));
        });

        // 返回包含详细信息的OTC上架单VO对象
        OtcPrepPutawaySlipVO vo = BeanUtil.copyNew(entity, OtcPrepPutawaySlipVO.class);
        vo.setPrepPickingSlip(otcPrepPickingSlipService.refNumById(entity.getPrepPickingSlipId()));
        vo.setPrepWorkorder(otcPrepWorkorderService.refNumById(entity.getPrepWorkorderId()));
        vo.setDetailList(details);
        return vo;
    }

    /**
     * 填充字段
     *
     * @param dataList 列表
     */
    private void fillPageList(List<OtcPrepPutawaySlipPageVO> dataList) {
        List<Long> workorderIds = StreamUtils.distinctMap(dataList, OtcPrepPutawaySlipPageVO::getPrepWorkorderId);
        List<Long> pickingSlipIds = StreamUtils.distinctMap(dataList, OtcPrepPutawaySlipPageVO::getPrepPickingSlipId);

        Map<Long, RefNumVO> wkMap = otcPrepWorkorderService.refNumMapByIds(workorderIds);
        Map<Long, RefNumVO> psMap = otcPrepPickingSlipService.refNumMapByIds(pickingSlipIds);

        dataList.forEach(obj -> {
            obj.setPrepWorkorder(wkMap.get(obj.getPrepWorkorderId()));
            obj.setPrepPickingSlip(psMap.get(obj.getPrepPickingSlipId()));
        });
    }

    /**
     * 检查并上架
     *
     * @param param 上架参数
     */
    private void checkAndPutaway(OtcPrepPutawaySlipPutAwayBO param) {


    }
}
