package cn.need.cloud.biz.model.query.product;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 产品 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "产品 query对象")
public class ProductQuery extends SuperQuery {

    /**
     * 交易伙伴ID
     */
    @Schema(description = "交易伙伴ID")
    private Long transactionPartnerId;

    /**
     * 交易伙伴ID
     */
    @Schema(description = "交易伙伴ID集合")
    @Condition(value = Keyword.IN, fields = {"transactionPartnerId"})
    private List<Long> transactionPartnerIdList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private List<String> refNumList;

    /**
     * 供应商SKU
     */
    @Schema(description = "供应商SKU")
    private String supplierSku;

    /**
     * 供应商SKU
     */
    @Schema(description = "供应商SKU集合")
    @Condition(value = Keyword.IN, fields = {"supplierSku"})
    private List<String> supplierSkuList;

    /**
     * UPC码
     */
    @Schema(description = "UPC码")
    private String upc;

    /**
     * UPC码
     */
    @Schema(description = "UPC码集合")
    @Condition(value = Keyword.IN, fields = {"upc"})
    private List<String> upcList;

    /**
     * 组装产品标志
     */
    @Schema(description = "组装产品标志")
    private Boolean assemblyProductFlag;

//    /**
//     * 备注
//     */
//    @Schema(description = "备注")
//    private String note;
//
//    /**
//     * 描述
//     */
//    @Schema(description = "描述")
//    private String description;
//
//    /**
//     * 标题
//     */
//    @Schema(description = "标题")
//    private String title;
//
//    /**
//     * 乐观锁版本号
//     */
//    @Schema(description = "乐观锁版本号")
//    private Long version;
//
//
//    /**
//     * 删除原因
//     */
//    @Schema(description = "删除原因")
//    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 多箱标志
     */
    @Schema(description = "多箱标志")
    private Boolean multiboxFlag;


    /**
     * 组类型
     */
    @Schema(description = "组类型")
    private String groupType;

    /**
     * 组类型
     */
    @Schema(description = "组类型集合")
    @Condition(value = Keyword.IN, fields = {"groupType"})
    private List<String> groupTypeList;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private List<String> productTypeList;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String productType;


}