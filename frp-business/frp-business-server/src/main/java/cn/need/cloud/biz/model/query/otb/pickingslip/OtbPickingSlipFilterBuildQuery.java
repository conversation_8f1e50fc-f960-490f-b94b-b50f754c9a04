package cn.need.cloud.biz.model.query.otb.pickingslip;

import cn.need.cloud.biz.client.constant.enums.base.BuildFromType;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkOrderListQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/***
 * 构建拣货单条件
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Data
@Schema(description = " Otb构建拣货单 query对象")
public class OtbPickingSlipFilterBuildQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 5819651255625596965L;
    /***
     * 构建拣货单工单条件
     */
    @Schema(description = "构建拣货单工单条件")
    private OtbWorkOrderListQuery filter;

    /**
     * 拣货位置
     */
    @Schema(description = "拣货位置")
    private String pickToStation;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 构建拣货单来源
     */
    @Schema(description = "构建拣货单来源", hidden = true)
    private BuildFromType buildFromType = BuildFromType.OTB_WORK_ORDER;
}
