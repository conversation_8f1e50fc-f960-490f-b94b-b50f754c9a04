package cn.need.cloud.biz.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * RequestListQuery
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@Schema(description = "请求单集合对象")
public class RequestListQuery implements Serializable {

    @Schema(description = "工单id集合")
    @NotEmpty(message = "Request idList is not empty")
    @NotNull(message = "Request idList is not null")
    private List<Long> idList;
}
