package cn.need.cloud.biz.model.vo.base.aware;

import cn.need.cloud.biz.model.vo.base.BaseProductVersionVO;
import cn.need.framework.common.core.lang.ObjectUtil;
import lombok.Getter;
import lombok.Setter;

/**
 * 产品版本展示对象
 * <p>
 * 子类中不能包含 productVersionId
 * </p>
 *
 * <AUTHOR>
 */
public class BaseProductVersionShowVO implements BaseProductVersionAware {
    /**
     * 产品版本id
     */
    @Getter
    @Setter
    private Long productVersionId;

    /**
     * 产品版本
     */
    @Setter(lombok.AccessLevel.PRIVATE)
    private BaseProductVersionVO baseProductVersionVO;

    @Override
    public BaseProductVersionVO getBaseProductVersionVO() {
        if (ObjectUtil.isEmpty(productVersionId)) {
            return null;
        }
        if (ObjectUtil.isNotEmpty(baseProductVersionVO)) {
            return baseProductVersionVO;
        }
        // Retrieve from cache once and store the result
        baseProductVersionVO = BaseProductVersionAware.super.getBaseProductVersionVO();
        return baseProductVersionVO;
    }
}
