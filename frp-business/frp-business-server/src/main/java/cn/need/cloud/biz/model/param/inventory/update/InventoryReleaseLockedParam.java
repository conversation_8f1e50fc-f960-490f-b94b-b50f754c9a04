package cn.need.cloud.biz.model.param.inventory.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/***
 * InventoryReleaseLockedParam.java
 *
 * <AUTHOR>
 * @since 2024-11-18
 */
@Data
@Schema(description = "释放库存Param对象")
public class InventoryReleaseLockedParam implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private Long id;


    /**
     * 释放库存数量
     */
    @Schema(description = "释放库存数量")
    private Integer qty;
}
