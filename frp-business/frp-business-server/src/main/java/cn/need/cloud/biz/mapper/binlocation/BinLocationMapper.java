package cn.need.cloud.biz.mapper.binlocation;

import cn.need.cloud.biz.model.bo.common.DropColumnInfoBO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocation;
import cn.need.cloud.biz.model.query.binlocation.BinLocationQuery;
import cn.need.cloud.biz.model.query.binlocation.BinLocationWithDetailQuery;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkOrderListBinLocationQuery;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationVO;
import cn.need.cloud.biz.model.vo.page.BinLocationPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 库位 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface BinLocationMapper extends SuperMapper<BinLocation> {

    /**
     * 根据条件获取库位列表
     *
     * @param query 查询条件
     * @return 库位集合
     */
    Set<Long> list(@Param("b") OtbWorkOrderListBinLocationQuery query);

    /**
     * 根据条件获取库位分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 库位集合
     */
    List<BinLocationPageVO> listByQuery(@Param("qo") BinLocationQuery query, @Param("page") Page<?> page);

    /**
     * 下拉列表
     *
     * @param columnList 查询字段名
     * @param qo         查询条件
     * @return 下拉列表
     */
    List<Map<String, Object>> dropProList(@Param("columnList") List<DropColumnInfoBO> columnList,
                                          @Param("qo") BinLocationQuery qo);

    /**
     * 带详情条件查询
     *
     * @param qo 查询条件
     * @return vo集合
     */
    List<BinLocationVO> listWithDetailByQuery(@Param("qo") BinLocationWithDetailQuery qo);
}