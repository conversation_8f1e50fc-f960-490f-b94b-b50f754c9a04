package cn.need.cloud.biz.service.base;

import cn.need.cloud.biz.client.constant.enums.base.PrepWordorderTypeEnum;
import cn.need.cloud.biz.client.constant.enums.base.PrepWorkOrderDetailTypeEnum;
import cn.need.cloud.biz.model.bo.base.BaseModelInventoryReserveBO;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailLockedChangeBO;
import cn.need.cloud.biz.model.bo.common.WorkOrderMembershipBO;
import cn.need.cloud.biz.model.entity.base.BaseWorkorderBinLocationModel;
import cn.need.cloud.biz.model.entity.base.PrepWorkorderDetailModel;
import cn.need.cloud.biz.model.entity.base.ProductModel;
import cn.need.cloud.biz.model.entity.binlocation.BinLocation;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.entity.product.ProductGroup;
import cn.need.cloud.biz.model.query.base.BaseBinLocationQuery;
import cn.need.cloud.biz.model.query.product.ProductTreeQuery;
import cn.need.cloud.biz.model.vo.base.BasePickVO;
import cn.need.cloud.biz.model.vo.base.ProductBinLocationPickVO;
import cn.need.cloud.biz.model.vo.base.ReadyToGoProductBinLocationPickVO;
import cn.need.cloud.biz.model.vo.base.product.*;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderDetailPutAwayVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkOrderNoEnoughAvailQtyVO;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.model.SuperModel;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通用操作Service
 *
 * <AUTHOR>
 * @since 2024-11-09
 */
public interface PickingSlipService {

    /**
     * 计算是否可上架，并扣减拣货数量
     *
     * @param productPickMap 产品拣货数量
     * @param groupList      可替换Group
     * @param mainProductId  所需产品id
     * @param mainQty        所需总数量
     * @return /
     */
    private static boolean canPutAway(
            Map<Long, Integer> productPickMap, List<PrepGroupVO> groupList, Long mainProductId, int mainQty) {
        // 主产品拣货总数量
        int mainPickQty = productPickMap.getOrDefault(mainProductId, 0);
        // 需要扣除的数量
        int needQty = Math.min(mainPickQty, mainQty);
        // 扣除所需的产品
        productPickMap.put(mainProductId, mainPickQty - needQty);
        // 还剩余数量
        AtomicInteger remainingQty = new AtomicInteger(mainQty - needQty);
        // 带Group的补齐数量
        ObjectUtil.emptyToDefault(groupList, Collections.<PrepGroupVO>emptyList())
                .stream()
                .filter(group -> remainingQty.get() > 0)
                .forEach(group -> {
                    // group 拣货数量
                    int subPickQty = productPickMap.getOrDefault(group.getChildProductId(), 0);
                    // group 所需数量
                    int subNeedQty = Math.min(subPickQty, remainingQty.get());
                    // 扣除所需的产品
                    productPickMap.put(group.getChildProductId(), subPickQty - subNeedQty);
                    // 扣除group替换的数量
                    remainingQty.addAndGet(-subNeedQty);
                });

        // 子Combo产品所需数量足够可上架
        return remainingQty.get() == 0;
    }

    /**
     * 释放库位锁
     *
     * @param pickList 库位锁详情
     */
    <T extends ReadyToGoProductBinLocationPickVO> void otcMoveBinLocationInventoryToReadyToGo(List<T> pickList);

    /**
     * 释放库位锁
     *
     * @param pickList 库位锁详情
     */
    <T extends ReadyToGoProductBinLocationPickVO> void otbMoveBinLocationInventoryToReadyToGo(List<T> pickList);

    /**
     * 释放库位锁
     *
     * @param pickList 库位锁详情
     */
    <T extends ReadyToGoProductBinLocationPickVO> void moveBinLocationInventoryToReadyToGo(List<T> pickList, BinLocation readyToGo);

    /**
     * 校验头行信息产品库存逻辑封装,并返回不足库存集合
     *
     * @param detailMap                  详情行与头的映射
     * @param productIdGetter            根据详情信息获取产品id的函数
     * @param needQtyGetter              根据详情信息获取产品所需数量的函数
     * @param hasStockHeaderIdListGetter 拥有库存的头id集合获取器
     * @param noEnoughConsumer           库存不足消费
     * @return 不足库存集合
     */
    <Detail> List<WorkOrderNoEnoughAvailQtyVO> findNoEnoughList(
            Map<Long, List<Detail>> detailMap,
            Map<Long, Integer> productInStockMap,
            Function<Detail, Long> productIdGetter,
            Function<Detail, Integer> needQtyGetter,
            Function<Map<Long, Integer>, List<Long>> hasStockHeaderIdListGetter,
            BiConsumer<WorkOrderNoEnoughAvailQtyVO, Detail> noEnoughConsumer);

    <Detail extends ProductModel> List<WorkOrderNoEnoughAvailQtyVO> findNoEnoughList(
            Map<Long, List<Detail>> detailMap,
            Map<Long, List<BinLocationDetail>> productInStockMap,
            Function<Map<Long, Integer>, List<Long>> hasStockHeaderIdListGetter,
            BiConsumer<WorkOrderNoEnoughAvailQtyVO, Detail> noEnoughConsumer);

    /**
     * 获取由谁拣货类型
     *
     * @param binLocationIdList   当前拣货的库位ID集合
     * @param currentPickFromType 当前拣货类型
     * @return /
     */
    String getPickFromType(List<Long> binLocationIdList, String currentPickFromType);

    /**
     * 处理所有已拣货的逻辑
     *
     * @param pickList                  拣货列表
     * @param headerIdFunction          工单id获取
     * @param detailGroupByProductIdMap 详情映射，产品分组
     * @param allPickedConsumer         是否拣货完成的消费
     */
    <Detail extends ProductModel> void dealWithAllPicked(
            List<? extends BasePickVO> pickList,
            Function<Detail, Long> headerIdFunction,
            Map<Long, List<Detail>> detailGroupByProductIdMap,
            BiConsumer<Long, Boolean> allPickedConsumer);

    /**
     * 获取有库存的工单
     *
     * @param dataList                  工单列表
     * @param detailGroupByWorkOrderMap 详情
     * @return /
     */
    <WorkOrder extends SuperModel, WorkOrderDetail extends ProductModel> List<Long> filterHasInStockWorkOrder(
            List<WorkOrder> dataList,
            Map<Long, List<WorkOrderDetail>> detailGroupByWorkOrderMap,
            BaseBinLocationQuery binLocationQuery);

    /**
     * 统一返回HowToDo 描述
     *
     * @param prepProduct 产品树
     * @return /
     */
    default String getHowToDoDescription(PrepFullProductVO prepProduct) {
        // 转成前端所需要的
        switch (Objects.requireNonNull(prepProduct).getType()) {
            // 统一返回对象即可
            case PREP_CONVERT, PREP_MULTI_BOX, PREP_CONVERT_MULTI_BOX -> {
                return JsonUtil.toJson(prepProduct);
            }
            // 这里就字段不一致，需要将ProductId改成ComponentProductId
            case PREP_PACK -> {
                PrepPackProductVO prepPack = BeanUtil.copyNew(prepProduct, PrepPackProductVO.class);
                prepPack.setComponentProductId(prepProduct.getProductId());
                prepPack.setComponentList(BeanUtil.copyNew(prepProduct.getComponentList(), PrepComponentVO.class));
                return JsonUtil.toJson(prepPack);
            }
            // 这里就字段不一致，需要将ProductId改成ComponentProductId
            case PREP_CONVERT_PACK -> {
                PrepConvertPackProductVO prepConvertPackProduct = BeanUtil.copyNew(prepProduct, PrepConvertPackProductVO.class);
                prepConvertPackProduct.setComponentProductId(prepProduct.getProductId());
                return JsonUtil.toJson(prepProduct);
            }
            default ->
                    throw new BusinessException(String.format("Unsupported preprocessing ticket type: %s", prepProduct.getType()));
        }
    }

    /**
     * 通过工单父子关系来获取产品Group
     *
     * @param memberships memberships
     * @return /
     */
    Map<Long, List<ProductGroup>> findGroupsWithMembership(List<WorkOrderMembershipBO> memberships);

    /**
     * 释放锁并扣减库存
     *
     * @param changeList 变更信息
     */
    void releaseLockAndReduceInStock(Collection<BinLocationDetailLockedChangeBO> changeList);

    /**
     * 通过工单父子关系已经产品树查询条件构建产品树
     *
     * @param memberships memberships
     * @return /
     */
    PrepFullProductVO findAndFillWithPrepConvert(ProductTreeQuery productTreeQuery, List<WorkOrderMembershipBO> memberships);

    /**
     * 填充Group信息
     *
     * @param productTree 产品树
     * @param detailList  父子关系集合
     */
    void fillWithPrepConvert(PrepFullProductVO productTree, List<WorkOrderMembershipBO> detailList);

    /**
     * 释放工单虚拟库位锁
     *
     * @param lockedGroupByWkDetailMap 虚拟库位工单锁
     */
    void releaseWorkorderVirtualLocked(Map<Long, List<BinLocationDetailLocked>> lockedGroupByWkDetailMap);

    /**
     * 上架释放库位锁
     *
     * @param wkDetailPutAwayList          工单详情上架信息
     * @param prepWorkorderBinLocationList Prep工单分配仓储信息
     * @param releaseLockedGroupFunction   释放锁分组id获取函数
     * @param changeConsumer               库位日志获取函数
     */
    <T extends BaseWorkorderBinLocationModel, R extends PrepWorkorderDetailPutAwayVO> void putAwayReleaseLocked(
            List<R> wkDetailPutAwayList,
            List<T> prepWorkorderBinLocationList,
            Function<T, Long> releaseLockedGroupFunction,
            BiConsumer<Long, BinLocationDetailLockedChangeBO> changeConsumer);

    /**
     * 记录库位日志，分配工单库位信息的库位变化的库位日志记录
     *
     * @param prepWorkorderBinLocationList 工单分配仓储信息
     * @param releaseLockedGroupFunction   释放锁分组id获取函数
     * @param allocatedQtyFunction         工单分配数量获取函数
     * @param changeConsumer               库位日志获取函数
     */
    <T extends BaseWorkorderBinLocationModel> void workorderBinLocationReleaseLockedAndReduceInStock(
            List<T> prepWorkorderBinLocationList,
            Function<T, Long> releaseLockedGroupFunction,
            Function<Long, Integer> allocatedQtyFunction,
            BiConsumer<Long, BinLocationDetailLockedChangeBO> changeConsumer);

    /**
     * 获取可上架数量
     *
     * @param pickList    拣货详情信息
     * @param productTree 产品树
     * @return /
     */
    default int getCanPutAwayQty(List<? extends ProductBinLocationPickVO> pickList, PrepFullProductVO productTree) {
        if (ObjectUtil.isNull(productTree)) {
            return 0;
        }
        // 通过不同类型获取不同计算方式
        return this.getCanPutAwayQtyProcessMap().get(productTree.getType()).apply(pickList, productTree);
    }

    /**
     * PrepConvert类型，获取可上架数量
     *
     * @param pickList 拣货详情信息
     * @return /
     */
    default int getPreConvertCanPutAwayQty(List<? extends ProductBinLocationPickVO> pickList) {
        return pickList.stream()
                .mapToInt(BasePickVO::getPickedQty)
                .sum();
    }

    /**
     * PrepPack、PrepConvertPack类型，获取可上架数量
     *
     * @param pickList    拣货详情信息
     * @param productTree 产品树
     * @return /
     */
    default int getPrePackCanPutAwayQty(List<? extends ProductBinLocationPickVO> pickList, PrepFullProductVO productTree) {
        // 产品拣货数量
        Map<Long, Integer> productPickMap = pickList.stream()
                .collect(Collectors.groupingBy(
                        // 产品分组
                        ProductBinLocationPickVO::getProductId,
                        // 拣货产品数量
                        Collectors.summingInt(ProductBinLocationPickVO::getPickedQty)
                ));

        // 使用分配策略
        int canPutAwayQty = -1;
        boolean canPutAway;
        do {
            // 所有子Combo都可上架，整个即可上架
            canPutAway = ObjectUtil.nullToDefault(productTree.getComponentList(), Collections.<PrepComponentGroupVO>emptyList())
                    .stream()
                    // 每个产品详情填充
                    .allMatch(detail -> canPutAway(productPickMap, detail.getGroupList(), detail.getComponentProductId(), detail.getComponentQty()));
            canPutAwayQty++;
        } while (canPutAway);

        return canPutAwayQty;
    }

    /**
     * PrepMultiBox、PrepConvertMultiBox类型，获取可上架数量
     *
     * @param pickList    拣货详情信息
     * @param productTree 产品树
     * @return /
     */
    default int getPreMultiBoxCanPutAwayQty(List<? extends ProductBinLocationPickVO> pickList, PrepFullProductVO productTree) {
        // 产品拣货数量
        Map<Long, Integer> productPickMap = pickList.stream()
                .collect(Collectors.groupingBy(
                        // 产品分组
                        ProductBinLocationPickVO::getProductId,
                        // 拣货产品数量
                        Collectors.summingInt(ProductBinLocationPickVO::getPickedQty)
                ));

        // 使用分配策略
        int canPutAwayQty = -1;
        boolean canPutAway;
        do {
            // 所有子Combo都可上架，整个即可上架
            canPutAway = ObjectUtil.nullToDefault(productTree.getMultiBoxList(), Collections.<PrepMultiBoxGroupVO>emptyList())
                    .stream()
                    .map(PrepMultiBoxGroupVO::getDetailList)
                    .flatMap(Collection::stream)
                    // 每个箱子详情填充
                    .allMatch(detail -> canPutAway(productPickMap, detail.getGroupList(), detail.getProductId(), detail.getQty()));
            canPutAwayQty++;
        } while (canPutAway);

        return canPutAwayQty;
    }

    /**
     * 更新拣货数量
     *
     * @param prepWorkOrderDetailList 工单详情
     * @param pickDetailUpdateList    需要更新详情信息
     */
    default <T extends PrepWorkorderDetailModel> List<T> updateDetailPickQty(List<T> prepWorkOrderDetailList, List<T> pickDetailUpdateList) {

        Map<Long, T> detailMap = StreamUtils.toMap(prepWorkOrderDetailList, IdModel::getId);

        // 需要更新的工单详情
        List<T> detailUpdateList = new ArrayList<>(pickDetailUpdateList);

        // Parent PickQty更新
        Map<Long, List<T>> groupByParentMap = prepWorkOrderDetailList
                .stream()
                // 不需要Convert
                .filter(obj -> !Objects.equals(obj.getPrepWorkorderDetailType(), PrepWorkOrderDetailTypeEnum.PREPCONVERT.getStatus()))
                // Child
                .filter(obj -> ObjectUtil.isNotNull(obj.getParentId()))
                .collect(Collectors.groupingBy(T::getParentId));
        // 更新Parent detail
        List<T> parentDetailUpdateList = groupByParentMap
                .entrySet()
                .stream()
                .filter(entry -> {
                    T parentDetail = detailMap.get(entry.getKey());
                    int pickedQty = parentDetail.getPickedQty();
                    parentDetail.setPickedQty(entry.getValue().stream()
                            // Parent.pickQty = Math.min { (Parent.qty * Child.pickQty) / Child.qty }
                            .mapToInt(obj -> (parentDetail.getQty() * obj.getPickedQty()) / obj.getQty())
                            .min()
                            .orElse(0)
                    );
                    return pickedQty != parentDetail.getPickedQty();
                })
                // 过滤有更新的Parent详情
                .map(entry -> detailMap.get(entry.getKey()))
                .toList();

        detailUpdateList.addAll(parentDetailUpdateList);

        return detailUpdateList;
    }

    /**
     * 释放Reserve锁
     *
     * @param reserveList 释放数量
     */
    void releaseReserveLocked(List<BaseModelInventoryReserveBO> reserveList);

    /**
     * 获取可FilterBuild 虚拟BinLocation，填充返回
     *
     * @param binLocationQuery         库位条件
     * @param productIdList            产品
     * @param lockedGroupByWkDetailMap 关联虚拟锁id
     * @return /
     */
    Map<Long, List<BinLocationDetail>> fullCanBuildVirtualBinLocation(
            BaseBinLocationQuery binLocationQuery,
            List<Long> productIdList,
            Map<Long, List<BinLocationDetailLocked>> lockedGroupByWkDetailMap);

    /**
     * 获取可上架数量处理器, 不同类型处理方式不同，放这里方便拓展
     *
     * @return 获取可上架数量
     */
    private Map<PrepWordorderTypeEnum, BiFunction<List<? extends ProductBinLocationPickVO>, PrepFullProductVO, Integer>> getCanPutAwayQtyProcessMap() {
        // 使用EnumMap替代HashMap，指定枚举类型
        EnumMap<PrepWordorderTypeEnum, BiFunction<List<? extends ProductBinLocationPickVO>, PrepFullProductVO, Integer>> map =
                new EnumMap<>(PrepWordorderTypeEnum.class);
        // 显式初始化并指定初始容量
        map.put(PrepWordorderTypeEnum.PREP_CONVERT, (picks, productTres) -> getPreConvertCanPutAwayQty(picks));
        map.put(PrepWordorderTypeEnum.PREP_CONVERT_PACK, this::getPrePackCanPutAwayQty);
        map.put(PrepWordorderTypeEnum.PREP_PACK, this::getPrePackCanPutAwayQty);
        map.put(PrepWordorderTypeEnum.PREP_MULTI_BOX, this::getPreMultiBoxCanPutAwayQty);
        map.put(PrepWordorderTypeEnum.PREP_CONVERT_MULTI_BOX, this::getPreMultiBoxCanPutAwayQty);
        return map;
    }


}
