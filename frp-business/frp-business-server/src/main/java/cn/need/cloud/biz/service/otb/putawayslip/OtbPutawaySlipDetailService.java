package cn.need.cloud.biz.service.otb.putawayslip;

import cn.need.cloud.biz.model.entity.otb.OtbPutawaySlipDetail;
import cn.need.cloud.biz.service.base.putawayslip.PutawaySlipDetailService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 上架详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
public interface OtbPutawaySlipDetailService extends PutawaySlipDetailService<OtbPutawaySlipDetail> {

    /**
     * 根据上架单分组
     *
     * @param headerIds 上架单
     * @return /
     */
    Map<Long, List<OtbPutawaySlipDetail>> groupByPutawaySlipId(List<Long> headerIds);
}