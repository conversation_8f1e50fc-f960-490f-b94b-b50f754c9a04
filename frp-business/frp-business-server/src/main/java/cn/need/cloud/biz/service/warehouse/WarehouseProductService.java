package cn.need.cloud.biz.service.warehouse;

import cn.need.cloud.biz.model.entity.warehouse.WarehouseProduct;
import cn.need.cloud.biz.model.param.warehouse.create.WarehouseProductCreateParam;
import cn.need.cloud.biz.model.param.warehouse.update.WarehouseProductCreateOrUpdateParam;
import cn.need.cloud.biz.model.query.warehouse.WarehouseProductQuery;
import cn.need.cloud.biz.model.vo.page.WarehouseProductPageVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseProductListVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseProductVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 产品即发货 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface WarehouseProductService extends SuperService<WarehouseProduct> {

    /**
     * 根据参数新增产品即发货
     *
     * @param createParam 请求创建参数，包含需要插入的产品即发货的相关信息
     * @return 产品即发货ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(WarehouseProductCreateParam createParam);


    /**
     * 根据参数新增或更新产品即发货
     *
     * @param param 请求创建参数，包含需要更新的产品即发货的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int createOrUpdate(WarehouseProductCreateOrUpdateParam param);


    /**
     * 根据查询条件获取产品即发货列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个产品即发货对象的列表(分页)
     */
    List<WarehouseProductPageVO> listByQuery(WarehouseProductQuery query);

    /**
     * 根据查询条件获取产品即发货列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个产品即发货对象的列表(分页)
     */
    PageData<WarehouseProductPageVO> pageByQuery(PageSearch<WarehouseProductQuery> search);

    /**
     * 根据ID获取产品即发货
     *
     * @param id 产品即发货ID
     * @return 返回产品即发货VO对象
     */
    WarehouseProductVO detailById(Long id);

    /**
     * 根据产品ID和仓库ID获取仓库产品信息
     *
     * @param productId   产品ID
     * @param warehouseId 仓库ID
     * @return 返回WarehouseProduct对象
     */
    WarehouseProduct getWarehouseProduct(Long productId, Long warehouseId);

    /**
     * 根据产品ID获取仓库产品列表
     *
     * @param productId 产品ID
     * @return 返回WarehouseProductListVO对象列表
     */
    List<WarehouseProductListVO> listByProductId(Long productId);

    /**
     * 通过产品id集合获取产品对象集合
     *
     * @param productIdList 产品id集合
     * @return /
     */
    Map<Long, WarehouseProduct> listByProductIdList(List<Long> productIdList);
}