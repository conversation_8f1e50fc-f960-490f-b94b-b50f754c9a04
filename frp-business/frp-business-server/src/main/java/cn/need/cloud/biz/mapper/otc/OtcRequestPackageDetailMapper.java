package cn.need.cloud.biz.mapper.otc;

import cn.need.cloud.biz.model.entity.otc.OtcRequestPackageDetail;
import cn.need.cloud.biz.model.query.otc.request.OtcRequestPackageDetailQuery;
import cn.need.cloud.biz.model.vo.otc.page.OtcRequestPackageDetailPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * OTC请求包裹详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtcRequestPackageDetailMapper extends SuperMapper<OtcRequestPackageDetail> {

    /**
     * 根据条件获取OTC请求包裹详情列表
     *
     * @param query 查询条件
     * @return OTC请求包裹详情集合
     */
    default List<OtcRequestPackageDetailPageVO> listByQuery(OtcRequestPackageDetailQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取OTC请求包裹详情分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return OTC请求包裹详情集合
     */
    List<OtcRequestPackageDetailPageVO> listByQuery(@Param("qo") OtcRequestPackageDetailQuery query, @Param("page") Page<?> page);
}