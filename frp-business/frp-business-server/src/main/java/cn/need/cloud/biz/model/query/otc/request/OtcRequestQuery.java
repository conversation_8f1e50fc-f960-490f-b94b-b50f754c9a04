package cn.need.cloud.biz.model.query.otc.request;

import cn.need.cloud.biz.service.base.RequestQuery;
import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;


/**
 * OTC请求 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTC请求 query对象")
public class OtcRequestQuery extends SuperQuery implements RequestQuery {

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码")
    private String requestRefNum;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"requestRefNum"})
    private Set<String> requestRefNumList;

    /**
     * 是否快递标志
     */
    @Schema(description = "是否快递标志")
    private Boolean shipExpressFlag;

    /**
     * 运输方式
     */
    @Schema(description = "运输方式")
    private String shipMethod;

    /**
     * 运输方式
     */
    @Schema(description = "运输方式集合")
    @Condition(value = Keyword.IN, fields = {"shipMethod"})
    private List<String> shipMethodList;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司集合")
    @Condition(value = Keyword.IN, fields = {"shipCarrier"})
    private List<String> shipCarrierList;

    /**
     * 快递Api配置RefNum
     */
    @Schema(description = "快递Api配置RefNum")
    private String shipApiProfileRefNum;

    /**
     * 快递Api配置RefNum
     */
    @Schema(description = "快递Api配置RefNum集合")
    @Condition(value = Keyword.IN, fields = {"shipApiProfileRefNum"})
    private List<String> shipApiProfileRefNumList;

    /**
     * 最后发货日期
     */
    @Schema(description = "最后发货日期")
    private LocalDateTime lastShipDate;
    /**
     * 最后发货日期开始
     */
    @Schema(description = "最后发货日期开始")
    @Condition(Keyword.GE)
    private LocalDateTime lastShipDateStart;
    /**
     * 最后发货日期结束
     */
    @Schema(description = "最后发货日期结束")
    @Condition(Keyword.LE)
    private LocalDateTime lastShipDateEnd;

    /**
     * 保险金额
     */
    @Schema(description = "保险金额")
    private BigDecimal insuranceAmountAmount;

    /**
     * 签名类型
     */
    @Schema(description = "签名类型")
    private String signatureType;

    /**
     * 签名类型
     */
    @Schema(description = "签名类型集合")
    @Condition(value = Keyword.IN, fields = {"signatureType"})
    private List<String> signatureTypeList;

    /**
     * 收货地址名称
     */
    @Schema(description = "收货地址名称")
    private String shipToAddressName;

    /**
     * 收货地址公司
     */
    @Schema(description = "收货地址公司")
    private String shipToAddressCompany;

    /**
     * 收货地址国家
     */
    @Schema(description = "收货地址国家")
    private String shipToAddressCountry;

    /**
     * 收货地址州/省
     */
    @Schema(description = "收货地址州/省")
    private String shipToAddressState;

    /**
     * 收货地址城市
     */
    @Schema(description = "收货地址城市")
    private String shipToAddressCity;

    /**
     * 收货地址邮编
     */
    @Schema(description = "收货地址邮编")
    private String shipToAddressZipCode;

    /**
     * 收货地址1
     */
    @Schema(description = "收货地址1")
    private String shipToAddressAddr1;

    /**
     * 收货地址2
     */
    @Schema(description = "收货地址2")
    private String shipToAddressAddr2;

    /**
     * 收货地址3
     */
    @Schema(description = "收货地址3")
    private String shipToAddressAddr3;

    /**
     * 收货地址邮箱
     */
    @Schema(description = "收货地址邮箱")
    private String shipToAddressEmail;

    /**
     * 收货地址电话
     */
    @Schema(description = "收货地址电话")
    private String shipToAddressPhone;

    /**
     * 收货地址备注
     */
    @Schema(description = "收货地址备注")
    private String shipToAddressNote;

    /**
     * 订单类型
     */
    @Schema(description = "订单类型")
    private String orderType;

    /**
     * 订单类型
     */
    @Schema(description = "订单类型集合")
    @Condition(value = Keyword.IN, fields = {"orderType"})
    private List<String> orderTypeList;


    /**
     * 是否提供运输标签
     */
    @Schema(description = "是否提供运输标签")
    private Boolean provideShippingLabelFlag;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private Set<String> refNumList;

    /**
     * 交易伙伴ID
     */
    @Schema(description = "交易伙伴ID")
    private Long transactionPartnerId;

    /**
     * 交易伙伴ID
     */
    @Schema(description = "交易伙伴ID集合")
    @Condition(value = Keyword.IN, fields = {"transactionPartnerId"})
    private List<Long> transactionPartnerIdList;

    /**
     * 请求状态
     */
    @Schema(description = "请求状态")
    private String otcRequestStatus;

    /**
     * 请求状态
     */
    @Schema(description = "请求状态集合")
    @Condition(value = Keyword.IN, fields = {"otcRequestStatus"})
    private List<String> otcRequestStatusList;


    /**
     * 渠道
     */
    @Schema(description = "渠道")
    private String channel;

    /**
     * 渠道
     */
    @Schema(description = "渠道集合")
    @Condition(value = Keyword.IN, fields = {"channel"})
    private List<String> channelList;


    /**
     * 保险金额货币
     */
    @Schema(description = "保险金额货币")
    private String insuranceAmountCurrency;

    /**
     * 发货地址1
     */
    @Schema(description = "发货地址1")
    private String shipFromAddressAddr1;

    /**
     * 发货地址2
     */
    @Schema(description = "发货地址2")
    private String shipFromAddressAddr2;

    /**
     * 发货地址3
     */
    @Schema(description = "发货地址3")
    private String shipFromAddressAddr3;

    /**
     * 发货地址城市
     */
    @Schema(description = "发货地址城市")
    private String shipFromAddressCity;

    /**
     * 发货地址公司
     */
    @Schema(description = "发货地址公司")
    private String shipFromAddressCompany;

    /**
     * 发货地址国家
     */
    @Schema(description = "发货地址国家")
    private String shipFromAddressCountry;

    /**
     * 发货地址邮箱
     */
    @Schema(description = "发货地址邮箱")
    private String shipFromAddressEmail;


    /**
     * 发货地址名称
     */
    @Schema(description = "发货地址名称")
    private String shipFromAddressName;

    /**
     * 发货地址备注
     */
    @Schema(description = "发货地址备注")
    private String shipFromAddressNote;

    /**
     * 发货地址电话
     */
    @Schema(description = "发货地址电话")
    private String shipFromAddressPhone;

    /**
     * 发货地址州/省
     */
    @Schema(description = "发货地址州/省")
    private String shipFromAddressState;

    /**
     * 发货地址邮编
     */
    @Schema(description = "发货地址邮编")
    private String shipFromAddressZipCode;


    /**
     * 锁定时间
     */
    @Schema(description = "锁定时间")
    private String lockedBefore;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 收货地址是否为住宅
     */
    @Schema(description = "收货地址是否为住宅")
    private Boolean shipToAddressIsResidential;


    /**
     * 是否住宅地址
     */
    @Schema(description = "是否住宅地址")
    private Boolean shipFromAddressIsResidential;

    /**
     * 是否有客户运输要求
     */
    @Schema(description = "是否有客户运输要求")
    private Boolean hasCusShipRequire;

    /**
     * 计费状态
     */
    @Schema(description = "计费状态")
    private String feeStatus;

    /**
     * 计费状态
     */
    @Schema(description = "计费状态")
    @Condition(value = Keyword.IN, fields = {"feeStatus"})
    private List<String> feeStatusList;

}