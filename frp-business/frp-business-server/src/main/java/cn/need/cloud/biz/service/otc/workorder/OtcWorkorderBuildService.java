package cn.need.cloud.biz.service.otc.workorder;

import cn.need.cloud.biz.model.param.otc.create.workorder.OtcWorkorderCreateParam;

/**
 * <p>
 * OTC工单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtcWorkorderBuildService {

    /**
     * 根据参数新增OTC工单
     *
     * @param createParam 请求创建参数，包含需要插入的OTC工单的相关信息
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    void insertByParam(OtcWorkorderCreateParam createParam);

}