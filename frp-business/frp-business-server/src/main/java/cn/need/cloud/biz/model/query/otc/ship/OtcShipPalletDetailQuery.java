package cn.need.cloud.biz.model.query.otc.ship;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * OTC运输托盘详情 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTC运输托盘详情 query对象")
public class OtcShipPalletDetailQuery extends SuperQuery {

    /**
     * 快递号
     */
    @Schema(description = "快递号")
    private String trackingNum;

    /**
     * 快递号
     */
    @Schema(description = "快递号集合")
    @Condition(value = Keyword.IN, fields = {"trackingNum"})
    private List<String> trackingNumList;

    /**
     * 打包id
     */
    @Schema(description = "打包id")
    private Long otcPackageId;

    /**
     * 发货到c端托盘运输id
     */
    @Schema(description = "发货到c端托盘运输id")
    private Long otcShipPalletId;


}