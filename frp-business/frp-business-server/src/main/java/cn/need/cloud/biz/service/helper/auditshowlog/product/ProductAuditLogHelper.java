package cn.need.cloud.biz.service.helper.auditshowlog.product;

import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.entity.product.Product;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.biz.util.log.AuditLogUtil;

/**
 * 产品日志工具
 *
 * <AUTHOR>
 * @since 2024/10/28
 */
public class ProductAuditLogHelper {

    private ProductAuditLogHelper() {
    }

    public static void recordLog(Product product, String status, String type, String description) {
        recordLog(product, status, type, null, description);
    }

    public static void recordLog(Product product, String status, String type) {
        recordLog(product, status, type, null, null);
    }

    public static void recordLog(Product product, String status, String type, String note, String description) {
        AuditShowLog auditShowLog = AuditLogUtil.commonLog(product)
                .with(AuditShowLog::setEvent, status)
                .with(AuditShowLog::setNote, note)
                .with(AuditShowLog::setDescription, description)
                .with(AuditShowLog::setType, type)
                .build();
        AuditLogHolder.record(auditShowLog);
    }
}
