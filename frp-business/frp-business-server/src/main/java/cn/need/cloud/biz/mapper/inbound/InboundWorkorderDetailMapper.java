package cn.need.cloud.biz.mapper.inbound;

import cn.need.cloud.biz.model.entity.inbound.InboundWorkorderDetail;
import cn.need.cloud.biz.model.query.inbound.InboundWorkorderDetailQuery;
import cn.need.cloud.biz.model.vo.page.InboundWorkorderDetailPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 入库工单详情 FinishQty可能和Qty不一致，这个是有仓库人员判定(外界发的货物可能不一致) 仓库内部的操作是一致的，外界可以不一致 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface InboundWorkorderDetailMapper extends SuperMapper<InboundWorkorderDetail> {

    /**
     * 根据条件获取入库工单详情 FinishQty可能和Qty不一致，这个是有仓库人员判定(外界发的货物可能不一致) 仓库内部的操作是一致的，外界可以不一致列表
     *
     * @param query 查询条件
     * @return 入库工单详情 FinishQty可能和Qty不一致，这个是有仓库人员判定(外界发的货物可能不一致) 仓库内部的操作是一致的，外界可以不一致集合
     */
    default List<InboundWorkorderDetailPageVO> listByQuery(InboundWorkorderDetailQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取入库工单详情 FinishQty可能和Qty不一致，这个是有仓库人员判定(外界发的货物可能不一致) 仓库内部的操作是一致的，外界可以不一致分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 入库工单详情 FinishQty可能和Qty不一致，这个是有仓库人员判定(外界发的货物可能不一致) 仓库内部的操作是一致的，外界可以不一致集合
     */
    List<InboundWorkorderDetailPageVO> listByQuery(@Param("qo") InboundWorkorderDetailQuery query, @Param("page") Page<?> page);
}