package cn.need.cloud.biz.model.entity.otb;

import cn.need.cloud.biz.client.constant.enums.base.ShipTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otb.PaperTypeEnum;
import cn.need.cloud.biz.model.entity.base.BaseRequest;
import cn.need.cloud.dfs.client.constant.enums.FileDataTypeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;


/**
 * <p>
 * OTB请求
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otb_request")
public class OtbRequest extends BaseRequest {

    @Serial
    private static final long serialVersionUID = -6747210521343252655L;
    /**
     * 发货窗口开始时间
     */
    @TableField("ship_window_start")
    private LocalDateTime shipWindowStart;

    /**
     * 请求状态
     */
    @TableField("otb_request_status")
    private String otbRequestStatus;

    /**
     * 请求发货状态
     */
    @TableField("request_shipment_status")
    private String requestShipmentStatus;

    /**
     * 订单号
     */
    @TableField("order_num")
    private String orderNum;

    /**
     * 发货窗口结束时间
     */
    @TableField("ship_window_end")
    private LocalDateTime shipWindowEnd;

    /**
     * 发货类型
     */
    @TableField("ship_type")
    private ShipTypeEnum shipType;


    /**
     * RI文件数据
     */
    @TableField("routing_instruction_file_file_data")
    private String routingInstructionFileFileData;

    /**
     * RI文件扩展名
     */
    @TableField("routing_instruction_file_file_extension")
    private String routingInstructionFileFileExtension;

    /**
     * RI文件类型
     */
    @TableField("routing_instruction_file_file_type")
    private FileDataTypeEnum routingInstructionFileFileType;

    /**
     * RI文件纸张类型
     */
    @TableField("routing_instruction_file_paper_type")
    private PaperTypeEnum routingInstructionFilePaperType;

    // /**
    //  * RI文件
    //  */
    // @TableField(exist = false)
    // private FrpFileModel routingInstructionFile;
    //
    // public FrpFileModel getRoutingInstructionFile() {
    //     if (routingInstructionFile == null) {
    //         routingInstructionFile = new FrpFileModel();
    //         routingInstructionFile.setFileData(routingInstructionFileFileData);
    //         routingInstructionFile.setFileExtension(routingInstructionFileFileExtension);
    //         routingInstructionFile.setFileType(routingInstructionFileFileType);
    //         routingInstructionFile.setPaperType(routingInstructionFilePaperType);
    //     }
    //     return routingInstructionFile;
    // }
    //
    // public void setRoutingInstructionFile(FrpFileModel routingInstructionFile) {
    //     if (ObjectUtil.isEmpty(routingInstructionFile)) {
    //         return;
    //     }
    //     this.routingInstructionFileFileData = routingInstructionFile.getFileData();
    //     this.routingInstructionFileFileExtension = routingInstructionFile.getFileExtension();
    //     this.routingInstructionFileFileType = routingInstructionFile.getFileType();
    //     this.routingInstructionFilePaperType = routingInstructionFile.getPaperType();
    //     this.routingInstructionFile = routingInstructionFile;
    // }

}
