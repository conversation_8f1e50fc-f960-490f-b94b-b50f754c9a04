package cn.need.cloud.biz.model.query.feeconfig;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.FrpSuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;


/**
 * 仓库报价费用配置inbound详情 Query对象
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "仓库报价费用配置inbound详情 Query对象")
public class FeeConfigInboundDetailQuery extends FrpSuperQuery {

    @Serial
    private static final long serialVersionUID = 3730228114763982257L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    // region baseFee

    /**
     * 基础价格
     */
    @Schema(description = "基础价格")
    private BigDecimal baseFee;

    /**
     * 基础价格
     */
    @Schema(description = "基础价格 集合")
    @Condition(value = Keyword.IN, fields = {"baseFee"})
    private List<BigDecimal> baseFeeList;

    /**
     * 基础价格
     */
    @Schema(description = "基础价格 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"baseFee"})
    private List<BigDecimal> baseFeeNiList;

    /**
     * 基础价格
     */
    @Schema(description = "基础价格值类型集合")
    private List<String> baseFeeValueTypeList;

    /**
     * 基础价格
     */
    @Schema(description = "基础价格 大于")
    @Condition(value = Keyword.GT, fields = {"baseFee"})
    private BigDecimal baseFeeGt;

    /**
     * 基础价格
     */
    @Schema(description = "基础价格 大于等于")
    @Condition(value = Keyword.GE, fields = {"baseFee"})
    private BigDecimal baseFeeGe;

    /**
     * 基础价格
     */
    @Schema(description = "基础价格 小于")
    @Condition(value = Keyword.LT, fields = {"baseFee"})
    private BigDecimal baseFeeLt;

    /**
     * 基础价格
     */
    @Schema(description = "基础价格 小于等于")
    @Condition(value = Keyword.LE, fields = {"baseFee"})
    private BigDecimal baseFeeLe;

    /**
     * 基础价格
     */
    @Schema(description = "基础价格 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"baseFee"})
    private BigDecimal baseFeeLike;

    /**
     * 基础价格
     */
    @Schema(description = "基础价格 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"baseFee"})
    private BigDecimal baseFeeLikeLeft;

    /**
     * 基础价格
     */
    @Schema(description = "基础价格 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"baseFee"})
    private BigDecimal baseFeeLikeRight;

    // endregion baseFee

    // region deletedNote

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 集合")
    @Condition(value = Keyword.IN, fields = {"deletedNote"})
    private List<String> deletedNoteList;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"deletedNote"})
    private List<String> deletedNoteNiList;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因值类型集合")
    private List<String> deletedNoteValueTypeList;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 大于")
    @Condition(value = Keyword.GT, fields = {"deletedNote"})
    private String deletedNoteGt;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 大于等于")
    @Condition(value = Keyword.GE, fields = {"deletedNote"})
    private String deletedNoteGe;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 小于")
    @Condition(value = Keyword.LT, fields = {"deletedNote"})
    private String deletedNoteLt;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 小于等于")
    @Condition(value = Keyword.LE, fields = {"deletedNote"})
    private String deletedNoteLe;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"deletedNote"})
    private String deletedNoteLike;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"deletedNote"})
    private String deletedNoteLikeLeft;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"deletedNote"})
    private String deletedNoteLikeRight;

    // endregion deletedNote

    // region feeStartThreshold

    /**
     * 计费起始阈值（实际数量超过该值时触发计费）
     */
    @Schema(description = "计费起始阈值（实际数量超过该值时触发计费）")
    private BigDecimal feeStartThreshold;

    /**
     * 计费起始阈值（实际数量超过该值时触发计费）
     */
    @Schema(description = "计费起始阈值（实际数量超过该值时触发计费） 集合")
    @Condition(value = Keyword.IN, fields = {"feeStartThreshold"})
    private List<BigDecimal> feeStartThresholdList;

    /**
     * 计费起始阈值（实际数量超过该值时触发计费）
     */
    @Schema(description = "计费起始阈值（实际数量超过该值时触发计费） 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"feeStartThreshold"})
    private List<BigDecimal> feeStartThresholdNiList;

    /**
     * 计费起始阈值（实际数量超过该值时触发计费）
     */
    @Schema(description = "计费起始阈值（实际数量超过该值时触发计费）值类型集合")
    private List<String> feeStartThresholdValueTypeList;

    /**
     * 计费起始阈值（实际数量超过该值时触发计费）
     */
    @Schema(description = "计费起始阈值（实际数量超过该值时触发计费） 大于")
    @Condition(value = Keyword.GT, fields = {"feeStartThreshold"})
    private BigDecimal feeStartThresholdGt;

    /**
     * 计费起始阈值（实际数量超过该值时触发计费）
     */
    @Schema(description = "计费起始阈值（实际数量超过该值时触发计费） 大于等于")
    @Condition(value = Keyword.GE, fields = {"feeStartThreshold"})
    private BigDecimal feeStartThresholdGe;

    /**
     * 计费起始阈值（实际数量超过该值时触发计费）
     */
    @Schema(description = "计费起始阈值（实际数量超过该值时触发计费） 小于")
    @Condition(value = Keyword.LT, fields = {"feeStartThreshold"})
    private BigDecimal feeStartThresholdLt;

    /**
     * 计费起始阈值（实际数量超过该值时触发计费）
     */
    @Schema(description = "计费起始阈值（实际数量超过该值时触发计费） 小于等于")
    @Condition(value = Keyword.LE, fields = {"feeStartThreshold"})
    private BigDecimal feeStartThresholdLe;

    /**
     * 计费起始阈值（实际数量超过该值时触发计费）
     */
    @Schema(description = "计费起始阈值（实际数量超过该值时触发计费） 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"feeStartThreshold"})
    private BigDecimal feeStartThresholdLike;

    /**
     * 计费起始阈值（实际数量超过该值时触发计费）
     */
    @Schema(description = "计费起始阈值（实际数量超过该值时触发计费） 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"feeStartThreshold"})
    private BigDecimal feeStartThresholdLikeLeft;

    /**
     * 计费起始阈值（实际数量超过该值时触发计费）
     */
    @Schema(description = "计费起始阈值（实际数量超过该值时触发计费） 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"feeStartThreshold"})
    private BigDecimal feeStartThresholdLikeRight;

    // endregion feeStartThreshold

    // region feeUnitType

    /**
     * 计费单位类型
     */
    @Schema(description = "计费单位类型")
    private String feeUnitType;

    /**
     * 计费单位类型
     */
    @Schema(description = "计费单位类型 集合")
    @Condition(value = Keyword.IN, fields = {"feeUnitType"})
    private List<String> feeUnitTypeList;

    /**
     * 计费单位类型
     */
    @Schema(description = "计费单位类型 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"feeUnitType"})
    private List<String> feeUnitTypeNiList;

    /**
     * 计费单位类型
     */
    @Schema(description = "计费单位类型值类型集合")
    private List<String> feeUnitTypeValueTypeList;

    /**
     * 计费单位类型
     */
    @Schema(description = "计费单位类型 大于")
    @Condition(value = Keyword.GT, fields = {"feeUnitType"})
    private String feeUnitTypeGt;

    /**
     * 计费单位类型
     */
    @Schema(description = "计费单位类型 大于等于")
    @Condition(value = Keyword.GE, fields = {"feeUnitType"})
    private String feeUnitTypeGe;

    /**
     * 计费单位类型
     */
    @Schema(description = "计费单位类型 小于")
    @Condition(value = Keyword.LT, fields = {"feeUnitType"})
    private String feeUnitTypeLt;

    /**
     * 计费单位类型
     */
    @Schema(description = "计费单位类型 小于等于")
    @Condition(value = Keyword.LE, fields = {"feeUnitType"})
    private String feeUnitTypeLe;

    /**
     * 计费单位类型
     */
    @Schema(description = "计费单位类型 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"feeUnitType"})
    private String feeUnitTypeLike;

    /**
     * 计费单位类型
     */
    @Schema(description = "计费单位类型 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"feeUnitType"})
    private String feeUnitTypeLikeLeft;

    /**
     * 计费单位类型
     */
    @Schema(description = "计费单位类型 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"feeUnitType"})
    private String feeUnitTypeLikeRight;

    // endregion feeUnitType

    // region headerId

    /**
     * header表id
     */
    @Schema(description = "header表id")
    private Long headerId;

    /**
     * header表id
     */
    @Schema(description = "header表id 集合")
    @Condition(value = Keyword.IN, fields = {"headerId"})
    private List<Long> headerIdList;

    /**
     * header表id
     */
    @Schema(description = "header表id 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"headerId"})
    private List<Long> headerIdNiList;

    /**
     * header表id
     */
    @Schema(description = "header表id值类型集合")
    private List<String> headerIdValueTypeList;

    /**
     * header表id
     */
    @Schema(description = "header表id 大于")
    @Condition(value = Keyword.GT, fields = {"headerId"})
    private Long headerIdGt;

    /**
     * header表id
     */
    @Schema(description = "header表id 大于等于")
    @Condition(value = Keyword.GE, fields = {"headerId"})
    private Long headerIdGe;

    /**
     * header表id
     */
    @Schema(description = "header表id 小于")
    @Condition(value = Keyword.LT, fields = {"headerId"})
    private Long headerIdLt;

    /**
     * header表id
     */
    @Schema(description = "header表id 小于等于")
    @Condition(value = Keyword.LE, fields = {"headerId"})
    private Long headerIdLe;

    /**
     * header表id
     */
    @Schema(description = "header表id 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"headerId"})
    private Long headerIdLike;

    /**
     * header表id
     */
    @Schema(description = "header表id 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"headerId"})
    private Long headerIdLikeLeft;

    /**
     * header表id
     */
    @Schema(description = "header表id 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"headerId"})
    private Long headerIdLikeRight;

    // endregion headerId

    // region lineNum

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 行序号
     */
    @Schema(description = "行序号 集合")
    @Condition(value = Keyword.IN, fields = {"lineNum"})
    private List<Integer> lineNumList;

    /**
     * 行序号
     */
    @Schema(description = "行序号 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"lineNum"})
    private List<Integer> lineNumNiList;

    /**
     * 行序号
     */
    @Schema(description = "行序号值类型集合")
    private List<String> lineNumValueTypeList;

    /**
     * 行序号
     */
    @Schema(description = "行序号 大于")
    @Condition(value = Keyword.GT, fields = {"lineNum"})
    private Integer lineNumGt;

    /**
     * 行序号
     */
    @Schema(description = "行序号 大于等于")
    @Condition(value = Keyword.GE, fields = {"lineNum"})
    private Integer lineNumGe;

    /**
     * 行序号
     */
    @Schema(description = "行序号 小于")
    @Condition(value = Keyword.LT, fields = {"lineNum"})
    private Integer lineNumLt;

    /**
     * 行序号
     */
    @Schema(description = "行序号 小于等于")
    @Condition(value = Keyword.LE, fields = {"lineNum"})
    private Integer lineNumLe;

    /**
     * 行序号
     */
    @Schema(description = "行序号 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"lineNum"})
    private Integer lineNumLike;

    /**
     * 行序号
     */
    @Schema(description = "行序号 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"lineNum"})
    private Integer lineNumLikeLeft;

    /**
     * 行序号
     */
    @Schema(description = "行序号 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"lineNum"})
    private Integer lineNumLikeRight;

    // endregion lineNum

    // region note

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 备注
     */
    @Schema(description = "备注 集合")
    @Condition(value = Keyword.IN, fields = {"note"})
    private List<String> noteList;

    /**
     * 备注
     */
    @Schema(description = "备注 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"note"})
    private List<String> noteNiList;

    /**
     * 备注
     */
    @Schema(description = "备注值类型集合")
    private List<String> noteValueTypeList;

    /**
     * 备注
     */
    @Schema(description = "备注 大于")
    @Condition(value = Keyword.GT, fields = {"note"})
    private String noteGt;

    /**
     * 备注
     */
    @Schema(description = "备注 大于等于")
    @Condition(value = Keyword.GE, fields = {"note"})
    private String noteGe;

    /**
     * 备注
     */
    @Schema(description = "备注 小于")
    @Condition(value = Keyword.LT, fields = {"note"})
    private String noteLt;

    /**
     * 备注
     */
    @Schema(description = "备注 小于等于")
    @Condition(value = Keyword.LE, fields = {"note"})
    private String noteLe;

    /**
     * 备注
     */
    @Schema(description = "备注 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"note"})
    private String noteLike;

    /**
     * 备注
     */
    @Schema(description = "备注 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"note"})
    private String noteLikeLeft;

    /**
     * 备注
     */
    @Schema(description = "备注 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"note"})
    private String noteLikeRight;

    // endregion note

    // region refNum

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private List<String> refNumList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"refNum"})
    private List<String> refNumNiList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码值类型集合")
    private List<String> refNumValueTypeList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 大于")
    @Condition(value = Keyword.GT, fields = {"refNum"})
    private String refNumGt;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 大于等于")
    @Condition(value = Keyword.GE, fields = {"refNum"})
    private String refNumGe;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 小于")
    @Condition(value = Keyword.LT, fields = {"refNum"})
    private String refNumLt;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 小于等于")
    @Condition(value = Keyword.LE, fields = {"refNum"})
    private String refNumLe;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"refNum"})
    private String refNumLike;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"refNum"})
    private String refNumLikeLeft;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"refNum"})
    private String refNumLikeRight;

    // endregion refNum

    // region sectionEnd

    /**
     * 结束（不包含）根据condition_type得到值来判断
     */
    @Schema(description = "结束（不包含）根据condition_type得到值来判断")
    private Long sectionEnd;

    /**
     * 结束（不包含）根据condition_type得到值来判断
     */
    @Schema(description = "结束（不包含）根据condition_type得到值来判断 集合")
    @Condition(value = Keyword.IN, fields = {"sectionEnd"})
    private List<Long> sectionEndList;

    /**
     * 结束（不包含）根据condition_type得到值来判断
     */
    @Schema(description = "结束（不包含）根据condition_type得到值来判断 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"sectionEnd"})
    private List<Long> sectionEndNiList;

    /**
     * 结束（不包含）根据condition_type得到值来判断
     */
    @Schema(description = "结束（不包含）根据condition_type得到值来判断值类型集合")
    private List<String> sectionEndValueTypeList;

    /**
     * 结束（不包含）根据condition_type得到值来判断
     */
    @Schema(description = "结束（不包含）根据condition_type得到值来判断 大于")
    @Condition(value = Keyword.GT, fields = {"sectionEnd"})
    private Long sectionEndGt;

    /**
     * 结束（不包含）根据condition_type得到值来判断
     */
    @Schema(description = "结束（不包含）根据condition_type得到值来判断 大于等于")
    @Condition(value = Keyword.GE, fields = {"sectionEnd"})
    private Long sectionEndGe;

    /**
     * 结束（不包含）根据condition_type得到值来判断
     */
    @Schema(description = "结束（不包含）根据condition_type得到值来判断 小于")
    @Condition(value = Keyword.LT, fields = {"sectionEnd"})
    private Long sectionEndLt;

    /**
     * 结束（不包含）根据condition_type得到值来判断
     */
    @Schema(description = "结束（不包含）根据condition_type得到值来判断 小于等于")
    @Condition(value = Keyword.LE, fields = {"sectionEnd"})
    private Long sectionEndLe;

    /**
     * 结束（不包含）根据condition_type得到值来判断
     */
    @Schema(description = "结束（不包含）根据condition_type得到值来判断 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"sectionEnd"})
    private Long sectionEndLike;

    /**
     * 结束（不包含）根据condition_type得到值来判断
     */
    @Schema(description = "结束（不包含）根据condition_type得到值来判断 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"sectionEnd"})
    private Long sectionEndLikeLeft;

    /**
     * 结束（不包含）根据condition_type得到值来判断
     */
    @Schema(description = "结束（不包含）根据condition_type得到值来判断 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"sectionEnd"})
    private Long sectionEndLikeRight;

    // endregion sectionEnd

    // region sectionStart

    /**
     * 开始（包含）根据condition_type得到值来判断
     */
    @Schema(description = "开始（包含）根据condition_type得到值来判断")
    private Long sectionStart;

    /**
     * 开始（包含）根据condition_type得到值来判断
     */
    @Schema(description = "开始（包含）根据condition_type得到值来判断 集合")
    @Condition(value = Keyword.IN, fields = {"sectionStart"})
    private List<Long> sectionStartList;

    /**
     * 开始（包含）根据condition_type得到值来判断
     */
    @Schema(description = "开始（包含）根据condition_type得到值来判断 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"sectionStart"})
    private List<Long> sectionStartNiList;

    /**
     * 开始（包含）根据condition_type得到值来判断
     */
    @Schema(description = "开始（包含）根据condition_type得到值来判断值类型集合")
    private List<String> sectionStartValueTypeList;

    /**
     * 开始（包含）根据condition_type得到值来判断
     */
    @Schema(description = "开始（包含）根据condition_type得到值来判断 大于")
    @Condition(value = Keyword.GT, fields = {"sectionStart"})
    private Long sectionStartGt;

    /**
     * 开始（包含）根据condition_type得到值来判断
     */
    @Schema(description = "开始（包含）根据condition_type得到值来判断 大于等于")
    @Condition(value = Keyword.GE, fields = {"sectionStart"})
    private Long sectionStartGe;

    /**
     * 开始（包含）根据condition_type得到值来判断
     */
    @Schema(description = "开始（包含）根据condition_type得到值来判断 小于")
    @Condition(value = Keyword.LT, fields = {"sectionStart"})
    private Long sectionStartLt;

    /**
     * 开始（包含）根据condition_type得到值来判断
     */
    @Schema(description = "开始（包含）根据condition_type得到值来判断 小于等于")
    @Condition(value = Keyword.LE, fields = {"sectionStart"})
    private Long sectionStartLe;

    /**
     * 开始（包含）根据condition_type得到值来判断
     */
    @Schema(description = "开始（包含）根据condition_type得到值来判断 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"sectionStart"})
    private Long sectionStartLike;

    /**
     * 开始（包含）根据condition_type得到值来判断
     */
    @Schema(description = "开始（包含）根据condition_type得到值来判断 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"sectionStart"})
    private Long sectionStartLikeLeft;

    /**
     * 开始（包含）根据condition_type得到值来判断
     */
    @Schema(description = "开始（包含）根据condition_type得到值来判断 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"sectionStart"})
    private Long sectionStartLikeRight;

    // endregion sectionStart

    // region unitFee

    /**
     * 单价
     */
    @Schema(description = "单价")
    private BigDecimal unitFee;

    /**
     * 单价
     */
    @Schema(description = "单价 集合")
    @Condition(value = Keyword.IN, fields = {"unitFee"})
    private List<BigDecimal> unitFeeList;

    /**
     * 单价
     */
    @Schema(description = "单价 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"unitFee"})
    private List<BigDecimal> unitFeeNiList;

    /**
     * 单价
     */
    @Schema(description = "单价值类型集合")
    private List<String> unitFeeValueTypeList;

    /**
     * 单价
     */
    @Schema(description = "单价 大于")
    @Condition(value = Keyword.GT, fields = {"unitFee"})
    private BigDecimal unitFeeGt;

    /**
     * 单价
     */
    @Schema(description = "单价 大于等于")
    @Condition(value = Keyword.GE, fields = {"unitFee"})
    private BigDecimal unitFeeGe;

    /**
     * 单价
     */
    @Schema(description = "单价 小于")
    @Condition(value = Keyword.LT, fields = {"unitFee"})
    private BigDecimal unitFeeLt;

    /**
     * 单价
     */
    @Schema(description = "单价 小于等于")
    @Condition(value = Keyword.LE, fields = {"unitFee"})
    private BigDecimal unitFeeLe;

    /**
     * 单价
     */
    @Schema(description = "单价 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"unitFee"})
    private BigDecimal unitFeeLike;

    /**
     * 单价
     */
    @Schema(description = "单价 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"unitFee"})
    private BigDecimal unitFeeLikeLeft;

    /**
     * 单价
     */
    @Schema(description = "单价 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"unitFee"})
    private BigDecimal unitFeeLikeRight;

    // endregion unitFee

    // region warehouseId

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 集合")
    @Condition(value = Keyword.IN, fields = {"warehouseId"})
    private List<Long> warehouseIdList;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"warehouseId"})
    private List<Long> warehouseIdNiList;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id值类型集合")
    private List<String> warehouseIdValueTypeList;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 大于")
    @Condition(value = Keyword.GT, fields = {"warehouseId"})
    private Long warehouseIdGt;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 大于等于")
    @Condition(value = Keyword.GE, fields = {"warehouseId"})
    private Long warehouseIdGe;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 小于")
    @Condition(value = Keyword.LT, fields = {"warehouseId"})
    private Long warehouseIdLt;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 小于等于")
    @Condition(value = Keyword.LE, fields = {"warehouseId"})
    private Long warehouseIdLe;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"warehouseId"})
    private Long warehouseIdLike;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"warehouseId"})
    private Long warehouseIdLikeLeft;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"warehouseId"})
    private Long warehouseIdLikeRight;

    // endregion warehouseId

    @Override
    public Set<String> getAvailableFields() {
        java.util.Set<String> fields = new java.util.HashSet<>();
        fields.add("id");
        fields.add("createBy");
        fields.add("createTime");
        fields.add("updateBy");
        fields.add("updateTime");
        fields.add("removeFlag");
        fields.add("version");
        fields.add("tenantId");
        fields.add("baseFee");
        fields.add("deletedNote");
        fields.add("feeStartThreshold");
        fields.add("feeUnitType");
        fields.add("headerId");
        fields.add("lineNum");
        fields.add("note");
        fields.add("refNum");
        fields.add("sectionEnd");
        fields.add("sectionStart");
        fields.add("unitFee");
        fields.add("warehouseId");
        return fields;
    }

}