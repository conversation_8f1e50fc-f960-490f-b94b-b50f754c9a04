package cn.need.cloud.biz.service.otc.pickingslip.impl;

import cn.need.cloud.biz.client.constant.enums.base.*;
import cn.need.cloud.biz.client.constant.enums.binlocation.BinTypeEnum;
import cn.need.cloud.biz.client.constant.enums.inventory.InventoryLockedStatusEnum;
import cn.need.cloud.biz.client.constant.enums.inventory.WorkOrderPrepStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.*;
import cn.need.cloud.biz.client.constant.pickingslip.PickingSlipLogConstant;
import cn.need.cloud.biz.client.constant.workorder.WorkorderLogConstant;
import cn.need.cloud.biz.model.entity.binlocation.BinLocation;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.entity.otc.*;
import cn.need.cloud.biz.model.query.base.BaseBinLocationQuery;
import cn.need.cloud.biz.model.query.otc.pickingslip.OtcPickingSlipBuildStrategyQuery;
import cn.need.cloud.biz.model.query.otc.pickingslip.OtcPickingSlipFilterBuildQuery;
import cn.need.cloud.biz.model.query.otc.pickingslip.prep.OtcPrepPickingSlipFilterBuildQuery;
import cn.need.cloud.biz.model.query.otc.workorder.OtcWorkOrderListQuery;
import cn.need.cloud.biz.model.query.otc.workorder.OtcWorkorderQuery;
import cn.need.cloud.biz.model.query.otc.workorder.prep.OtcPrepWorkOrderListQuery;
import cn.need.cloud.biz.model.vo.base.workorder.WorkOrderNoEnoughAvailQtyVO;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcFilterBuildContextVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPickingSlipVO;
import cn.need.cloud.biz.model.vo.otc.pkg.OtcPrepFilterBuildContextVO;
import cn.need.cloud.biz.service.base.PickingSlipService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailLockedService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.service.binlocation.BinLocationService;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcPrepPickingSlipAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcPrepWorkorderAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcRequestAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcWorkOrderAuditLogHelper;
import cn.need.cloud.biz.service.inventory.InventoryLockedService;
import cn.need.cloud.biz.service.otc.pickingslip.*;
import cn.need.cloud.biz.service.otc.request.OtcRequestService;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderDetailService;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderService;
import cn.need.cloud.biz.util.BinLocationCheckUtil;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.biz.util.TimeUtils;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.session.Users;
import cn.need.framework.common.mybatis.model.IdModel;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * OTC预提货单 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@AllArgsConstructor
public class OtcPrepPickingSlipBuildServiceImpl implements OtcPrepPickingSlipBuildService {


    private final OtcPrepPickingSlipService otcPrepPickingSlipService;

    private final OtcPickingSlipService otcPickingSlipService;

    private final OtcPickingSlipBuildService otcPickingSlipBuildService;

    private final BinLocationService binLocationService;

    private final BinLocationDetailService binLocationDetailService;

    private final BinLocationDetailLockedService binLocationDetailLockedService;

    private final InventoryLockedService inventoryLockedService;

    private final OtcPrepPickingSlipDetailService otcPrepPickingSlipDetailService;

    private final OtcPrepWorkorderService otcPrepWorkorderService;

    private final OtcWorkorderService otcWorkorderService;

    private final OtcRequestService otcRequestService;

    private final OtcPrepWorkorderDetailService otcPrepWorkorderDetailService;

    private final PickingSlipService pickingSlipService;

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<WorkOrderNoEnoughAvailQtyVO> filterBuild(OtcPrepPickingSlipFilterBuildQuery query) {
        // 上下文信息
        OtcPrepFilterBuildContextVO context = new OtcPrepFilterBuildContextVO();
        context.setQuery(query);

        // 获取工单列表
        this.checkAndGetPrepWorkOrderList(context);

        // 检查工单库存
        this.checkPrepWorkOrderInStock(context);

        if (ObjectUtil.isEmpty(context.getPrepWorkOrderList())) {
            return context.getNoStockWorkOrderList();
        }

        // 构建拣货单
        this.buildPrepPickingSlipList(context);

        // 构建拣货单详情
        this.buildPrepPickingSlipDetailList(context);

        // 锁库位库存
        this.lockBinLocation(context);

        List<OtcPrepWorkorder> prepWorkOrderList = context.getPrepWorkOrderList();
        List<Long> prepWorkOrderIdList = StreamUtils.distinctMap(prepWorkOrderList, OtcPrepWorkorder::getId);

        // 释放Prep WorkOrder LockedInventory
        inventoryLockedService.releaseLockedInventory(otcPrepWorkorderDetailService.findInventoryReleaseLockedParam(prepWorkOrderIdList));

        // 批量入库拣货单
        otcPrepPickingSlipService.insertBatch(context.getPrepPickingSlipList());

        // 批量入库拣货单详情
        otcPrepPickingSlipDetailService.insertBatch(context.getPrepPickingSlipDetailList());

        // 更新 Prep WorkOrder 拣货单id 和 InPicking
        List<OtcPrepWorkorder> prepWorkorderList = this.buildUpdatePrepWorkOrderList(prepWorkOrderList);
        Validate.isTrue(otcPrepWorkorderService.updateBatch(prepWorkorderList) == prepWorkOrderIdList.size(),
                "Update WorkOrder status [InPicking] failed"
        );

        // 更新 WorkOrder workorderPrepStatus Processing状态
        this.buildUpdateWorkOrderList(context);
        List<OtcWorkorder> workorderList = context.getWorkorderList();
        Validate.isTrue(otcWorkorderService.updateBatch(workorderList) == workorderList.size(),
                "Update WorkOrder WorkorderPrepStatus [Processing] failed"
        );

        // 更新请求单 Processing状态
        List<OtcRequest> requestList = this.buildUpdateRequestList(context);
        Validate.isTrue(otcRequestService.updateBatch(requestList) == requestList.size(),
                "Update Request status [Processing] failed"
        );

        // 记录日志
        this.recordFilterBuildLog(context);
        return context.getNoStockWorkOrderList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OtcPickingSlipVO buildPickingSlip(Long prepPickingSlipId) {
        OtcPrepPickingSlip prepPickingSlip = otcPrepPickingSlipService.getById(prepPickingSlipId);
        Validate.notNull(prepPickingSlip, "{} not found in PrepPickingSlip", prepPickingSlipId);

        ProcessType.checkNormalAvailability(prepPickingSlip.getProcessType(), prepPickingSlip.refNumLog(), "buildPickingSlip");

        // 可处理订单类型
        List<String> canProcessOrderTypeList = Arrays.asList(
                OtcOrderTypeEnum.MULTI_BOX.getStatus(),
                OtcOrderTypeEnum.SLAP_AND_GO.getStatus(),
                OtcOrderTypeEnum.SOSP.getStatus()
        );

        // 不支持SOMP
        Validate.isTrue(canProcessOrderTypeList.contains(prepPickingSlip.getOrderType()), "OrderType is not valid");
        // 已经Build
        Validate.isTrue(ObjectUtil.isNull(prepPickingSlip.getOtcPickingSlipId()), "OtcPickingSlipId is already exist");
        // 上架后才可以生成拣货单
        Validate.isTrue(Objects.equals(OtcPrepPickingSlipStatusEnum.PUT_AWAY.getStatus(), prepPickingSlip.getPrepPickingSlipStatus()),
                "PrepPickingSlip current is not {} status", OtcPrepPickingSlipStatusEnum.PUT_AWAY.getStatus()
        );

        // 获取工单id
        List<Long> workorderIdList = otcPrepWorkorderService.workorderIdListByPrepPickingSlipId(prepPickingSlip.getId());

        // 校验Normal没有FilterBuild
        otcWorkorderService.checkNoNormalFilterBuild(workorderIdList);

        // 构建拣货单
        OtcPickingSlip otcPickingSlip = this.buildPickingSlip(workorderIdList);
        prepPickingSlip.setOtcPickingSlipId(otcPickingSlip.getId());

        // 更新预拣货单
        int updateCount = otcPrepPickingSlipService.update(prepPickingSlip);
        Validate.isTrue(updateCount == 1, "Update Prep Picking Slip failed");

        // PrepPickingSlip buildPickingSlip 日志
        OtcPrepPickingSlipAuditLogHelper.recordLog(prepPickingSlip, PickingSlipLogConstant.PREP_BUILD_PICKING_SLIP_STATUS, otcPickingSlip.getRefNum(), null);

        // 拣货单详情
        return otcPickingSlipService.detailById(otcPickingSlip.getId());
    }

    /**
     * Prep拣货单构建拣货单
     *
     * @param workOrderIdList 工单ID集合
     * @return 拣货单
     */
    private OtcPickingSlip buildPickingSlip(List<Long> workOrderIdList) {
        // 构建查询条件
        OtcPickingSlipFilterBuildQuery buildQuery = new OtcPickingSlipFilterBuildQuery();
        buildQuery.setBuildFromType(BuildFromType.OTC_PREP_PICKING_SLIP);
        OtcWorkOrderListQuery workOrderListQuery = new OtcWorkOrderListQuery();

        // 设置工单查询条件
        OtcWorkorderQuery otcWorkorderQuery = new OtcWorkorderQuery();
        //  只处理Normal工单
        otcWorkorderQuery.setProcessType(ProcessType.NORMAL.getType());
        // 设置工单id集合
        otcWorkorderQuery.setIdList(workOrderIdList);
        // 设置和列表一致
        otcWorkorderQuery.setOtcWorkorderStatus(OtcWorkorderStatusEnum.BEGIN.getStatus());
        // 支持None/Processed
        otcWorkorderQuery.setWorkorderPrepStatusList(Arrays.asList(
                WorkOrderPrepStatusEnum.NONE.getStatus(),
                WorkOrderPrepStatusEnum.PROCESSED.getStatus()
        ));
        // 选择发货时间、选择较大值
        otcWorkorderQuery.setRequestSnapshotLastShipDateStart(LocalDateTime.of(1970, 1, 1, 0, 0));
        otcWorkorderQuery.setRequestSnapshotLastShipDateEnd(LocalDateTime.of(2100, 1, 1, 0, 0));

        // 设置库位查询条件
        BaseBinLocationQuery binLocationQuery = new BaseBinLocationQuery();
        // 使用Type查询，列表查询单是BinType
        binLocationQuery.setTypeList(Arrays.asList(
                BinTypeEnum.OTC_ASSEMBLY.getBinType(),
                BinTypeEnum.OTC_MULTIBOX.getBinType(),
                BinTypeEnum.OTC_CONVERT.getBinType())
        );

        workOrderListQuery.setBinLocationQuery(binLocationQuery);
        workOrderListQuery.setOtcWorkorderQuery(otcWorkorderQuery);
        buildQuery.setFilter(workOrderListQuery);
        // 设置默认策略
        OtcPickingSlipBuildStrategyQuery strategy = new OtcPickingSlipBuildStrategyQuery();
        strategy.setBuildPickingSlipStrategy(OtcBuildPickingSlipStrategyEnum.Default.getStatus());
        strategy.setMaxSosp(Integer.MAX_VALUE);
        strategy.setMaxSlapAndGo(Integer.MAX_VALUE);
        strategy.setMaxSospBinLocation(Integer.MAX_VALUE);
        strategy.setMaxSlapAndGoBinLocation(Integer.MAX_VALUE);
        strategy.setMaxCanBuildSum(Integer.MAX_VALUE);
        buildQuery.setStrategy(strategy);

        // 执行拣货单FilterBuild逻辑
        OtcFilterBuildContextVO context = otcPickingSlipBuildService.filterBuildWithContext(buildQuery);
        List<OtcPickingSlip> buildPickingSlipList = context.getPickingSlipList();
        List<WorkOrderNoEnoughAvailQtyVO> noStockWorkOrderList = context.getNoStockWorkOrderList();
        // 库存不足
        if (ObjectUtil.isEmpty(buildPickingSlipList) && ObjectUtil.isNotEmpty(noStockWorkOrderList)) {
            String noEnoughMsg = noStockWorkOrderList.stream()
                    .map(obj -> String.format("WorkOrder %s Product %s no enough, needQty: %d, availQty: %d",
                            obj.getRefNum(), obj.getProductId(), obj.getNeedQty(), obj.getAvailQty())
                    )
                    .collect(Collectors.joining(StringPool.COMMA));
            throw new BusinessException(noEnoughMsg);
        }
        Validate.isTrue(ObjectUtil.isNotEmpty(buildPickingSlipList), "BuildPickingSlip is empty");
        Validate.isTrue(buildPickingSlipList.size() == 1, "Build multiple picking lists, Can not build PickingSlip");
        return buildPickingSlipList.get(0);
    }

    /**
     * 检查并获取工单列表
     *
     * @param context 上下文
     */
    private void checkAndGetPrepWorkOrderList(OtcPrepFilterBuildContextVO context) {
        OtcPrepPickingSlipFilterBuildQuery query = context.getQuery();
        List<OtcPrepWorkorder> workOrderList = otcPrepWorkorderService.filterBuildByQuery(query);
        if (ObjectUtil.isEmpty(workOrderList)) {
            throw new BusinessException("Current Filter has No PrepWorkOrder to Build!");
        }
        List<Long> workOrderIdList = StreamUtils.distinctMap(workOrderList, OtcPrepWorkorder::getId);
        // 获取工单详情
        Map<Long, List<OtcPrepWorkorderDetail>> workOrderDetailGroupByWorkOrderIdMap
                = otcPrepWorkorderDetailService.convertAfterProductsGroupByWorkOrderIdList(workOrderIdList);

        // 保持与工单顺序一致
        Map<Long, List<OtcPrepWorkorderDetail>> sortWithPrepWorkOrder
                = StreamUtils.sortByList(workOrderDetailGroupByWorkOrderIdMap, workOrderList);

        // 绑定到上下文
        context.setWorkOrderDetailGroupByWorkOrderIdMap(sortWithPrepWorkOrder);
        context.setPrepWorkOrderList(new ArrayList<>(workOrderList));
    }

    /**
     * 构建拣货单
     * 1.根据工单 OrderType and PickToStation and transaction_partner_id 分组
     *
     * @param context 上下文
     */
    private void buildPrepPickingSlipList(OtcPrepFilterBuildContextVO context) {

        List<OtcPrepWorkorder> prepWorkOrderList = context.getPrepWorkOrderList();
        List<Long> workOrderIdList = StreamUtils.distinctMap(prepWorkOrderList, OtcPrepWorkorder::getOtcWorkorderId);
        List<OtcWorkorder> workorderList = otcWorkorderService.listByIds(workOrderIdList);
        // 绑定工单信息
        context.setWorkorderList(workorderList);

        // 工单打包类型
        Map<Long, String> workorderBuildShipPackageTypeMap = workorderList.stream()
                .collect(Collectors.toMap(IdModel::getId, OtcWorkorder::getBuildShipPackageType));

        List<OtcPrepWorkorder> workOrderList = context.getPrepWorkOrderList();
        // 根据分组
        Map<String, List<OtcPrepWorkorder>> workOrderGroupMap = workOrderList.stream()
                .collect(Collectors.groupingBy(obj -> String.format("%s:%s:%s:%s:%s:%s:%s:%s:%s:%s",
                        obj.getProductId(),
                        obj.getPrepWorkorderVersionInt(),
                        obj.getPrepWorkorderType(),
                        obj.getOrderType(),
                        obj.getPickToStation(),
                        obj.getTransactionPartnerId(),
                        obj.getHasCusShipRequire(),
                        obj.getPrepWorkorderProductType(),
                        OtcBuildShipPackageEnum.BY_WAREHOUSE.getStatus().equals(workorderBuildShipPackageTypeMap.getOrDefault(obj.getOtcWorkorderId(), OtcBuildShipPackageEnum.NEW.getStatus())),
                        obj.getOnSitePackFlag()
                )));
        // 获取OCWAssembly、OCWMultiBox、OCWConvert库位
        BinLocation ocwAssembly = binLocationService.findVirtualBinLocationByType(BinTypeEnum.OTC_ASSEMBLY);
        BinLocation ocwMultiBox = binLocationService.findVirtualBinLocationByType(BinTypeEnum.OTC_MULTIBOX);
        BinLocation ocwConvert = binLocationService.findVirtualBinLocationByType(BinTypeEnum.OTC_CONVERT);

        // 设置Prep工单类型至库位的映射
        Map<PrepWordorderTypeEnum, Long> binLocationByBinType = new HashMap<>() {
            @Serial
            private static final long serialVersionUID = 3394183585179242999L;

            {
                put(PrepWordorderTypeEnum.PREP_PACK, ocwAssembly.getId());
                put(PrepWordorderTypeEnum.PREP_MULTI_BOX, ocwMultiBox.getId());
                put(PrepWordorderTypeEnum.PREP_CONVERT, ocwConvert.getId());
                put(PrepWordorderTypeEnum.PREP_CONVERT_MULTI_BOX, ocwMultiBox.getId());
                put(PrepWordorderTypeEnum.PREP_CONVERT_PACK, ocwAssembly.getId());
            }
        };

        List<OtcPrepPickingSlip> pickingSlipList = workOrderGroupMap.values()
                .stream()
                // 创建拣货单
                .map(obj -> this.createPickingSlip(obj, this.getPutAwayBinLocationId(obj, binLocationByBinType)))
                .toList();

        // 赋值拣货单信息到上下文中
        context.setPrepPickingSlipList(new ArrayList<>(pickingSlipList));
    }

    /**
     * 获取上架虚拟库位id
     *
     * @param orderList            Prep工单集合
     * @param binLocationByBinType 库位id映射
     * @return /
     */
    private Long getPutAwayBinLocationId(List<OtcPrepWorkorder> orderList, Map<PrepWordorderTypeEnum, Long> binLocationByBinType) {

        OtcPrepWorkorder prepWorkOrder = orderList.get(0);
        String prepWorkOrderType = prepWorkOrder.getPrepWorkorderType();
        PrepWordorderTypeEnum type = PrepWordorderTypeEnum.typeOf(prepWorkOrderType);
        if (ObjectUtil.isNull(type)) {
            throw new BusinessException(String.format("Unsupported preprocessing ticket type: %s", prepWorkOrderType));
        }
        return binLocationByBinType.get(type);

    }

    /**
     * 创建拣货单
     *
     * @param orderList 工单集合
     * @return /
     */
    private OtcPrepPickingSlip createPickingSlip(List<OtcPrepWorkorder> orderList, Long binLocationId) {
        OtcPrepWorkorder first = orderList.get(0);
        OtcPrepPickingSlip slip = new OtcPrepPickingSlip();
        // 初始化值
        slip.setId(IdWorker.getId());
        slip.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTC_PREP_PICKING_SLIP.getCode()));

        // 库位
        slip.setBinLocationId(binLocationId);
        slip.setPrintStatus(PrintStatusEnum.NONE.getStatus());
        slip.setQty(orderList.stream().mapToInt(OtcPrepWorkorder::getQty).sum());

        // 工单属性
        slip.setProductId(first.getProductId());
        slip.setOrderType(first.getOrderType());
        slip.setPickToStation(first.getPickToStation());
        slip.setOnSitePackFlag(first.getOnSitePackFlag());
        slip.setHasCusShipRequire(first.getHasCusShipRequire());
        slip.setPrepPickingSlipType(first.getPrepWorkorderType());
        slip.setTransactionPartnerId(first.getTransactionPartnerId());
        slip.setPrepPickingSlipVersionInt(first.getPrepWorkorderVersionInt());
        slip.setPrepPickingSlipProductType(first.getPrepWorkorderProductType());

        // 其他属性
        slip.setPrepPickingSlipStatus(OtcPrepPickingSlipStatusEnum.NEW.getStatus());
        slip.setAssignedUserId(Objects.requireNonNull(Users.getUser()).getId());
        slip.setAllocatePutawayQty(0);
        slip.setPutawayQty(0);

        // 工单赋值
        orderList.forEach(obj -> obj.setOtcPrepPickingSlipId(slip.getId()));
        return slip;
    }

    /**
     * 构建拣货单详情
     * <p>
     * 通过工单详情 productId 获取所有 (productVersionId + binLocationId) 构建拣货单详情。
     * 该方法主要通过层级关系构建拣货单详情：拣货单id -> 工单id集合 -> 工单详情集合 -> 产品id集合 -> 库位详情集合。
     *
     * @param context 上下文对象，包含工单详情、工单列表等信息
     */
    private void buildPrepPickingSlipDetailList(OtcPrepFilterBuildContextVO context) {
        Map<Long, List<OtcPrepWorkorderDetail>> workOrderDetailGroupByWorkOrderIdMap = context.getWorkOrderDetailGroupByWorkOrderIdMap();
        List<OtcPrepWorkorder> workOrderList = context.getPrepWorkOrderList();

        // 获取实际可用库位详情
        Map<Long, List<BinLocationDetail>> binLocationDetailMap = context.getRealAvailableGroupByPorductMap();
        // 虚拟库位
        Map<Long, BinLocation> virtualBinLocationMap = binLocationService.allVirtualBinLocationList();

        List<OtcPrepPickingSlipDetail> prepPickingSlipDetails = new ArrayList<>();
        for (OtcPrepWorkorder wk : workOrderList) {
            if (!workOrderDetailGroupByWorkOrderIdMap.containsKey(wk.getId())) {
                continue;
            }
            for (OtcPrepWorkorderDetail wkDetail : workOrderDetailGroupByWorkOrderIdMap.get(wk.getId())) {
                if (!binLocationDetailMap.containsKey(wkDetail.getProductId())) {
                    continue;
                }
                AtomicInteger totalQty = new AtomicInteger(wkDetail.getQty());
                List<OtcPrepPickingSlipDetail> psDetailList = new ArrayList<>();
                // 遍历库位详情，分配库存数量并生成拣货单详情
                for (BinLocationDetail binDetail : binLocationDetailMap.get(wkDetail.getProductId())) {
                    if (totalQty.get() <= 0 || binDetail.getInStockQty() <= 0) {
                        continue;
                    }
                    if (virtualBinLocationMap.containsKey(binDetail.getBinLocationId())) {
                        continue;
                    }
                    int currentQty = Math.min(totalQty.get(), binDetail.getInStockQty());
                    OtcPrepPickingSlipDetail slipDetail = this.buildPickingSlipDetail(wk, binDetail);
                    slipDetail.setQty(currentQty);
                    binDetail.setInStockQty(binDetail.getInStockQty() - currentQty);
                    totalQty.addAndGet(-currentQty);
                    psDetailList.add(slipDetail);
                }
                Validate.isTrue(totalQty.get() == 0, "Failed to allocate quantity");
                prepPickingSlipDetails.addAll(psDetailList);
            }
        }
        // 合并拣货单详情列表
        List<OtcPrepPickingSlipDetail> mergeDetailList = aggregateDetailList(prepPickingSlipDetails);

        // 赋值拣货单详情、工单库位详情到上下文中
        context.setPrepPickingSlipDetailList(mergeDetailList);
    }

    /**
     * 聚合拣货单详情
     * <p>
     * 对拣货单详情列表进行多维度聚合处理，合并相同拣货单、库位和产品的记录，
     * 累加数量后按库位名称排序并生成行号
     *
     * @param prepPickingSlipDetailList 待处理的Prep拣货单详情列表，包含原始拣货数据
     * @return 聚合后的拣货单详情列表，包含合并后的数量和排序后的行号
     * 返回列表满足以下特征：
     * 1. 同一拣货单内按库位名称排序
     * 2. 每条记录包含合并后的总数量
     * 3. 每个拣货单内的记录有连续递增的行号
     */
    private List<OtcPrepPickingSlipDetail> aggregateDetailList(List<OtcPrepPickingSlipDetail> prepPickingSlipDetailList) {
        /**
         * 构建库位ID到库位信息的映射关系
         * 1. 从原始数据中提取所有不重复的库位ID
         * 2. 调用服务层批量查询库位详细信息
         * 用于后续排序时获取库位名称
         */
        List<Long> binLocationIdList = StreamUtils.distinctMap(prepPickingSlipDetailList, OtcPrepPickingSlipDetail::getBinLocationId);
        Map<Long, BinLocationVO> binLocationMap = binLocationService.binLocationByIds(binLocationIdList);

        /**
         * 按拣货单ID+库位详情ID+产品ID组合键进行分组
         * 生成临时分组键格式："{拣货单ID}:{库位详情ID}:{产品ID}"
         * 保证相同拣货单、相同库位、相同产品的记录被聚合
         */
        Map<String, List<OtcPrepPickingSlipDetail>> groupedByKeys = new HashMap<>();
        for (OtcPrepPickingSlipDetail detail : prepPickingSlipDetailList) {
            String key = String.format("%s:%s:%s",
                    detail.getOtcPrepPickingSlipId(), detail.getBinLocationDetailId(), detail.getProductId());
            groupedByKeys.computeIfAbsent(key, k -> new ArrayList<>()).add(detail);
        }

        /**
         * 合并分组后的拣货单详情
         * 1. 复制首个记录的基础属性
         * 2. 生成新的唯一ID
         * 3. 累加分组内所有记录的数量
         * 4. 创建合并后的记录对象
         */
        List<OtcPrepPickingSlipDetail> mergedList = new ArrayList<>();
        for (List<OtcPrepPickingSlipDetail> details : groupedByKeys.values()) {
            OtcPrepPickingSlipDetail first = details.get(0);
            OtcPrepPickingSlipDetail merge = new OtcPrepPickingSlipDetail();
            BeanUtil.copy(first, merge);
            merge.setId(IdWorker.getId());
            int totalQty = 0;
            for (OtcPrepPickingSlipDetail detail : details) {
                totalQty += detail.getQty();
            }
            merge.setQty(totalQty);
            mergedList.add(merge);
        }

        /**
         * 按拣货单ID重新分组
         * 为后续排序和行号分配做准备
         * 保证每个拣货单内的记录独立处理
         */
        Map<Long, List<OtcPrepPickingSlipDetail>> groupedByPickingSlipId = new HashMap<>();
        for (OtcPrepPickingSlipDetail detail : mergedList) {
            groupedByPickingSlipId.computeIfAbsent(detail.getOtcPrepPickingSlipId(), k -> new ArrayList<>()).add(detail);
        }

        /**
         * 最终结果处理
         * 1. 按库位名称对每个拣货单的记录排序
         * 2. 为每个拣货单内的记录分配连续行号
         * 3. 合并所有结果到最终列表
         */
        List<OtcPrepPickingSlipDetail> result = new ArrayList<>();
        for (List<OtcPrepPickingSlipDetail> details : groupedByPickingSlipId.values()) {
            details.sort(Comparator.comparing(obj -> binLocationMap.get(obj.getBinLocationId()).getLocationName()));
            for (int idx = 0; idx < details.size(); idx++) {
                details.get(idx).setLineNum(idx + 1);
            }
            result.addAll(details);
        }

        return result;
    }


    /**
     * 构建拣货单详情
     *
     * @param workOrder         工单
     * @param binLocationDetail 库位详情
     * @return 拣货单详情
     */
    private OtcPrepPickingSlipDetail buildPickingSlipDetail(OtcPrepWorkorder workOrder,
                                                            BinLocationDetail binLocationDetail) {
        OtcPrepPickingSlipDetail slipDetail = new OtcPrepPickingSlipDetail();
        // 手动设置id
        slipDetail.setId(IdWorker.getId());
        slipDetail.setLineNum(0);
        // 拣货单id
        slipDetail.setOtcPrepPickingSlipId(workOrder.getOtcPrepPickingSlipId());
        // 库位信息
        slipDetail.setBinLocationDetailId(binLocationDetail.getId());
        slipDetail.setBinLocationId(binLocationDetail.getBinLocationId());
        // 产品信息
        slipDetail.setProductVersionId(binLocationDetail.getProductVersionId());
        slipDetail.setProductId(binLocationDetail.getProductId());

        // 已分配数量赋值
        slipDetail.setAllocateQty(0);
        // 拣货数量
        slipDetail.setPickedQty(0);
        slipDetail.setPutawayQty(0);
        return slipDetail;
    }

    /**
     * 锁定库位
     *
     * @param context 上下文
     */
    private void lockBinLocation(OtcPrepFilterBuildContextVO context) {
        // 拣货单映射
        Map<Long, OtcPrepPickingSlip> pickingSlipMap = StreamUtils.toMap(context.getPrepPickingSlipList(), IdModel::getId);
        // 构建锁定库位库存
        List<OtcPrepPickingSlipDetail> pickingSlipDetailList = context.getPrepPickingSlipDetailList();
        List<BinLocationDetailLocked> lockedList = pickingSlipDetailList.stream()
                .map(obj -> {
                    // 构建锁定 库位详情实体对象
                    BinLocationDetailLocked locked = new BinLocationDetailLocked();
                    locked.setId(IdWorker.getId());
                    locked.setBinLocationDetailId(obj.getBinLocationDetailId());
                    locked.setBinLocationId(obj.getBinLocationId());
                    locked.setProductId(obj.getProductId());
                    locked.setProductVersionId(obj.getProductVersionId());
                    locked.setQty(obj.getQty());
                    locked.setFinishQty(0);
                    locked.setLockedStatus(InventoryLockedStatusEnum.LOCKED.getStatus());
                    locked.setRefTableId(obj.getId());
                    locked.setRefTableName(OtcPrepPickingSlipDetail.class.getSimpleName());
                    locked.setRefTableRefNum(String.valueOf(obj.getLineNum()));
                    locked.setRefTableShowName(OtcPrepPickingSlip.class.getSimpleName());
                    Optional.ofNullable(pickingSlipMap.get(obj.getOtcPrepPickingSlipId()))
                            .ifPresent(slip -> locked.setRefTableShowRefNum(slip.getRefNum()));
                    return locked;
                })
                .toList();

        // 校验库位是否禁用
        BinLocationCheckUtil.checkStatuses(lockedList);

        // 锁定库位库存
        binLocationDetailLockedService.lockedBinLocationInventory(lockedList);

        // 填充库位锁信息
        Map<Long, Long> lockedIdMap = lockedList.stream()
                .collect(Collectors.toMap(BinLocationDetailLocked::getRefTableId, IdModel::getId));
        pickingSlipDetailList.forEach(obj -> obj.setBinLocationDetailLockedId(lockedIdMap.get(obj.getId())));
    }

    /**
     * 根据工单PageVO 构建更新工单集合
     *
     * @param prepWorkOrderList 工单集合
     * @return /
     */
    private List<OtcPrepWorkorder> buildUpdatePrepWorkOrderList(List<OtcPrepWorkorder> prepWorkOrderList) {
        return prepWorkOrderList.stream()
                .map(obj -> {
                    OtcPrepWorkorder update = new OtcPrepWorkorder();
                    update.setId(obj.getId());
                    update.setOtcPrepPickingSlipId(obj.getOtcPrepPickingSlipId());
                    update.setPrepWorkorderStatus(OtcPrepWorkorderStatusEnum.IN_PICKING.getStatus());
                    return update;
                })
                .toList();
    }

    /**
     * 处理中请求单
     *
     * @param context context
     * @return /
     */
    private List<OtcRequest> buildUpdateRequestList(OtcPrepFilterBuildContextVO context) {
        List<OtcWorkorder> workOrderList = context.getWorkorderList();
        // 工单集合
        List<Long> requestIdList = StreamUtils.distinctMap(workOrderList, OtcWorkorder::getOtcRequestId);
        List<OtcRequest> requestList = otcRequestService.listByIds(requestIdList);
        // 设置处理中
        requestList.forEach(obj -> {
            obj.setOtcRequestStatus(RequestStatusEnum.PROCESSING.getStatus());
            obj.setProcessStartTime(TimeUtils.now());
        });

        // 绑定到上下文中
        context.setOtcRequestList(requestList);
        return requestList;
    }

    /**
     * 根据工单PageVO 构建更新工单集合
     *
     * @param context 上下文
     */
    private void buildUpdateWorkOrderList(OtcPrepFilterBuildContextVO context) {
        List<OtcWorkorder> workorderList = context.getWorkorderList();
        // 更新工单信息
        workorderList.forEach(obj -> obj.setWorkorderPrepStatus(WorkOrderPrepStatusEnum.PROCESSING.getStatus()));
    }

    /**
     * 检查工单库存
     *
     * @param context 上下文
     */
    private void checkPrepWorkOrderInStock(OtcPrepFilterBuildContextVO context) {
        // 工单详情映射
        Map<Long, List<OtcPrepWorkorderDetail>> workOrderDetailGroupByWorkOrderIdMap = context.getWorkOrderDetailGroupByWorkOrderIdMap();

        // 工单映射
        Map<Long, OtcPrepWorkorder> workOrderMap = context.getPrepWorkOrderList()
                .stream()
                .collect(Collectors.toMap(OtcPrepWorkorder::getId, Function.identity()));

        // 获取产品id集合
        List<Long> productIdList = workOrderDetailGroupByWorkOrderIdMap.values()
                .stream()
                .flatMap(Collection::stream)
                .map(OtcPrepWorkorderDetail::getProductId)
                .toList();

        // 获取【产品id -> 真实有库存的库位详情集合】映射
        context.setRealAvailableGroupByPorductMap(binLocationDetailService.realAvailableGroupByProductId(
                Optional.ofNullable(context.getQuery())
                        .map(OtcPrepPickingSlipFilterBuildQuery::getFilter)
                        .map(OtcPrepWorkOrderListQuery::getBinLocationQuery)
                        .orElse(null),
                productIdList
        ));

        // 获取不足库存的工单
        List<WorkOrderNoEnoughAvailQtyVO> noEnoughList = pickingSlipService.findNoEnoughList(
                workOrderDetailGroupByWorkOrderIdMap,
                context.getRealAvailableGroupByPorductMap(),
                productStockMap -> StreamUtils.filterHasProductStock(workOrderDetailGroupByWorkOrderIdMap, productStockMap),
                (noEnough, detail) -> {
                    noEnough.setRefNum(workOrderMap.get(detail.getOtcPrepWorkorderId()).getRefNum());
                    noEnough.setId(detail.getOtcPrepWorkorderId());
                }
        );
        // 设置不足库存的工单到上下文
        context.setNoStockWorkOrderList(noEnoughList);
        // 删除库存不足的工单
        List<Long> noEnoughIdList = StreamUtils.distinctMap(noEnoughList, WorkOrderNoEnoughAvailQtyVO::getId);
        context.getPrepWorkOrderList().removeIf(obj -> noEnoughIdList.contains(obj.getId()));
    }

    /**
     * 记录Prep FilterBuild 日志
     * Prep工单: New -> InPicking , Prep拣货单: New , 工单 workorderPrepStatus -> Processing日志
     *
     * @param context 上下文
     */
    private void recordFilterBuildLog(OtcPrepFilterBuildContextVO context) {
        Map<Long, OtcPrepPickingSlip> pickingSlipMap = context.getPrepPickingSlipList()
                .stream()
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));
        // Prep工单: New -> InPicking
        for (OtcPrepWorkorder workorder : context.getPrepWorkOrderList()) {
            OtcPrepPickingSlip prepPickingSlip = pickingSlipMap.get(workorder.getOtcPrepPickingSlipId());
            // 工单: Filter Build PickingSlip
            OtcPrepWorkorderAuditLogHelper.recordLog(workorder, WorkorderLogConstant.FILTER_BUILD_STATUS,
                    prepPickingSlip.toLogBuild(), null, BaseTypeLogEnum.OPERATION.getType()
            );

            // Prep工单: New -> InPicking
            OtcPrepWorkorderAuditLogHelper.recordLog(workorder, OtcPrepWorkorderStatusEnum.IN_PICKING.getStatus(), null, null);
        }

        // 拣货单: New  日志
        OtcPrepPickingSlipAuditLogHelper.recordLog(context.getPrepPickingSlipList());

        // 工单 workorderPrepStatus -> Processing
        OtcWorkOrderAuditLogHelper.recordLog(context.getWorkorderList(), WorkorderLogConstant.PREP_WORKORDER_PROCESSING_DESCRIPTION_PREFIX, null, null);

        // 请求单: Processing 日志
        OtcRequestAuditLogHelper.recordLog(context.getOtcRequestList());
    }
}
