package cn.need.cloud.biz.model.bo.otc.pickingslip;

import cn.need.cloud.biz.model.bo.base.pickingslip.PickingSlipUnpickBO;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPutawaySlip;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPutawaySlipDetail;
import cn.need.cloud.biz.model.vo.base.pickingslip.PrepPickingSlipUnpickDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTC拣货单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "UnPick 对象")
public class OtcPrepPickingSlipUnpickBO extends PickingSlipUnpickBO<OtcPrepPutawaySlip, OtcPrepPutawaySlipDetail, PrepPickingSlipUnpickDetailVO> {

}