package cn.need.cloud.biz.mapper.feeconfig;


import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigStorageDetail;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigStorageDetailQuery;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigStorageDetailPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 仓库报价费用配置storage详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Mapper
public interface FeeConfigStorageDetailMapper extends SuperMapper<FeeConfigStorageDetail> {

    /**
     * 根据条件获取仓库报价费用配置storage详情列表
     *
     * @param query 查询条件
     * @return 仓库报价费用配置storage详情集合
     */
    default List<FeeConfigStorageDetailPageVO> listByQuery(@Param("qofcsd") FeeConfigStorageDetailQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取仓库报价费用配置storage详情分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 仓库报价费用配置storage详情集合
     */
    List<FeeConfigStorageDetailPageVO> listByQuery(@Param("qofcsd") FeeConfigStorageDetailQuery query, @Param("page") Page<?> page);
}