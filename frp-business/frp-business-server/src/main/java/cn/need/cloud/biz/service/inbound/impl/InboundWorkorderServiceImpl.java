package cn.need.cloud.biz.service.inbound.impl;

import cn.hutool.core.collection.CollUtil;
import cn.need.cloud.biz.cache.bean.ProductVersionCache;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.PrintStatusEnum;
import cn.need.cloud.biz.client.constant.enums.base.ShowLogEnum;
import cn.need.cloud.biz.client.constant.enums.inbound.*;
import cn.need.cloud.biz.client.constant.enums.warehouse.WarehousePrefixEnum;
import cn.need.cloud.biz.converter.inbound.InboundWorkorderConverter;
import cn.need.cloud.biz.mapper.inbound.InboundWorkorderMapper;
import cn.need.cloud.biz.model.bo.inbound.InboundRequestAuditContextBO;
import cn.need.cloud.biz.model.entity.inbound.InboundRequest;
import cn.need.cloud.biz.model.entity.inbound.InboundUnload;
import cn.need.cloud.biz.model.entity.inbound.InboundWorkorder;
import cn.need.cloud.biz.model.entity.inbound.InboundWorkorderDetail;
import cn.need.cloud.biz.model.entity.product.ProductVersion;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.inbound.InboundPalletQuery;
import cn.need.cloud.biz.model.query.inbound.InboundWorkorderQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.inbound.request.InboundRequestAuditVO;
import cn.need.cloud.biz.model.vo.inbound.request.InboundRequestDetailVO;
import cn.need.cloud.biz.model.vo.inbound.workorder.InboundMeasureByProductVO;
import cn.need.cloud.biz.model.vo.inbound.workorder.InboundMeasureVO;
import cn.need.cloud.biz.model.vo.inbound.workorder.InboundWorkorderDetailVO;
import cn.need.cloud.biz.model.vo.inbound.workorder.InboundWorkorderVO;
import cn.need.cloud.biz.model.vo.page.InboundWorkorderPageVO;
import cn.need.cloud.biz.model.vo.warehouse.PalletTemplateVO;
import cn.need.cloud.biz.service.helper.auditshowlog.inbound.InboundWorkOrderAuditLogHelper;
import cn.need.cloud.biz.service.inbound.*;
import cn.need.cloud.biz.service.product.ProductVersionService;
import cn.need.cloud.biz.service.warehouse.PalletTemplateService;
import cn.need.cloud.biz.util.*;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Clock;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 入库工单服务实现类
 * </p>
 * <p>
 * 该类实现了入库工单相关的业务逻辑，包括工单的创建、查询、状态更新以及测量确认等功能。
 * 入库工单是入库流程中的关键环节，记录货品到达仓库后的处理过程，包括确认到货、测量确认、
 * 卸货完成等操作。该服务与入库请求单、卸货单等其他入库相关服务紧密协作。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class InboundWorkorderServiceImpl extends SuperServiceImpl<InboundWorkorderMapper, InboundWorkorder> implements InboundWorkorderService {

    /**
     * 入库请求单服务，使用懒加载避免循环依赖
     */
    @Resource
    @Lazy
    private InboundRequestService inboundRequestService;

    /**
     * 产品版本服务，用于管理产品规格信息
     */
    @Resource
    private ProductVersionService productVersionService;

    /**
     * 托盘模板服务，用于获取托盘相关配置
     */
    @Resource
    private PalletTemplateService palletTemplateService;

    /**
     * 入库工单明细服务，用于管理工单中的详细货品信息
     */
    @Resource
    private InboundWorkorderDetailService inboundWorkorderDetailService;

    /**
     * 入库卸货服务，使用懒加载避免循环依赖
     */
    @Resource
    @Lazy
    private InboundUnloadService inboundUnloadService;

    /**
     * 入库请求明细服务，用于获取入库请求的详细信息
     */
    @Resource
    private InboundRequestDetailService inboundRequestDetailService;

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    /**
     * 根据查询条件获取入库工单列表
     * <p>
     * 该方法用于按照指定的查询条件查询入库工单列表。
     * </p>
     *
     * @param query 查询条件
     * @return 入库工单分页视图对象列表
     * <p>
     * TODO: 方法缺少分页处理能力，仅返回符合条件的所有记录，可能导致大数据量时的性能问题
     * 优化建议：考虑添加分页参数或限制返回数量
     */
    @Override
    public List<InboundWorkorderPageVO> listByQuery(InboundWorkorderQuery query) {
        return mapper.listByQuery(query);
    }

    /**
     * 根据查询条件分页获取入库工单
     * <p>
     * 该方法按照指定的查询条件和分页参数查询入库工单列表。
     * </p>
     *
     * @param search 包含查询条件和分页参数的搜索对象
     * @return 包含分页信息的入库工单分页视图对象
     * <p>
     * TODO: 方法中没有填充工单相关的详细信息，如产品信息、仓库信息等
     * 优化建议：添加相关信息的填充逻辑，提供更完整的数据
     */
    @Override
    public PageData<InboundWorkorderPageVO> pageByQuery(PageSearch<InboundWorkorderQuery> search) {
        Page<InboundWorkorder> page = Conditions.page(search, entityClass);
        List<InboundWorkorderPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取入库工单详情
     * <p>
     * 该方法用于获取指定ID的入库工单详细信息，包括其关联的详情信息。
     * </p>
     *
     * @param id 入库工单ID
     * @return 入库工单视图对象，包含详细信息
     * @throws BusinessException 如果找不到指定ID的入库工单，则抛出业务异常
     */
    @Override
    public InboundWorkorderVO detailById(Long id) {
        InboundWorkorder entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in InboundWorkorder");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "InboundWorkorder", id));
        }
        return buildInboundWorkorderVO(entity);
    }

    /**
     * 根据参考编号获取入库工单详情
     * <p>
     * 该方法用于获取指定参考编号的入库工单详细信息，包括其关联的详情信息。
     * </p>
     *
     * @param refNum 入库工单参考编号
     * @return 入库工单视图对象，包含详细信息
     * @throws BusinessException 如果找不到指定参考编号的入库工单，则抛出业务异常
     *                           <p>
     *                                                                                                                                                                                       TODO: 方法中调用了未定义的getByRefNum方法，需要补充实现
     *                                                                                                                                                                                       优化建议：实现getByRefNum方法或使用Lambda查询
     */
    @Override
    public InboundWorkorderVO detailByRefNum(String refNum) {
        InboundWorkorder entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("RefNum: " + refNum + " not found in InboundWorkorder");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "InboundWorkorder", "refNum", refNum));
        }
        return buildInboundWorkorderVO(entity);
    }

    /**
     * 获取入库工单的下拉列表数据
     * <p>
     * 该方法用于获取指定字段的唯一值列表，用于前端下拉选择框。
     * </p>
     *
     * @param query 包含列名列表的查询条件
     * @return 下拉列表视图对象列表
     * <p>
     * TODO: 缺少对返回数据量的限制，可能导致大数据量时的性能问题
     * 优化建议：添加返回数据量限制，避免过多数据返回
     */
    @Override
    public List<DropProVO> distinctValue(InboundWorkorderQuery query) {
        return DropListUtil.dropProList(query.getColumnNameList(),
                InboundWorkorder.class,
                item -> mapper.dropProList(item, query));
    }

    /**
     * 确认到货
     * <p>
     * 该方法用于确认货物已到达仓库，更新入库工单状态为"产品已到达"，
     * 并同步更新对应的入库请求单状态为"处理中"。
     * 这标志着入库流程正式开始。
     * </p>
     *
     * @param id 入库工单id
     * @throws BusinessException 如果工单状态不是"新建"状态，则抛出业务异常
     *                           <p>
     *                                                                                                                                                                                       TODO: 方法中没有验证入库工单是否存在，如果ID不存在可能导致NullPointerException
     *                                                                                                                                                                                       优化建议：添加对工单存在性的验证
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void productArrived(Long id) {
        //获取该入库工单请求单id
        InboundWorkorder inboundWorkorder = super.getById(id);
        //状态校验
        Validate.isTrue(StringUtil.equals(InboundWorkOrderStatusEnum.NEW.getStatus(), inboundWorkorder.getInboundWorkorderStatus()),
                String.format(ErrorMessages.STATUS_REQUIRED, "InboundWorkorder", "NEW", inboundWorkorder.getInboundWorkorderStatus()));
        //实际到达时间
        LocalDateTime arrivedTime = ZonedDateTime.now(Clock.systemUTC()).toLocalDateTime();
        //更新入库工单状态
        updateStatus(inboundWorkorder, arrivedTime, InboundWorkOrderStatusEnum.PRODUCT_ARRIVED.getStatus());
        //更新入库请求单状态
        inboundRequestService.updateRequestStatus(inboundWorkorder.getInboundRequestId(),
                InboundRequestStatusEnum.PROCESSING.getStatus(), arrivedTime);
    }

    /**
     * 更新入库工单状态
     * <p>
     * 该方法用于更新指定ID入库工单的状态。
     * </p>
     *
     * @param id     入库工单ID
     * @param status 目标状态
     *               <p>
     *                                                                                                   TODO: 缺少对状态值合法性的验证，可能设置了不支持的状态值
     *                                                                                                   优化建议：添加对状态值合法性的验证逻辑
     */
    @Override
    public void updateStatus(Long id, String status) {
        InboundWorkorder inboundWorkorder = getById(id);
        inboundWorkorder.setInboundWorkorderStatus(status);
        mapper.updateById(inboundWorkorder);
        //记录showLog日志
        InboundWorkOrderAuditLogHelper.recordLog(inboundWorkorder);
    }

    /**
     * 更新入库工单状态
     * <p>
     * 该方法用于更新指定ID入库工单的状态和卸货状态。
     * </p>
     *
     * @param id              入库工单ID
     * @param workOrderStatus 目标状态
     * @param unloadStatus    卸货状态
     *                        <p>
     *                                                                                                                                                                  TODO: 方法存在重载，但参数含义不同，可能导致误用
     *                                                                                                                                                                  优化建议：使用不同的方法名，如updateWorkOrderStatus和updateWorkOrderWithUnloadStatus
     */
    @Override
    public void updateStatus(Long id, String workOrderStatus, String unloadStatus) {
        InboundWorkorder inboundWorkorder = getById(id);
        inboundWorkorder.setInboundWorkorderStatus(workOrderStatus);
        inboundWorkorder.setInboundWorkorderUnloadStatus(unloadStatus);
        super.update(inboundWorkorder);
        //记录showLog日志
        InboundWorkOrderAuditLogHelper.recordLog(
                inboundWorkorder
        );
    }

    /**
     * 更新入库工单状态
     * <p>
     * 该方法用于更新入库工单的状态和到达时间，并记录状态变更日志。
     * </p>
     *
     * @param inboundWorkorder 入库工单对象
     * @param arrivedTime      实际到达时间
     * @param status           更新状态
     *                         <p>
     *                                                                                                                                                                         TODO: 方法名称和参数不匹配，arrivedTime不一定是到达时间，可能是任何状态变更时间
     *                                                                                                                                                                         优化建议：重命名参数为statusChangeTime，更准确地表示其含义
     */
    private void updateStatus(InboundWorkorder inboundWorkorder, LocalDateTime arrivedTime, String status) {
        //填充到达时间
        inboundWorkorder.setActualArrivalDate(arrivedTime);
        //填充入库工单状态
        inboundWorkorder.setInboundWorkorderStatus(status);
        super.update(inboundWorkorder);
        //记录日志
        InboundWorkOrderAuditLogHelper.recordLog(inboundWorkorder);
    }

    /**
     * 测量确认接口
     * <p>
     * 该方法用于确认产品的测量信息，更新产品规格并标记工单详情为已测量。
     * 测量确认是入库流程中的一个重要步骤，确保产品的实际规格符合系统记录。
     * </p>
     *
     * @param measureVO 测量信息，包含产品尺寸、重量等信息
     * @throws BusinessException 如果测量数据验证失败，则抛出业务异常
     *                           <p>
     *                                                                                                                                                                                       TODO: 缺少对measureVO字段完整性的验证，可能导致部分信息丢失
     *                                                                                                                                                                                       优化建议：添加必要字段的非空校验
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirm(InboundMeasureVO measureVO) {
        //获取产品测量状态
        dataValid(measureVO);
        //更新产品规格
        productVersionService.updateMeasureStatus(measureVO);
        //更新入库工单测量状态
        inboundWorkorderDetailService.updateMeasure(measureVO.getWorkOrderId(), Boolean.TRUE, measureVO.getProductVersionId(), null);
    }

    /**
     * 按产品测量确认
     * <p>
     * 该方法用于确认单个产品的测量信息，更新产品规格并标记工单详情为已测量。
     * 与confirm方法不同，此方法专门用于处理单个产品的测量确认。
     * </p>
     *
     * @param measureVO 测量信息，包含产品尺寸、重量等参数
     * @throws BusinessException 如果产品已经被测量过，则抛出业务异常
     *                           <p>
     *                                                                                                                                                                                       TODO: 方法中没有对measureVO参数的合法性进行全面验证
     *                                                                                                                                                                                       优化建议：增加对测量参数的合法性验证，如尺寸不能为负值等
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmByProduct(InboundMeasureByProductVO measureVO) {
        //产品测量状态校验
        InboundWorkorderDetail workorderDetail = inboundWorkorderDetailService.getById(measureVO.getWorkOrderDetailId());
        Validate.isTrue(!workorderDetail.getRemeasureFlag(),
                String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "The product has already been measured and cannot be measured again"));
        //获取入库工单详情
        InboundWorkorderDetail inboundWorkorderDetail = inboundWorkorderDetailService.getById(measureVO.getWorkOrderDetailId());
        //更新产品规格
        ProductVersion pro = productVersionService.getById(inboundWorkorderDetail.getProductVersionId());
        ProductVersion productVersion = BeanUtil.copyNew(measureVO, ProductVersion.class);
        productVersion.setId(inboundWorkorderDetail.getProductVersionId());
        productVersion.setVersion(pro.getVersion());
        productVersion.setProductVersionInt(pro.getProductVersionInt());
        productVersion.setProductRemeasureType(InboundMeasureTypeEnum.Remeasured.getStatus());
        productVersionService.update(productVersion);
        //更新入库工单测量状态
        inboundWorkorderDetailService.updateMeasure(null, Boolean.TRUE, inboundWorkorderDetail.getProductVersionId(), measureVO.getWorkOrderDetailId());
    }

    /**
     * 标记工单为已打印
     * <p>
     * 该方法用于更新入库工单的打印状态，并记录打印日志。如果打印成功且工单状态为"产品已到达"，
     * 则同时更新工单状态为"工单已打印"，表示该工单已准备好进入下一步处理。
     * </p>
     *
     * @param printQuery 打印查询参数，包含工单ID、打印状态和备注信息
     *                   <p>
     *                                                                                                                               TODO: 方法中的条件判断较为复杂，应该提取为单独的方法提高可读性
     *                                                                                                                               优化建议：将状态更新的逻辑抽取为独立方法，简化当前方法
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markPrinted(PrintQuery printQuery) {
        //初始化数据
        InboundWorkorder workorder = super.getById(printQuery.getId());
        workorder.setPrintStatus(printQuery.getPrintStatus());
        //记录showLog日志打印状态
        InboundWorkOrderAuditLogHelper.recordLog(
                workorder,
                StringUtil.format("{} {}", ShowLogEnum.PRINT.getStatus(), printQuery.getPrintStatus()),
                BaseTypeLogEnum.OPERATION.getType(),
                printQuery.getNote(),
                StringUtil.format("print {}", workorder.getRefNum())
        );
        //更新打印状态
        if (StringUtil.equals(printQuery.getPrintStatus(), PrintStatusEnum.SUCCESS.getStatus()) &&
                ObjectUtil.equals(workorder.getInboundWorkorderStatus(), InboundWorkOrderStatusEnum.PRODUCT_ARRIVED.getStatus())) {
            //更新入库工单状态
            workorder.setInboundWorkorderStatus(InboundWorkOrderStatusEnum.WORK_ORDER_PRINTED.getStatus());
            //记录showLog日志工单状态
            InboundWorkOrderAuditLogHelper.recordLog(workorder);
        }
        //持久化数据库
        mapper.updateById(workorder);
    }

    /**
     * 获取满足条件的入库工单ID集合
     * <p>
     * 该方法用于根据提供的查询条件获取符合条件的入库工单ID集合。
     * 支持按工单编号、物流跟踪单号、请求单编号等条件进行查询。
     * </p>
     *
     * @param condition 包含查询条件的入库托盘查询对象
     * @return 符合条件的工单ID集合，如果没有符合条件的工单，则返回包含-1的集合
     * <p>
     * TODO: 返回包含-1的集合作为空结果处理不够优雅
     * 优化建议：直接返回空集合，由调用方处理空结果情况
     */
    @Override
    public Set<Long> getInboundWorkOrderIds(InboundPalletQuery condition) {
        boolean flag = false;
        LambdaQueryWrapper<InboundWorkorder> queryWrapper = new LambdaQueryWrapper<>();
        //工单refNum
        Set<String> workOrderOfRefNumList = condition.getWorkOrderOfRefNumList();
        if (ObjectUtil.isNotEmpty(workOrderOfRefNumList)) {
            queryWrapper.in(InboundWorkorder::getRefNum, workOrderOfRefNumList);
            flag = true;
        }
        //请求单物流跟踪单号
        Set<String> requestTrackingNumList = condition.getRequestTrackingNumList();
        if (ObjectUtil.isNotEmpty(requestTrackingNumList)) {
            queryWrapper.in(InboundWorkorder::getTrackingNum, requestTrackingNumList);
            flag = true;
        }
        //请求单refNum
        Set<String> requestOfRefNumList = condition.getRequestOfRefNumList();
        if (ObjectUtil.isNotEmpty(requestOfRefNumList)) {
            queryWrapper.in(InboundWorkorder::getRequestSnapshotRefNum, requestOfRefNumList);
            flag = true;
        }
        //请求单requestRefNum
        Set<String> requestOfRequestRefNumList = condition.getRequestOfRequestRefNumList();
        if (ObjectUtil.isNotEmpty(requestOfRequestRefNumList)) {
            queryWrapper.in(InboundWorkorder::getRequestSnapshotRequestRefNum, requestOfRequestRefNumList);
            flag = true;
        }
        //判断上述条件是否都为空,flag 值为false则条件都为空返回空集合即可
        if (!flag) {
            return CollUtil.newHashSet();
        }
        //获取工单集合
        List<InboundWorkorder> list = list(queryWrapper);
        //判空检验，若无查到id则给一个假id
        if (ObjectUtil.isEmpty(list)) {
            return CollUtil.newHashSet(-1L);
        }
        //返回工单id集合
        return list.stream().map(InboundWorkorder::getId).collect(Collectors.toSet());
    }

    /**
     * 完成卸货
     * <p>
     * 该方法用于标记入库工单卸货完成，会执行以下验证和操作：
     * 1. 验证工单是否已经处于完成状态
     * 2. 验证所有产品是否都已上架完成
     * 3. 验证所有工单详情是否都已完成卸货
     * 4. 更新工单卸货状态和工单状态
     * 5. 更新关联的入库请求单状态
     * </p>
     *
     * @param id 入库工单ID
     * @throws BusinessException 如果工单已完成、产品未全部上架或详情未全部卸货完成，则抛出业务异常
     *                           <p>
     *                                                                                                                                                                                       TODO: 方法中包含多次数据库查询，可能影响性能
     *                                                                                                                                                                                       优化建议：考虑优化查询策略，减少数据库访问次数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishUnload(Long id) {
        //获取入库工单信息
        InboundWorkorder inboundWorkorder = getById(id);
        //校验工单状态
        Validate.isTrue(!StringUtil.equals(inboundWorkorder.getInboundWorkorderStatus(), InboundWorkOrderStatusEnum.PROCESSED.getStatus()),
                String.format(ErrorMessages.STATUS_INVALID_OPERATION, "finish unload", "InboundWorkorder", inboundWorkorder.getInboundWorkorderStatus()));
        //校验所有产品是否都已上架
        boolean exist = inboundUnloadService.exist(id, InboundUnloadStatusEnum.PROCESSED.getStatus());
        Validate.isTrue(!exist,
                String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Cannot finish unload before completing put away"));
        //获取工单详情
        List<InboundWorkorderDetailVO> detailList = inboundWorkorderDetailService.listByInboundWorkorderId(id);
        //校验工单详情是否都已完成卸货
        List<InboundWorkorderDetailVO> list = detailList.stream()
                .filter(item -> ObjectUtil.notEqual(item.getFinishQty(), item.getNeedReceiveQty()))
                .toList();
        Validate.isTrue(ObjectUtil.isEmpty(list),
                String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Workorder with refNum " + inboundWorkorder.getRefNum() + " already has a finished unload record"));
        //更新工单卸货状态
        updateUnloadStatus(id, InboundWorkOrderUnloadEnum.PROCESSED.getStatus());
        //更新工单状态
        updateStatus(id, InboundWorkOrderUnloadEnum.PROCESSED.getStatus());
        //更新请求单状态
        inboundRequestService.updateRequestStatus(inboundWorkorder.getInboundRequestId(), InboundRequestStatusEnum.PROCESSED.getStatus(), null);
    }

    /**
     * 生成入库工单
     * <p>
     * 该方法用于根据入库请求单信息生成新的入库工单和工单详情，并记录审计日志。
     * 这是入库流程从请求阶段转入执行阶段的关键步骤。
     * </p>
     *
     * @param context 入库请求审核上下文，包含审核参数和请求信息
     *                <p>
     *                                                                                                          TODO: 方法缺少对生成工单结果的验证
     *                                                                                                          优化建议：添加对生成结果的验证，确保工单和详情都成功创建
     */
    @Override
    public void generateWorkOrder(InboundRequestAuditContextBO context) {
        //获取前端参数
        InboundRequestAuditVO param = context.getParam();
        //获取请求单信息
        InboundRequest request = inboundRequestService.getById(param.getId());
        //获取请求单详情
        List<InboundRequestDetailVO> inboundRequestDetailList = inboundRequestDetailService.listByInboundRequestId(param.getId());
        //构建入库工单
        InboundWorkorder inboundWorkorder = buildInboundWorkOrder(request, param);
        //持久化工单信息
        super.insert(inboundWorkorder);
        //工单refNum加载到上下文
        context.setInboundWorkOrderRefNum(inboundWorkorder.getRefNum());
        //异步记录日志
        InboundWorkOrderAuditLogHelper.recordLog(
                inboundWorkorder,
                StringUtil.format("Create by {}/{}", request.getRefNum(), request.getRequestRefNum())
        );
        //持久化工单详情
        inboundWorkorderDetailService.generateInboundWorkOrderDetail(inboundRequestDetailList, inboundWorkorder.getId(), param.getWarehouseId());
    }

    /**
     * 更新入库工单卸货状态
     * <p>
     * 该方法用于更新指定入库工单的卸货状态，不影响工单的其他状态字段。
     * </p>
     *
     * @param id     入库工单ID
     * @param status 卸货状态
     *               <p>
     *                                                                                                   TODO: 缺少对状态值合法性的验证
     *                                                                                                   优化建议：添加对卸货状态值合法性的验证逻辑
     */
    @Override
    public void updateUnloadStatus(Long id, String status) {
        //创建工单对象
        InboundWorkorder inboundWorkorder = super.getById(id);
        //填充状态
        inboundWorkorder.setInboundWorkorderUnloadStatus(status);
        //持久化数据库
        mapper.updateById(inboundWorkorder);
    }

    /**
     * 根据请求单ID更新工单状态
     * <p>
     * 该方法用于根据关联的入库请求单ID查找工单，并更新其状态。
     * 如果找不到关联的工单，则静默返回而不抛出异常。
     * </p>
     *
     * @param id     入库请求单ID
     * @param status 要更新的工单状态
     *               <p>
     *                                                                                                   TODO: 方法中缺少对异常情况的处理，如更新失败时没有日志记录
     *                                                                                                   优化建议：添加异常处理和日志记录，提高可追踪性
     */
    @Override
    public void updateStatusByRequestId(Long id, String status) {
        //创建工单对象
        InboundWorkorder inboundWorkorder = getByRequestId(id);
        //判空
        if (ObjectUtil.isEmpty(inboundWorkorder)) {
            return;
        }
        //填充状态
        inboundWorkorder.setInboundWorkorderStatus(status);
        //持久化数据库
        mapper.updateById(inboundWorkorder);
        //异步记录日志
        InboundWorkOrderAuditLogHelper.recordLog(inboundWorkorder);
    }

    /**
     * 根据请求单ID获取工单信息
     * <p>
     * 该方法用于根据入库请求单ID查询关联的入库工单。
     * </p>
     *
     * @param id 入库请求单ID
     * @return 关联的入库工单对象，如果不存在则返回null
     * <p>
     * TODO: 如果一个请求单对应多个工单，当前实现只会返回第一个
     * 优化建议：考虑返回所有关联工单，或者明确在方法注释中说明只返回一个
     */
    @Override
    public InboundWorkorder getByRequestId(Long id) {
        return lambdaQuery()
                .eq(InboundWorkorder::getInboundRequestId, id)
                .one();
    }

    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 构建入库工单
     * <p>
     * 该方法用于根据入库请求单和审批信息构建新的入库工单对象。
     * 包括设置工单基本信息、状态、参考编号等。
     * </p>
     *
     * @param inboundRequest 入库请求单信息
     * @param inboundAuditVO 入库请求单审批信息
     * @return 构建好的入库工单对象
     * <p>
     * TODO: 方法中直接使用null设置ID字段，不太清晰
     * 优化建议：使用更明确的方法如IdWorker.getId()生成新ID
     */
    private InboundWorkorder buildInboundWorkOrder(InboundRequest inboundRequest, InboundRequestAuditVO inboundAuditVO) {
        //生成入库工单
        InboundWorkorder inboundWorkorder = BeanUtil.copyNew(inboundRequest, InboundWorkorder.class);
        //填充入库请求单refNum
        inboundWorkorder.setRequestSnapshotRefNum(inboundRequest.getRefNum());
        //入库请求单请求refNum
        inboundWorkorder.setRequestSnapshotRequestRefNum(inboundRequest.getRequestRefNum());
        //入库请求单id
        inboundWorkorder.setInboundRequestId(inboundRequest.getId());
        //入库请求单预计到达时间
        inboundWorkorder.setEstimateArrivalDate(inboundAuditVO.getEstimateArrivalDate());
        //入库请求单备注
        inboundWorkorder.setRequestSnapshotNote(inboundRequest.getNote());
        //入库工单状态
        inboundWorkorder.setInboundWorkorderStatus(InboundWorkOrderStatusEnum.NEW.getStatus());
        //卸货状态
        inboundWorkorder.setInboundWorkorderUnloadStatus(InboundWorkOrderStatusEnum.NEW.getStatus());
        //上架状态
        inboundWorkorder.setInboundWorkorderPutawayStatus(InboundPutAwayStatusEnum.NEW.getStatus());
        //打印状态
        inboundWorkorder.setPrintStatus(PrintStatusEnum.NONE.getStatus());
        //填充仓库id
        inboundWorkorder.setWarehouseId(inboundAuditVO.getWarehouseId());
        //生成refNum
        inboundWorkorder.setRefNum(FormatUtil.substringAfter(RefNumTypeEnum.INBOUND_WORKORDER.getCode(), inboundAuditVO.getWarehouseId(), WarehousePrefixEnum.PREFIX.getPrefix()));
        inboundWorkorder.setId(null);
        inboundWorkorder.setCreateTime(TimeUtils.now());
        inboundWorkorder.setUpdateTime(TimeUtils.now());
        return inboundWorkorder;
    }

    /**
     * 构建入库工单VO对象
     * <p>
     * 该方法用于将入库工单实体对象转换为视图对象，并填充相关的详情信息、
     * 产品信息、仓库信息等。这样可以提供更完整的数据给前端展示。
     * </p>
     *
     * @param entity 入库工单对象
     * @return 包含详细信息的入库工单VO对象
     * <p>
     * TODO: 该方法包含多次缓存和服务调用，可能影响性能
     * 优化建议：考虑批量获取数据，减少服务调用次数
     */
    private InboundWorkorderVO buildInboundWorkorderVO(InboundWorkorder entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 包含详细信息的入库工单VO对象
        InboundWorkorderVO inboundWorkorderVO = Converters.get(InboundWorkorderConverter.class).toVO(entity);
        //获取入库工单详情
        List<InboundWorkorderDetailVO> inboundWorkorderDetailList = inboundWorkorderDetailService.listByInboundWorkorderId(inboundWorkorderVO.getId());
        if (ObjectUtil.isEmpty(inboundWorkorderDetailList)) {
            return inboundWorkorderVO;
        }
        //填充产品默认打托模板
        fillPalletTemplate(inboundWorkorderDetailList);
        // 填充产品信息
        ProductVersionCacheUtil.filledProductVersion(inboundWorkorderDetailList);
        //填充仓库信息
        WarehouseCacheUtil.filledWarehouse(inboundWorkorderVO);
        //填充计算的数量
        fillQty(inboundWorkorderDetailList);
        //填充入库工单详情
        inboundWorkorderVO.setDetails(inboundWorkorderDetailList);
        //返回入库工单详情
        return inboundWorkorderVO;
    }

    /**
     * 填充计算的数量
     * <p>
     * 该方法用于为入库工单详情填充计算得到的数量信息，如差异数量、总卸货数量和总上架数量等。
     * 这些数量是根据关联的卸货单数据计算得出的。
     * </p>
     *
     * @param inboundWorkOrderDetailList 入库工单详情集合
     *                                   <p>
     *                                                                                                                                                                                                                                               TODO: 方法中的计算逻辑比较复杂，且没有对异常情况处理
     *                                                                                                                                                                                                                                               优化建议：添加异常处理，并考虑将计算逻辑拆分为更小的方法
     */
    private void fillQty(List<InboundWorkorderDetailVO> inboundWorkOrderDetailList) {
        //获取入库详情id
        Set<Long> inboundWorkOrderDetailIdList = inboundWorkOrderDetailList
                .stream()
                .map(InboundWorkorderDetailVO::getId)
                .collect(Collectors.toSet());
        //获取入库卸货单详情
        List<InboundUnload> inboundUnloadList = inboundUnloadService.listByInboundWorkOrderDetailIds(inboundWorkOrderDetailIdList);
        //按入库详情id映射卸货单卸货数量
        Map<Long, Integer> unloadNumMap = inboundUnloadList
                .stream()
                .collect(Collectors.groupingBy(InboundUnload::getInboundWorkorderDetailId, Collectors.summingInt(InboundUnload::getQty)));
        //按入库详情id映射卸货单上架数量
        Map<Long, Integer> putAwayNumMap = inboundUnloadList
                .stream()
                .collect(Collectors.groupingBy(InboundUnload::getInboundWorkorderDetailId, Collectors.summingInt(inboundUnload -> inboundUnload.getRegularPutawayQty() + inboundUnload.getPalletPutawayQty())));
        //遍历入库详情
        inboundWorkOrderDetailList.forEach(item -> {
            //填充差异数量
            item.setDiffQty(item.getNeedReceiveQty() - item.getQty());
            //填充入库详情总卸货数量
            item.setTotalUnloadQty(unloadNumMap.getOrDefault(item.getId(), 0));
            //填充入库详情总上架数量
            item.setTotalPutAwayQty(putAwayNumMap.getOrDefault(item.getId(), 0));
        });
    }

    /**
     * 填充产品默认打托模板
     * <p>
     * 该方法用于为入库工单详情填充产品的默认打托模板信息，便于后续打托操作。
     * </p>
     *
     * @param inboundWorkorderDetailList 入库工单详情列表
     *                                   <p>
     *                                                                                                                                                                                                                                               TODO: 方法没有处理模板不存在的情况，可能导致空指针异常
     *                                                                                                                                                                                                                                               优化建议：添加空值检查，避免空指针异常
     */
    private void fillPalletTemplate(List<InboundWorkorderDetailVO> inboundWorkorderDetailList) {
        //获取产品id
        List<Long> productVersionIdList = inboundWorkorderDetailList.stream().map(InboundWorkorderDetailVO::getProductVersionId).toList();

        //根据id映射产品默认打托模板信息
        Map<Long, PalletTemplateVO> palletTemplateMap = ObjectUtil.toMap(palletTemplateService.listByProductId(productVersionIdList), PalletTemplateVO::getProductVersionId);

        //填充模板信息
        for (InboundWorkorderDetailVO inboundWorkorderDetailVO : inboundWorkorderDetailList) {
            inboundWorkorderDetailVO.setPalletTemplate(palletTemplateMap.get(inboundWorkorderDetailVO.getProductVersionId()));
        }
    }

    /**
     * 检验测量数据
     * <p>
     * 该方法用于检验测量数据的有效性，确保各项测量参数符合要求。
     * 主要验证：
     * 1. 工单状态必须处于"工单已打印"或"处理中"状态
     * 2. 产品未被重新测量过
     * </p>
     *
     * @param measureVO 测量信息视图对象
     * @throws BusinessException 如果测量数据验证失败，则抛出业务异常
     *                           <p>
     *                                                                                                                                                                                       TODO: 方法中缺少对长宽高单位的验证，可能导致单位不统一的问题
     *                                                                                                                                                                                       优化建议：添加单位验证和标准化处理
     */
    private void dataValid(InboundMeasureVO measureVO) {
        //校验工单状态
        InboundWorkorder inboundWorkorder = super.getById(measureVO.getWorkOrderId());
        //状态容器
        List<String> statusList = Lists.arrayList(
                InboundWorkOrderStatusEnum.WORK_ORDER_PRINTED.getStatus(),
                InboundWorkOrderStatusEnum.PROCESSING.getStatus()

        );
        Validate.isTrue(
                statusList.contains(inboundWorkorder.getInboundWorkorderStatus()),
                String.format(ErrorMessages.STATUS_REQUIRED, "InboundWorkorder " + inboundWorkorder.getRefNum(),
                        "WORK_ORDER_PRINTED or PROCESSING", inboundWorkorder.getInboundWorkorderStatus())
        );
        ProductVersionCache productVersionCache = ProductVersionCacheUtil.getById(measureVO.getProductVersionId());
        Validate.isTrue(!StringUtil.equals(productVersionCache.getProductRemeasureType(), InboundMeasureTypeEnum.Remeasured.getStatus()),
                String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Product " + productVersionCache.getSupplierSku() + " has already been remeasured"));
    }

}
