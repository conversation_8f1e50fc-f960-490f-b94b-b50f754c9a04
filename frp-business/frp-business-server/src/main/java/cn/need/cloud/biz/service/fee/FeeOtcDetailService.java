package cn.need.cloud.biz.service.fee;


import cn.need.cloud.biz.model.entity.fee.FeeOtcDetail;
import cn.need.cloud.biz.model.param.fee.create.FeeOtcDetailCreateParam;
import cn.need.cloud.biz.model.param.fee.update.FeeOtcDetailUpdateParam;
import cn.need.cloud.biz.model.query.fee.FeeOtcDetailQuery;
import cn.need.cloud.biz.model.vo.fee.FeeOtcDetailVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeOtcDetailPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 费用详情otc service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public interface FeeOtcDetailService extends SuperService<FeeOtcDetail> {

    /**
     * 根据参数新增费用详情otc
     *
     * @param createParam 请求创建参数，包含需要插入的费用详情otc的相关信息
     * @return 费用详情otcID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(FeeOtcDetailCreateParam createParam);


    /**
     * 根据参数更新费用详情otc
     *
     * @param updateParam 请求创建参数，包含需要更新的费用详情otc的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(FeeOtcDetailUpdateParam updateParam);

    /**
     * 根据查询条件获取费用详情otc列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个费用详情otc对象的列表(分页)
     */
    List<FeeOtcDetailPageVO> listByQuery(FeeOtcDetailQuery query);

    /**
     * 根据查询条件获取费用详情otc列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个费用详情otc对象的列表(分页)
     */
    PageData<FeeOtcDetailPageVO> pageByQuery(PageSearch<FeeOtcDetailQuery> search);

    /**
     * 根据ID获取费用详情otc
     *
     * @param id 费用详情otcID
     * @return 返回费用详情otcVO对象
     */
    FeeOtcDetailVO detailById(Long id);

    /**
     * 根据费用详情otc唯一编码获取费用详情otc
     *
     * @param refNum 费用详情otc唯一编码
     * @return 返回费用详情otcVO对象
     */
    FeeOtcDetailVO detailByRefNum(String refNum);


    /**
     * 根据费用otcid获取费用详情otc集合
     *
     * @param feeOtcId 费用otcid
     * @return 费用详情otc集合
     */
    List<FeeOtcDetailVO> listByFeeOtcId(Long feeOtcId);

}