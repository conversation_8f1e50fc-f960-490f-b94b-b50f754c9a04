package cn.need.cloud.biz.model.entity.otc;

import cn.need.cloud.biz.model.entity.base.pickingslip.PrepPickingSlipModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * OTC预提货单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otc_prep_picking_slip")
public class OtcPrepPickingSlip extends PrepPickingSlipModel {

    /**
     * 预拣货状态
     */
    @TableField
    private String prepPickingSlipStatus;

    /**
     * 订单类型
     */
    @TableField
    private String orderType;

    /**
     * 拣货id
     */
    @TableField
    private Long otcPickingSlipId;

    /**
     * 是否有特定运输要求
     */
    @TableField
    private Boolean hasCusShipRequire;

    /**
     * 现场包装标志
     */
    @TableField
    private Boolean onSitePackFlag;

    /**
     * 预拣货类型
     */
    @TableField
    private String prepPickingSlipType;

    /**
     * 锁定前
     */
    @TableField
    private String lockedBefore;
}
