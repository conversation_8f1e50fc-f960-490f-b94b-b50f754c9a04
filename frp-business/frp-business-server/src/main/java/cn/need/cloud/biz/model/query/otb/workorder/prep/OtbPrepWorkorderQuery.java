package cn.need.cloud.biz.model.query.otb.workorder.prep;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;


/**
 * OTB预提工单 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTB预提工单 query对象")
public class OtbPrepWorkorderQuery extends SuperQuery {

    /**
     * 唯一标识码
     */
    @Schema(description = "主键id")
    private List<Long> idList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private List<String> refNumList;

    /**
     * Prep拣货单id
     */
    @Schema(description = "Prep拣货单id")
    private Long otbPrepPickingSlipId;

    /**
     * Prep工单产品类型
     */
    @Schema(description = "Prep工单产品类型")
    private String prepWorkorderProductType;

    /**
     * Prep工单产品类型
     */
    @Schema(description = "Prep工单产品类型集合")
    @Condition(value = Keyword.IN, fields = {"prepWorkorderProductType"})
    private List<String> prepWorkorderProductTypeList;

    /**
     * 预工单类型
     */
    @Schema(description = "预工单类型")
    private String otbPrepWorkorderStatus;

    /**
     * 预工单类型
     */
    @Schema(description = "预工单类型集合")
    @Condition(value = Keyword.IN, fields = {"otbPrepWorkorderStatus"})
    private List<String> otbPrepWorkorderStatusList;


    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 产品id集合
     */
    @Schema(description = "产品id集合")
    private Long productIdList;

    @Schema(description = "流程类型")
    private String processType;


    //下面都是 Workorder 里面的过滤条件，暂时先放这里，后面再优化 参考 autoQuery写法

    /**
     * request快照requestRefNum
     */
    @Schema(description = "request快照requestRefNum集合")
    private List<String> requestSnapshotRequestRefNumList;

    /**
     * 请求快照 发货窗口结束时间开始
     */
    @Schema(description = "请求快照 发货窗口结束时间开始")
    @Condition(Keyword.GE)
    private LocalDateTime requestSnapshotShipWindowEndStart;
    /**
     * 请求快照 发货窗口结束时间结束
     */
    @Schema(description = "请求快照 发货窗口结束时间结束")
    @Condition(Keyword.LE)
    private LocalDateTime requestSnapshotShipWindowEndEnd;

    /**
     * 请求快照 发货窗口开始时间开始
     */
    @Schema(description = "请求快照 发货窗口开始时间开始")
    @Condition(Keyword.GE)
    private LocalDateTime requestSnapshotShipWindowStartStart;
    /**
     * 请求快照 发货窗口开始时间结束
     */
    @Schema(description = "请求快照 发货窗口开始时间结束")
    @Condition(Keyword.LE)
    private LocalDateTime requestSnapshotShipWindowStartEnd;
}