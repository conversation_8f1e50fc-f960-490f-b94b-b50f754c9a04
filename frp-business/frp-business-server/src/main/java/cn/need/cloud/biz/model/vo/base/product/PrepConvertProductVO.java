package cn.need.cloud.biz.model.vo.base.product;

import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/***
 * PrepConvertProductVO.java
 *
 * <AUTHOR>
 * @since 2024-12-02
 */
@Data
@Schema(description = "Prep Convert Description VO对象")
public class PrepConvertProductVO implements Serializable {

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;


    @Schema(description = "产品")
    private BaseProductVO baseProductVO;

    /**
     * Group产品
     */
    @Schema(description = "Group产品")
    private List<PrepGroupVO> groupList;
}
