package cn.need.cloud.biz.model.param.otc.update.shippallet;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * OTC运输托盘 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC运输托盘 vo对象")
public class OtcShipPalletUpdateParam implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;


    /**
     * 纸箱数量
     */
    @Schema(description = "纸箱数量")
    private Integer cartonCount;

    /**
     * 原始托盘标签数据
     */
    @Schema(description = "原始托盘标签数据")
    private String labelRawData;

    /**
     * 原始托盘标签类型
     */
    @Schema(description = "原始托盘标签类型")
    private String rawDataType;

    /**
     * 托盘标签物流追踪码
     */
    @Schema(description = "托盘标签物流追踪码")
    private String trackingNum;

    /**
     * 托盘标签原始数据类型文件id
     */
    @Schema(description = "托盘标签原始数据类型文件id")
    private String fileIdRawDataType;

    /**
     * 托盘标签纸张类型
     */
    @Schema(description = "托盘标签纸张类型")
    private String paperType;

}