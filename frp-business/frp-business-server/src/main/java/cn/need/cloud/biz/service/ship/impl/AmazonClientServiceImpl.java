package cn.need.cloud.biz.service.ship.impl;

import cn.need.cloud.biz.service.ship.AmazonClientService;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.ship.client.api.AmazonClient;
import cn.need.cloud.ship.client.dto.amazon.AmazonShipPalletReqDTO;
import cn.need.cloud.ship.client.dto.amazon.AmazonShipPalletRespDTO;
import cn.need.framework.common.core.exception.unchecked.FeignClientException;
import cn.need.framework.common.support.api.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class AmazonClientServiceImpl implements AmazonClientService {

    @Resource
    private AmazonClient amazonClient;

    @Override
    public AmazonShipPalletRespDTO getShipPallet(AmazonShipPalletReqDTO dto) {
        return getAmazonShipPalletRespDTO(dto);
    }

    private AmazonShipPalletRespDTO getAmazonShipPalletRespDTO(AmazonShipPalletReqDTO dto) {
        log.info("=============>>CommonClientServiceImpl.getShipPallet,入参:{}", JsonUtil.toJson(dto));
        Result<AmazonShipPalletRespDTO> result = amazonClient.getShipPallet(dto);
        log.info("=============>>CommonClientServiceImpl.getShipPallet,出参:{}", result);
        if (result.getCode() == 200) {
            return result.getData();
        }
        throw new FeignClientException(result.getCode(), result.getMessage());
    }
}
