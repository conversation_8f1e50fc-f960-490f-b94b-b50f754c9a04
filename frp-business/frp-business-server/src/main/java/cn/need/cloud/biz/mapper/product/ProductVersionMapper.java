package cn.need.cloud.biz.mapper.product;


import cn.need.cloud.biz.model.entity.product.ProductVersion;
import cn.need.cloud.biz.model.query.product.ProductVersionQuery;
import cn.need.cloud.biz.model.vo.product.ProductVersionVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 产品版本详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface ProductVersionMapper extends SuperMapper<ProductVersion> {

    /**
     * 根据条件获取产品版本详情列表
     *
     * @param query 查询条件
     * @return 产品版本详情集合
     */
    default List<ProductVersionVO> listByQuery(ProductVersionQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取产品版本详情分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 产品版本详情集合
     */
    List<ProductVersionVO> listByQuery(@Param("qo") ProductVersionQuery query, @Param("page") Page<?> page);

    /**
     * 根据产品id集合获取最新版本
     *
     * @param productIdList 产品id集合
     * @return 产品版本详情集合
     */
    List<ProductVersionVO> gretLatestProductVersionByProductIdList(@Param("productIdList") List<Long> productIdList);
}