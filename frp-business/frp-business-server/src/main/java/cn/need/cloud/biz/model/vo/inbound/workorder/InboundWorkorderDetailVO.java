package cn.need.cloud.biz.model.vo.inbound.workorder;

import cn.need.cloud.biz.model.vo.base.BaseFullProductVersionVO;
import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.warehouse.PalletTemplateVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 入库工单详情 FinishQty可能和Qty不一致，这个是有仓库人员判定(外界发的货物可能不一致) 仓库内部的操作是一致的，外界可以不一致 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "入库工单详情 FinishQty可能和Qty不一致，这个是有仓库人员判定(外界发的货物可能不一致) 仓库内部的操作是一致的，外界可以不一致 vo对象")
public class InboundWorkorderDetailVO extends BaseSuperVO {


    /**
     * 入库请求详情
     */
    @Schema(description = "入库请求详情")
    private Long inboundRequestDetailId;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 入库工单id
     */
    @Schema(description = "入库工单id")
    private Long inboundWorkorderId;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 差异数量
     */
    @Schema(description = "差异数量")
    private Integer diffQty;

    /**
     * finishQty
     */
    @Schema(description = "finishQty")
    private Integer finishQty;

    /**
     * requestDetailSnapshotQty
     */
    @Schema(description = "requestDetailSnapshotQty")
    private Integer requestDetailSnapshotQty;

    /**
     * 请求详情序号
     */
    @Schema(description = "请求详情序号")
    private Integer requestDetailSnapshotLineNum;

    /**
     * 请求详情备注
     */
    @Schema(description = "请求详情备注")
    private String requestDetailSnapshotNote;

    /**
     * 请求详情产品id
     */
    @Schema(description = "请求详情产品id")
    private Long requestDetailSnapshotProductId;

    /**
     * 需要卸货数量
     */
    @Schema(description = "需要卸货数量")
    private Integer needReceiveQty;

    /**
     * 总卸货数量
     */
    @Schema(description = "总卸货数量")
    private Integer totalUnloadQty;

    /**
     * 总上架数量
     */
    @Schema(description = "总上架数量")
    private Integer totalPutAwayQty;

    /**
     * 是否重新测量过 默认为false,没有重新测量过 如果发现Product和实际不符，需要走特殊流程更改为对的
     */
    @Schema(description = "是否重新测量过 默认为false,没有重新测量过 如果发现Product和实际不符，需要走特殊流程更改为对的")
    private Boolean remeasureFlag;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    /**
     * 请求详情请求参考编码
     */
    @Schema(description = "请求详情请求参考编码")
    private String requestDetailSnapshotDetailRequestRefNum;

    /**
     * 请求详情类型
     */
    @Schema(description = "请求详情类型")
    private String requestDetailSnapshotDetailType;

    /**
     * 打托模板
     */
    @Schema(description = "打托模板")
    private PalletTemplateVO palletTemplate;


    /**
     * 产品信息
     */
    @Schema(description = "产品信息")
    private BaseFullProductVersionVO baseFullProductVersionVO;

}