package cn.need.cloud.biz.model.vo.otc.pickingslip;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * OTC预提货单 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTC预提货单构建拣货单 vo对象")
public class OtcPrepPickingSlipBuildPickingSlipVO extends BaseSuperVO {

    @Serial
    private static final long serialVersionUID = 1838740794578107860L;
    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String detailProductType;

    /**
     * 是否有特定运输要求
     */
    @Schema(description = "是否有特定运输要求")
    private Boolean hasCusShipRequire;

    /**
     * 现场包装标志
     */
    @Schema(description = "现场包装标志")
    private Boolean onSitePackFlag;

    /**
     * 订单类型
     */
    @Schema(description = "订单类型")
    private String orderType;

    /**
     * 拣货到那里
     */
    @Schema(description = "拣货到那里")
    private String pickToStation;

    /**
     * 拣货状态
     */
    @Schema(description = "拣货状态")
    private String pickingSlipStatus;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

}