package cn.need.cloud.biz.model.param.otb.update.request.prep;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 预请求 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "预请求 vo对象")
public class PrepRequestUpdateParam implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 请求状态
     */
    @Schema(description = "请求状态")
    private String prepRequestStatus;


    /**
     * 交易合作伙伴ID
     */
    @Schema(description = "交易合作伙伴ID")
    private Long transactionPartnerId;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码")
    private String requestRefNum;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

}