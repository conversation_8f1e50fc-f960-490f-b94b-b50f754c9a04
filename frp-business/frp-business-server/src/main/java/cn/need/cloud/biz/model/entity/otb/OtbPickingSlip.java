package cn.need.cloud.biz.model.entity.otb;

import cn.need.cloud.biz.client.constant.enums.base.ShipTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otb.PaperTypeEnum;
import cn.need.cloud.biz.model.entity.base.pickingslip.PickingSlipModel;
import cn.need.cloud.dfs.client.constant.enums.FileDataTypeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;


/**
 * <p>
 * otb拣货单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otb_picking_slip")
@Accessors(chain = true)
public class OtbPickingSlip extends PickingSlipModel {

    @Serial
    private static final long serialVersionUID = -5182188579884706798L;

    /**
     * otb 拣货单状态
     */
    @TableField("otb_picking_slip_status")
    private String otbPickingSlipStatus;

    /**
     * otb工单id
     */
    @TableField("otb_workorder_id")
    private Long otbWorkorderId;


    @TableField("otb_picking_slip_type")
    private String otbPickSlipType;

    /**
     * 产品类型
     */
    @TableField("detail_product_type")
    private String detailProductType;


    /**
     * 发货类型
     */
    @TableField("ship_type")
    private ShipTypeEnum shipType;


    /**
     * RI文件数据
     */
    @TableField("routing_instruction_file_file_data")
    private String routingInstructionFileFileData;

    /**
     * RI文件扩展名
     */
    @TableField("routing_instruction_file_file_extension")
    private String routingInstructionFileFileExtension;

    /**
     * RI文件类型
     */
    @TableField("routing_instruction_file_file_type")
    private FileDataTypeEnum routingInstructionFileFileType;

    /**
     * RI文件纸张类型
     */
    @TableField("routing_instruction_file_paper_type")
    private PaperTypeEnum routingInstructionFilePaperType;

}
