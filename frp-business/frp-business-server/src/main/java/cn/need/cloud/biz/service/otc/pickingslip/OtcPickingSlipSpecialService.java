package cn.need.cloud.biz.service.otc.pickingslip;

import cn.need.cloud.biz.model.bo.base.WorkorderProcessBO;
import cn.need.cloud.biz.model.bo.otc.OtcPackageRollbackSingleWorkorderBO;
import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.bo.otc.workorder.OtcWorkorderSplitBO;
import cn.need.cloud.biz.model.param.base.update.PickingSlipCancelUpdateParam;
import cn.need.cloud.biz.model.param.otc.update.pickingslip.OtcPickingSlipUnpickCreateParam;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestCancelContext;
import cn.need.cloud.biz.model.vo.base.UnpickVO;

import java.util.List;

/**
 * <p>
 * OTC拣货单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtcPickingSlipSpecialService {

    /**
     * 触发异常流程
     *
     * @param process 流程对象
     */
    void processTriggering(WorkorderProcessBO process);

    /**
     * unpick
     *
     * @param query unpick条件
     */
    void unpick(OtcPickingSlipUnpickCreateParam query);

    /**
     * unpick 拣货单信息
     *
     * @param workorderId 工单信息
     * @return /
     */
    UnpickVO unpickByWorkorderId(Long workorderId);

    /**
     * Rollback
     *
     * @param param 上架参数
     */
    void rollback(OtcPutawaySlipPutAwayBO param);

    /**
     * Rollback ReadyToShipQty
     *
     * @param rollbackList rollback参数
     */
    void rollbackByPackage(List<OtcPackageRollbackSingleWorkorderBO> rollbackList);

    /**
     * 拣货单Rollback
     *
     * @param param Rollback参数
     * @return /
     */
    boolean batchCancel(PickingSlipCancelUpdateParam param);

    /**
     * 拆单
     *
     * @param splitHolders 拆单参数
     */
    void split(List<OtcWorkorderSplitBO> splitHolders);


    void startCancel(OtcRequestCancelContext context);
}