package cn.need.cloud.biz.model.query.otb.pickingslip.prep;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * otb预拣货单 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "otb预拣货单 query对象")
public class OtbPrepPickingSlipQuery extends SuperQuery {

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private List<String> refNumList;

    /**
     * otb预拣货单状态
     */
    @Schema(description = "otb预拣货单状态")
    private String otbPrepPickingSlipStatus;

    /**
     * otb预拣货单状态
     */
    @Schema(description = "otb预拣货单状态集合")
    @Condition(value = Keyword.IN, fields = {"otbPrepPickingSlipStatus"})
    private List<String> otbPrepPickingSlipStatusList;

    /**
     * otb预拣货单类型
     */
    @Schema(description = "otb预拣货单类型")
    private String otbPrepPickingSlipType;

    /**
     * otb预拣货单类型
     */
    @Schema(description = "otb预拣货单类型集合")
    @Condition(value = Keyword.IN, fields = {"otbPrepPickingSlipType"})
    private List<String> otbPrepPickingSlipTypeList;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态集合")
    @Condition(value = Keyword.IN, fields = {"printStatus"})
    private List<String> printStatusList;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 产品id
     */
    @Schema(description = "产品id集合")
    private List<Long> productIdList;

    /**
     * otb预拣货单类型
     */
    @Schema(description = "otb预拣货单产品类型")
    private String prepPickingSlipProductType;

    /**
     * otb预拣货单类型
     */
    @Schema(description = "otb预拣货单产品类型集合")
    @Condition(value = Keyword.IN, fields = {"prepPickingSlipProductType"})
    private List<String> prepPickingSlipProductTypeList;

    @Schema(description = "流程类型")
    private String processType;
}