package cn.need.cloud.biz.model.vo.base.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/***
 * OtcPrepGroupVO.java
 *
 * <AUTHOR>
 * @since 2024-12-02
 */
@Data
@Schema(description = "Prep拣货单 Prep MultiBox Group产品 VO对象")
public class PrepMultiBoxGroupVO implements Serializable {

    /**
     * UPC码
     */
    @Schema(description = "UPC码")
    private String upc;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 多箱产品版本号
     */
    @Schema(description = "多箱产品版本号")
    private Integer multiboxVersionInt;

    /**
     * 详情
     */
    @Schema(description = "详情")
    private List<PrepMultiBoxDetailGroupVO> detailList;

}
