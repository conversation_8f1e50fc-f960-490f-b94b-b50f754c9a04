package cn.need.cloud.biz.model.vo.base;

import cn.need.cloud.biz.model.vo.base.pickingslip.PickingSlipUnpickDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkorderConfirmDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * UnpickVO
 *
 * <AUTHOR>
 * @since 2025-04-03
 */
@Schema(description = "UnpickVO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UnpickVO implements Serializable {

    @Schema(description = "Unpick集合")
    private List<PickingSlipUnpickDetailVO> unpickList;

    @Schema(description = "工单详情集合")
    private List<WorkorderConfirmDetailVO> workorderDetailList;
}
