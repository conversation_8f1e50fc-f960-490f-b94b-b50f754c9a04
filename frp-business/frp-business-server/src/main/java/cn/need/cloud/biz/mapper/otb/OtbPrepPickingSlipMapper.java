package cn.need.cloud.biz.mapper.otb;

import cn.need.cloud.biz.model.entity.otb.OtbPrepPickingSlip;
import cn.need.cloud.biz.model.query.otb.pickingslip.prep.OtbPrepPickingSlipQuery;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkorderQuery;
import cn.need.cloud.biz.model.vo.page.OtbPrepPickingSlipPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * otb预拣货单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtbPrepPickingSlipMapper extends SuperMapper<OtbPrepPickingSlip> {

    /**
     * 根据条件获取otb预拣货单列表
     *
     * @param query 查询条件
     * @return otb预拣货单集合
     */
    default List<OtbPrepPickingSlipPageVO> listByQuery(OtbPrepPickingSlipQuery query) {
        return listByQuery(query, null, null);
    }

    /**
     * 根据条件获取otb预拣货单分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return otb预拣货单集合
     */
    List<OtbPrepPickingSlipPageVO> listByQuery(@Param("qo") OtbPrepPickingSlipQuery query,
                                               @Param("wk") OtbWorkorderQuery otbPrepWorkorderQuery,
                                               @Param("page") Page<?> page);
}