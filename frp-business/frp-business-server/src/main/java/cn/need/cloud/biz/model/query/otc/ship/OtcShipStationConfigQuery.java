package cn.need.cloud.biz.model.query.otc.ship;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 快递公司配置 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "快递公司配置 query对象")
public class OtcShipStationConfigQuery extends SuperQuery {

    /**
     * 运输方式
     */
    @Schema(description = "运输方式")
    private String shipMethod;

    /**
     * 运输方式
     */
    @Schema(description = "运输方式集合")
    @Condition(value = Keyword.IN, fields = {"shipMethod"})
    private List<String> shipMethodList;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司集合")
    @Condition(value = Keyword.IN, fields = {"shipCarrier"})
    private List<String> shipCarrierList;

    /**
     * shipMethodCategory
     */
    @Schema(description = "shipMethodCategory")
    private String shipMethodCategory;

    /**
     * shipStation
     */
    @Schema(description = "shipStation")
    private String shipStation;

    /**
     * shipStation
     */
    @Schema(description = "shipStation集合")
    @Condition(value = Keyword.IN, fields = {"shipStation"})
    private List<String> shipStationList;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;


}