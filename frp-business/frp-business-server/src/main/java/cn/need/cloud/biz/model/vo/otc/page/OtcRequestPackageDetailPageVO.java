package cn.need.cloud.biz.model.vo.otc.page;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * OTC请求包裹详情 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC请求包裹详情 vo对象")
public class OtcRequestPackageDetailPageVO implements Serializable {


    @Serial
    private static final long serialVersionUID = 8659044465581355294L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * OTC请求ID
     */
    @Schema(description = "OTC请求ID")
    private Long otcRequestId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    private String hazmatVersionRefNum;

    /**
     * OTC请求包裹ID
     */
    @Schema(description = "OTC请求包裹ID")
    private Long otcRequestPackageId;

}