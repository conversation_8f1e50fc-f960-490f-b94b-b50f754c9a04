package cn.need.cloud.biz.service.otb.pickingslip;

import cn.need.cloud.biz.model.entity.base.pickingslip.BasePickingSlipDetailModel;
import cn.need.cloud.biz.model.entity.otb.OtbPrepPickingSlip;
import cn.need.cloud.biz.model.entity.otb.OtbPrepPickingSlipDetail;
import cn.need.cloud.biz.model.query.otb.pickingslip.prep.OtbPrepPickingSlipProductPickQuery;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPrepPickingSlipDetailPickVO;
import cn.need.framework.common.mybatis.base.SuperService;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * otb预拣货单详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbPrepPickingSlipDetailService extends SuperService<OtbPrepPickingSlipDetail> {

    /**
     * 根据otb预拣货单id获取otb预拣货单详情集合
     *
     * @param otbPrepPickingSlipId otb预拣货单id
     * @return otb预拣货单详情集合
     */
    List<OtbPrepPickingSlipDetail> listByOtbPrepPickingSlipId(Long otbPrepPickingSlipId);

    /**
     * 拣货
     *
     * @param pickList        拣货信息列表
     * @param prepPickingSlip 拣货单
     * @return /
     */
    List<OtbPrepPickingSlipDetailPickVO> pick(List<OtbPrepPickingSlipProductPickQuery> pickList, OtbPrepPickingSlip prepPickingSlip);

    /**
     * 校验是否全部拣货
     *
     * @param id                          拣货单id
     * @param prepPickingSlipDetailIdList 忽略详情id
     * @return /
     */
    boolean allPickedIgnoreDetailIdList(Long id, List<Long> prepPickingSlipDetailIdList);

    /**
     * 拣货更新
     *
     * @param pickDetails 拣货详情
     * @return /
     */
    boolean pickUpdate(List<OtbPrepPickingSlipDetailPickVO> pickDetails);

    /**
     * 根据otb预拣货单id获取otb预拣货单详情集合
     *
     * @param otbPrepPickingSlipId 拣货单id
     * @return /
     */
    Map<Long, List<OtbPrepPickingSlipDetail>> groupByOtbPrepPickingSlipId(Long otbPrepPickingSlipId);

    /**
     * 根据Prep拣货单id获取OTC预提货单详情集合
     *
     * @param pickingSlipIdList Prep拣货单
     * @return /
     */
    List<OtbPrepPickingSlipDetail> listByOtbPrepPickingSlipIds(List<Long> pickingSlipIdList);

    /**
     * 根据Prep拣货单id获取OTC预提货单详情集合
     *
     * @param pickingSlipIdList Prep拣货单
     * @return /
     */
    default Map<Long, List<OtbPrepPickingSlipDetail>> groupByOtbPrepPickingSlipIds(List<Long> pickingSlipIdList) {
        return this.listByOtbPrepPickingSlipIds(pickingSlipIdList)
                .stream()
                .sorted(Comparator.comparing(BasePickingSlipDetailModel::getLineNum))
                .collect(Collectors.groupingBy(OtbPrepPickingSlipDetail::getOtbPrepPickingSlipId));
    }
}