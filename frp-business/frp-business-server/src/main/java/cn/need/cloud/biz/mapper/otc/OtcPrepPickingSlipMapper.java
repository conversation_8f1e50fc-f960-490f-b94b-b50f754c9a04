package cn.need.cloud.biz.mapper.otc;

import cn.need.cloud.biz.model.bo.common.DropColumnInfoBO;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPickingSlip;
import cn.need.cloud.biz.model.query.otc.pickingslip.prep.OtcPrepPickingSlipQuery;
import cn.need.cloud.biz.model.query.otc.workorder.OtcWorkorderQuery;
import cn.need.cloud.biz.model.vo.page.OtcPrepPickingSlipPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * OTC预提货单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtcPrepPickingSlipMapper extends SuperMapper<OtcPrepPickingSlip> {

    /**
     * 根据条件获取OTC预提货单分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return OTC预提货单集合
     */
    List<OtcPrepPickingSlipPageVO> listByQuery(@Param("qo") OtcPrepPickingSlipQuery query,
                                               @Param("wk") OtcWorkorderQuery workorderQuery,
                                               @Param("page") Page<?> page);

    /**
     * OTC预提货单下拉列表
     *
     * @param columnNameList 查询字段名
     * @param qo             查询条件
     * @return OTC预提货单下拉列表
     */
    List<Map<String, Object>> dropProList(@Param("columnList") List<DropColumnInfoBO> columnNameList,
                                          @Param("qo") OtcPrepPickingSlipQuery qo);
}