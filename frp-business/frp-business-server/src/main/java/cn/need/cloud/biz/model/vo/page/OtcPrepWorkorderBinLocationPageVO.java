package cn.need.cloud.biz.model.vo.page;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.base.RelatedProductVO;
import cn.need.cloud.biz.model.vo.base.aware.BaseBinLocationAware;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTC预提工单仓储位置 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTC预提工单仓储位置 vo对象")
public class OtcPrepWorkorderBinLocationPageVO extends BaseSuperVO implements BaseBinLocationAware {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * 预拣货id
     */
    @Schema(description = "预拣货id")
    private Long otcPrepPickingSlipId;

    /**
     * 预拣货
     */
    @Schema(description = "预拣货")
    private RefNumVO otcPrepPickingSlip;

    /**
     * 工单id
     */
    @Schema(description = "工单id")
    private Long otcWorkorderId;

    /**
     * 工单
     */
    @Schema(description = "工单")
    private RefNumVO otcWorkOrder;

    /**
     * 工单详情id
     */
    @Schema(description = "工单详情id")
    private Long otcWorkorderDetailId;

    /**
     * 工单详情
     */
    @Schema(description = "工单详情")
    private RelatedProductVO otcWorkOrderDetail;

    /**
     * 预工单id
     */
    @Schema(description = "预工单id")
    private Long otcPrepWorkorderId;

    /**
     * Prep工单
     */
    @Schema(description = "Prep工单")
    private RefNumVO otcPrepWorkOrder;

    /**
     * 预工单详情id
     */
    @Schema(description = "预工单详情id")
    private Long otcPrepWorkorderDetailId;

    /**
     * Prep工单详情
     */
    @Schema(description = "Prep工单详情")
    private RelatedProductVO otcPrepWorkOrderDetail;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;


}