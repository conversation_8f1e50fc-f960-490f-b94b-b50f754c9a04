package cn.need.cloud.biz.model.vo.base.product;


import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/***
 * 组件
 *
 * <AUTHOR>
 * @since 2024-12-02
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "产品Group组件 VO对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PrepComponentGroupVO extends PrepComponentVO {

    /**
     * Group产品
     */
    @Schema(description = "Group产品")
    private List<PrepGroupVO> groupList;
}
