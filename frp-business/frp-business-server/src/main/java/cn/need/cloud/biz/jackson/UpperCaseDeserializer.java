package cn.need.cloud.biz.jackson;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;

import java.io.IOException;

/**
 * UpperCaseDeserializer
 *
 * <AUTHOR>
 * @since 2023-01-31
 */
public class UpperCaseDeserializer extends JsonDeserializer<String> implements ContextualDeserializer {

    private final UpperCase annotation;

    public UpperCaseDeserializer() {
        this.annotation = null;
    }

    public UpperCaseDeserializer(UpperCase annotation) {
        this.annotation = annotation;
    }

    @Override
    public String deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JacksonException {
        String value = jsonParser.getValueAsString();
        return (value == null || annotation == null)
                ? value
                : annotation.value() ? value.toUpperCase() : value.toLowerCase();
    }

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext deserializationContext, BeanProperty beanProperty) throws JsonMappingException {
        if (beanProperty != null) {
            UpperCase annotation = beanProperty.getAnnotation(UpperCase.class);
            if (annotation != null) {
                return new UpperCaseDeserializer(annotation);
            }
        }
        return this;
    }
}
