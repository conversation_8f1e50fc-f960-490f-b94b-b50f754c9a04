package cn.need.cloud.biz.model.entity.base;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/***
 * 带RefNum表的模型实体
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PackageModel extends RefNumModel {
    /**
     * 收件人名称
     */
    @TableField("ship_to_address_name")
    private String shipToAddressName;

    /**
     * 收件公司
     */
    @TableField("ship_to_address_company")
    private String shipToAddressCompany;

    /**
     * 收件国家
     */
    @TableField("ship_to_address_country")
    private String shipToAddressCountry;

    /**
     * 收件州
     */
    @TableField("ship_to_address_state")
    private String shipToAddressState;

    /**
     * 收件城市
     */
    @TableField("ship_to_address_city")
    private String shipToAddressCity;

    /**
     * 收件邮政编码
     */
    @TableField("ship_to_address_zip_code")
    private String shipToAddressZipCode;

    /**
     * 收件地址1
     */
    @TableField("ship_to_address_addr1")
    private String shipToAddressAddr1;

    /**
     * 收件地址2
     */
    @TableField("ship_to_address_addr2")
    private String shipToAddressAddr2;

    /**
     * 收件地址3
     */
    @TableField("ship_to_address_addr3")
    private String shipToAddressAddr3;

    /**
     * 收件邮件信息
     */
    @TableField("ship_to_address_email")
    private String shipToAddressEmail;

    /**
     * 收件电话信息
     */
    @TableField("ship_to_address_phone")
    private String shipToAddressPhone;

    /**
     * 收件备注
     */
    @TableField("ship_to_address_note")
    private String shipToAddressNote;

    /**
     * 发件人名称
     */
    @TableField("ship_from_address_name")
    private String shipFromAddressName;

    /**
     * 发件公司
     */
    @TableField("ship_from_address_company")
    private String shipFromAddressCompany;

    /**
     * 发件国家
     */
    @TableField("ship_from_address_country")
    private String shipFromAddressCountry;

    /**
     * 发件州
     */
    @TableField("ship_from_address_state")
    private String shipFromAddressState;

    /**
     * 发件城市
     */
    @TableField("ship_from_address_city")
    private String shipFromAddressCity;

    /**
     * 发件邮政编码
     */
    @TableField("ship_from_address_zip_code")
    private String shipFromAddressZipCode;

    /**
     * 发件地址1
     */
    @TableField("ship_from_address_addr1")
    private String shipFromAddressAddr1;

    /**
     * 发件地址2
     */
    @TableField("ship_from_address_addr2")
    private String shipFromAddressAddr2;

    /**
     * 发件地址3
     */
    @TableField("ship_from_address_addr3")
    private String shipFromAddressAddr3;

    /**
     * 发件邮件
     */
    @TableField("ship_from_address_email")
    private String shipFromAddressEmail;

    /**
     * 发件电话
     */
    @TableField("ship_from_address_phone")
    private String shipFromAddressPhone;

    /**
     * 发件备注
     */
    @TableField("ship_from_address_note")
    private String shipFromAddressNote;

    /**
     * c端出货工单id
     */
    @TableField("workorder_id")
    private Long workorderId;

    /**
     * 运输箱子-长
     */
    @TableField("ship_size_length")
    private BigDecimal shipSizeLength;

    /**
     * 运输箱子-宽
     */
    @TableField("ship_size_width")
    private BigDecimal shipSizeWidth;

    /**
     * 运输箱子-高
     */
    @TableField("ship_size_height")
    private BigDecimal shipSizeHeight;

    /**
     * 运输箱子-重量
     */
    @TableField("ship_size_weight")
    private BigDecimal shipSizeWeight;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 包裹状态
     */
    @TableField("package_status")
    private String packageStatus;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 住宅地址发件运输
     */
    @TableField("ship_from_address_is_residential")
    private Boolean shipFromAddressIsResidential;

    /**
     * 运输箱子-长度单位
     */
    @TableField("ship_size_dimension_unit")
    private String shipSizeDimensionUnit;

    /**
     * 运输箱子-重量单位
     */
    @TableField("ship_size_weight_unit")
    private String shipSizeWeightUnit;

    /**
     * 收货地址是否为住宅
     */
    @TableField("ship_to_address_is_residential")
    private Boolean shipToAddressIsResidential;

    /**
     * 行序号
     */
    @TableField("line_num")
    private Integer lineNum;

    /**
     * c端拣货id
     */
    @TableField("picking_slip_id")
    private Long pickingSlipId;

    /**
     * 快递号
     */
    @TableField("tracking_num")
    private String trackingNum;

    /**
     * 运输公司
     */
    @TableField("ship_carrier")
    private String shipCarrier;

    /**
     * 运输方式
     */
    @TableField("ship_method")
    private String shipMethod;

    /**
     * 快递Api配置RefNum
     */
    @TableField("ship_api_profile_ref_num")
    private String shipApiProfileRefNum;

    /**
     * 包裹类型
     */
    @TableField("package_type")
    private String packageType;
}
