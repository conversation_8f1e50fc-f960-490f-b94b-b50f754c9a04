package cn.need.cloud.biz.model.param.otb.create.ri;

import cn.need.cloud.biz.model.vo.otb.pkg.OtbRoutingInstructionPackageLabelVO;
import cn.need.cloud.biz.model.vo.otb.ri.OtbRoutingInstructionPalletLabelVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * otb发货指南 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "otb发货指南 vo对象")
public class OtbRoutingInstructionCreateParam implements Serializable {


    /**
     * 承运人联系人姓名
     */
    @Schema(description = "承运人联系人姓名")
    private String carrierContactName;

    /**
     * 承运人联系人电话
     */
    @Schema(description = "承运人联系人电话")
    private String carrierContactPhone;

    /**
     * 承运人联系人邮箱
     */
    @Schema(description = "承运人联系人邮箱")
    private String carrierContactEmail;

    /**
     * 收货人联系人姓名
     */
    @Schema(description = "收货人联系人姓名")
    private String receivingContactName;

    /**
     * 收货人联系人电话
     */
    @Schema(description = "收货人联系人电话")
    private String receivingContactPhone;

    /**
     * 收货人联系人邮箱
     */
    @Schema(description = "收货人联系人邮箱")
    private String receivingContactEmail;

    /**
     * 发货人联系人姓名
     */
    @Schema(description = "发货人联系人姓名")
    private String shipperContactName;

    /**
     * 发货人联系人电话
     */
    @Schema(description = "发货人联系人电话")
    private String shipperContactPhone;

    /**
     * 发货人联系人邮箱
     */
    @Schema(description = "发货人联系人邮箱")
    private String shipperContactEmail;

    /**
     * 发货地址名称
     */
    @Schema(description = "发货地址名称")
    private String shipFromAddressName;

    /**
     * 发货地址公司
     */
    @Schema(description = "发货地址公司")
    private String shipFromAddressCompany;

    /**
     * 发货地址国家
     */
    @Schema(description = "发货地址国家")
    private String shipFromAddressCountry;

    /**
     * 发货地址州/省
     */
    @Schema(description = "发货地址州/省")
    private String shipFromAddressState;

    /**
     * 发货地址城市
     */
    @Schema(description = "发货地址城市")
    private String shipFromAddressCity;

    /**
     * 发货地址邮编
     */
    @Schema(description = "发货地址邮编")
    private String shipFromAddressZipCode;

    /**
     * 发货地址1
     */
    @Schema(description = "发货地址1")
    private String shipFromAddressAddr1;

    /**
     * 发货地址2
     */
    @Schema(description = "发货地址2")
    private String shipFromAddressAddr2;

    /**
     * 发货地址3
     */
    @Schema(description = "发货地址3")
    private String shipFromAddressAddr3;

    /**
     * 发货地址邮箱
     */
    @Schema(description = "发货地址邮箱")
    private String shipFromAddressEmail;

    /**
     * 发货地址电话
     */
    @Schema(description = "发货地址电话")
    private String shipFromAddressPhone;

    /**
     * 发货地址备注
     */
    @Schema(description = "发货地址备注")
    private String shipFromAddressNote;


    /**
     * 收货地址名称
     */
    @Schema(description = "收货地址名称")
    private String shipToAddressName;

    /**
     * 收货地址公司
     */
    @Schema(description = "收货地址公司")
    private String shipToAddressCompany;

    /**
     * 收货地址国家
     */
    @Schema(description = "收货地址国家")
    private String shipToAddressCountry;

    /**
     * 收货地址州/省
     */
    @Schema(description = "收货地址州/省")
    private String shipToAddressState;

    /**
     * 收货地址城市
     */
    @Schema(description = "收货地址城市")
    private String shipToAddressCity;

    /**
     * 收货地址邮编
     */
    @Schema(description = "收货地址邮编")
    private String shipToAddressZipCode;

    /**
     * 收货地址1
     */
    @Schema(description = "收货地址1")
    private String shipToAddressAddr1;

    /**
     * 收货地址2
     */
    @Schema(description = "收货地址2")
    private String shipToAddressAddr2;

    /**
     * 收货地址3
     */
    @Schema(description = "收货地址3")
    private String shipToAddressAddr3;

    /**
     * 收货地址邮箱
     */
    @Schema(description = "收货地址邮箱")
    private String shipToAddressEmail;

    /**
     * 收货地址电话
     */
    @Schema(description = "收货地址电话")
    private String shipToAddressPhone;

    /**
     * 收货地址备注
     */
    @Schema(description = "收货地址备注")
    private String shipToAddressNote;


    /**
     * BOL文件数据
     */
    @Schema(description = "BOL文件数据")
    private String otbBolFileFileData;

    /**
     * BOL文件扩展名
     */
    @Schema(description = "BOL文件扩展名")
    private String otbBolFileFileExtension;

    /**
     * BOL文件类型
     */
    @Schema(description = "BOL文件类型")
    private String otbBolFileFileType;

    /**
     * 承运人代码
     */
    @Schema(description = "承运人代码")
    private String carrierCode;

    /**
     * 承运人名称
     */
    @Schema(description = "承运人名称")
    private String carrierName;

    /**
     * Carton数量
     */
    @Schema(description = "Carton数量")
    private Integer cartonQty;

    /**
     * 确认的运输类型
     */
    @Schema(description = "确认的运输类型")
    private String confirmedShipmentType;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 托盘数量
     */
    @Schema(description = "托盘数量")
    private Integer palletQty;

    /**
     * otb请求id
     */
    @Schema(description = "otb请求id")
    private Long otbRequestId;

    /**
     * 请求提货日期
     */
    @Schema(description = "请求提货日期")
    private LocalDateTime requestPickupDate;

    /**
     * 计划提货日期
     */
    @Schema(description = "计划提货日期")
    private LocalDateTime schedulePickupDate;

    /**
     * otb装运id
     */
    @Schema(description = "otb装运id")
    private Long otbShipmentId;

    /**
     * 运输参考编号
     */
    @Schema(description = "运输参考编号")
    private String otbShipmentRefNum;

    /**
     * 可堆叠托盘数量
     */
    @Schema(description = "可堆叠托盘数量")
    private Integer stackedPalletQty;


    /**
     * BOL编号
     */
    @Schema(description = "BOL编号")
    private String bolNum;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    /**
     * 运输方式
     */
    @Schema(description = "运输方式")
    private String shipMethod;

    /**
     * 快递Api配置RefNum
     */
    @Schema(description = "快递Api配置RefNum")
    private String shipApiProfileRefNum;


    /**
     * 托盘文件数据
     */
    @Schema(description = "托盘文件数据")
    private String otbPalletFileFileData;

    /**
     * 托盘文件扩展名
     */
    @Schema(description = "托盘文件扩展名")
    private String otbPalletFileFileExtension;

    /**
     * 托盘文件类型
     */
    @Schema(description = "托盘文件类型")
    private String otbPalletFileFileType;

    /**
     * BOL文件纸张类型
     */
    @Schema(description = "BOL文件纸张类型")
    private String otbBolFilePaperType;

    /**
     * 托盘文件纸张类型
     */
    @Schema(description = "托盘文件纸张类型")
    private String otbPalletFilePaperType;

    @Schema(description = "otb路由指令标签列表")
    @Valid
    private List<OtbRoutingInstructionPalletLabelVO> routingInstructionPalletLabelList;

    @Schema(description = "otb路由指令标签列表")
    @Valid
    private List<OtbRoutingInstructionPackageLabelVO> routingInstructionPackageLabelList;

    /**
     * 保险金额
     */
    @Schema(description = "保险金额")
    private BigDecimal insuranceAmountAmount;

    /**
     * 保险金额货币
     */
    @Schema(description = "保险金额货币")
    private String insuranceAmountCurrency;

    /**
     * 签名类型
     */
    @Schema(description = "签名类型")
    private String signatureType;

}