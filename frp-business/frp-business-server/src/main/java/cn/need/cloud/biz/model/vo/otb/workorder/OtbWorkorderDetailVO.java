package cn.need.cloud.biz.model.vo.otb.workorder;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.aware.BaseFullProductAware;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * OTB工单详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTB工单详情 vo对象")
public class OtbWorkorderDetailVO extends BaseSuperVO implements BaseFullProductAware {


    @Serial
    private static final long serialVersionUID = -1232788527798052135L;
    /**
     * 拣货数量
     */
    @Schema(description = "拣货数量")
    private Integer pickedQty;

    /**
     * 待拣货数量
     */
    @Schema(description = "待拣货数量")
    private Integer needPickQty;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    private String hazmatVersionRefNum;

    /**
     * otb工单id
     */
    @Schema(description = "otb工单id")
    private Long otbWorkorderId;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 完成数量
     */
    @Schema(description = "完成数量")
    private Integer finishQty;

    /**
     * 需要拣货单数量
     */
    @Schema(description = "需要拣货单数量")
    private Integer needPickingSlipQty;

    /**
     * 请求快照,渠道产品编码
     */
    @Schema(description = "请求快照,渠道产品编码")
    private String detailSnapshotProductBarcode;

    /**
     * 库存锁定id
     */
    @Schema(description = "库存锁定id")
    private Long inventoryLockedId;

    /**
     * 库存预定id
     */
    @Schema(description = "库存预定id")
    private Long inventoryReserveId;

    /**
     * 预定数量
     */
    @Schema(description = "预定数量")
    private Integer reserveQty;

    /**
     * 打包为Package 数量
     */
    @Schema(description = "打包为Package 数量")
    private Integer packedQty;

    /**
     * 需要打包为Package 数量
     */
    @Schema(description = "需要打包为Package 数量")
    private Integer needPackQty;

    /**
     * 做成Shipment数量
     */
    @Schema(description = "做成Shipment数量")
    private Integer shipmentQty;

    /**
     * 需要分配做成Shipment数量
     */
    @Schema(description = "需要分配做成Shipment数量")
    private Integer needAllocateShipmentQty;

    /**
     * 可以做成Shipment数量
     */
    @Schema(description = "可以做成Shipment数量")
    private Integer canShipmentQty;

    /**
     * 完成预定数量
     */
    @Schema(description = "完成预定数量")
    private Integer finishReserveQty;

    /**
     * 需要预定数量
     */
    @Schema(description = "需要预定数量")
    private Integer needReserveQty;

    @Schema(description = "产品渠道sku")
    private String detailSnapshotProductChannelSku;

    @Schema(description = "出库数量")
    private Integer shippedQty;

    @Schema(description = "能够 生成 拣货单 数量")
    private Integer canBuildPickingSlipQty;

    public Integer canShippedQty() {
        return shipmentQty - shippedQty;
    }

    public void allocateShippedQty(int qty) {
        shippedQty += qty;
        if (shippedQty > shipmentQty) {
            throw new BusinessException("BizError ShippedQty can't be greater than ShipmentQty");
        }
    }

    @Schema(description = "可以拆单的数量")
    public Integer getAvailableSplitQty() {
        return qty > pickedQty ? qty - pickedQty : 0;
    }

}