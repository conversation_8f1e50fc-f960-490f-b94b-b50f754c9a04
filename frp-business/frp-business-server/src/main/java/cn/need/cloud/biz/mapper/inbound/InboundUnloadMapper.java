package cn.need.cloud.biz.mapper.inbound;

import cn.need.cloud.biz.model.entity.inbound.InboundUnload;
import cn.need.cloud.biz.model.query.inbound.InboundUnloadQuery;
import cn.need.cloud.biz.model.vo.page.InboundUnloadPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 入库工单卸货表 根据这个来生成上架单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface InboundUnloadMapper extends SuperMapper<InboundUnload> {

    /**
     * 根据条件获取入库工单卸货表 根据这个来生成上架单列表
     *
     * @param query 查询条件
     * @return 入库工单卸货表 根据这个来生成上架单集合
     */
    default List<InboundUnloadPageVO> listByQuery(InboundUnloadQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取入库工单卸货表 根据这个来生成上架单分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 入库工单卸货表 根据这个来生成上架单集合
     */
    List<InboundUnloadPageVO> listByQuery(@Param("qo") InboundUnloadQuery query, @Param("page") Page<?> page);
}