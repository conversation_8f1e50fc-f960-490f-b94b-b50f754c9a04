package cn.need.cloud.biz.model.vo.otb.putawayslip;

import cn.need.cloud.biz.model.vo.base.putawayslip.PrepPutawaySlipVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTC上架单 VO对象
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTC Prep 上架单 VO对象")
public class OtbPrepPutawaySlipVO extends PrepPutawaySlipVO<OtbPrepPutawaySlipDetailVO> {

}