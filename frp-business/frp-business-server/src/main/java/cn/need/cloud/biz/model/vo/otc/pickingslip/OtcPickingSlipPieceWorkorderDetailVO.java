package cn.need.cloud.biz.model.vo.otc.pickingslip;

import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * OTC工单详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "OTC拣货单包裹信息 工单详情 vo对象")
public class OtcPickingSlipPieceWorkorderDetailVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -5137823827809610794L;
    /**
     * id
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 产品
     */
    @Schema(description = "产品")
    private BaseProductVO baseProductVO;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;


    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    private String hazmatVersionRefNum;
}