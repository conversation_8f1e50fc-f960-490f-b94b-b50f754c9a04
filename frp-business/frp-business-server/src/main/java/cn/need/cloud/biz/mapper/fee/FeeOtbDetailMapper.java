package cn.need.cloud.biz.mapper.fee;

import cn.need.cloud.biz.model.bo.common.DropColumnInfoBO;
import cn.need.cloud.biz.model.entity.fee.FeeOtbDetail;
import cn.need.cloud.biz.model.query.fee.FeeOtbDetailQuery;
import cn.need.cloud.biz.model.vo.fee.page.FeeOtbDetailPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 费用详情otb Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Mapper
public interface FeeOtbDetailMapper extends SuperMapper<FeeOtbDetail> {

    /**
     * 根据条件获取费用详情otb列表
     *
     * @param query 查询条件
     * @return 费用详情otb集合
     */
    default List<FeeOtbDetailPageVO> listByQuery(@Param("qofod") FeeOtbDetailQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取费用详情otb分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 费用详情otb集合
     */
    List<FeeOtbDetailPageVO> listByQuery(
            @Param("qofod") FeeOtbDetailQuery query,
            @Param("page") Page<?> page);

    /**
     * 费用详情otb下拉列表
     *
     * @param columnList 查询字段名
     * @param query      查询条件
     * @return 费用详情otb下拉列表
     */
    List<Map<String, Object>> dropProList(
            @Param("columnList") List<DropColumnInfoBO> columnList,
            @Param("qofod") FeeOtbDetailQuery query
    );
}