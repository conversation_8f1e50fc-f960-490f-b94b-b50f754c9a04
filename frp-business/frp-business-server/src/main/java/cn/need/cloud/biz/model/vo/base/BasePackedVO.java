package cn.need.cloud.biz.model.vo.base;

import cn.need.cloud.biz.util.Allocation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 基础发货对象，拥有相同 拣货数量、发货数量、乐观锁版本号 字段
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "OTC 拣货基础 对象")
public class BasePackedVO implements Allocation {

    /**
     * 对象id
     */
    @Schema(description = "对象id")
    private Long id;

    /**
     * 打包数量
     */
    @Schema(description = "打包数量")
    private Integer packedQty;

    /**
     * 打包前数量
     */
    @Schema(description = "打包前数量")
    private Integer packedBeforeQty;

    /**
     * 拣货数量
     */
    @Schema(description = "拣货数量")
    private Integer pickedQty;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    public int getChangePackedQty() {
        return packedQty - packedBeforeQty;
    }

    @Override
    public int total() {
        return this.pickedQty;
    }

    @Override
    public void allocation(int allocationQty) {
        this.packedQty = allocationQty;
    }

    @Override
    public int allocated() {
        return this.packedQty;
    }
}