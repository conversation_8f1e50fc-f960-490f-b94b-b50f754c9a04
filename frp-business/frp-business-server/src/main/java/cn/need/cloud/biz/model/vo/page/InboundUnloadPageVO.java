package cn.need.cloud.biz.model.vo.page;

import cn.need.cloud.biz.model.vo.base.BaseFullProductVersionVO;
import cn.need.cloud.biz.model.vo.base.aware.BaseFullProductVersionAware;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 入库工单卸货表 根据这个来生成上架单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "入库工单卸货表 根据这个来生成上架单 vo对象")
public class InboundUnloadPageVO implements Serializable, BaseFullProductVersionAware {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 创建人id
     */
    @Schema(description = "创建人id")
    private Long createBy;

    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    private String createByName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 入库工单
     */
    @Schema(description = "入库工单")
    private Long inboundWorkorderId;

    /**
     * 入库工单详情
     */
    @Schema(description = "入库工单详情")
    private Long inboundWorkorderDetailId;

    /**
     * 上架单
     */
    @Schema(description = "上架单")
    private Long putawaySlipId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * palletQty
     */
    @Schema(description = "palletQty")
    private Integer palletQty;

    /**
     * palletPutawayQty
     */
    @Schema(description = "palletPutawayQty")
    private Integer palletPutawayQty;

    /**
     * regularPutawayQty
     */
    @Schema(description = "regularPutawayQty")
    private Integer regularPutawayQty;

    /**
     * 卸货状态
     */
    @Schema(description = "卸货状态")
    private String inboundUnloadStatus;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    /**
     * 入库请求
     */
    @Schema(description = "入库请求")
    private Long requestId;

}