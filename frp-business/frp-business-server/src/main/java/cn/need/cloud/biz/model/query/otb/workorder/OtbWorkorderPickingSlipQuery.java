package cn.need.cloud.biz.model.query.otb.workorder;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Set;

/***
 * 工单拣货单过滤条件
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@Schema(description = "OTB工单 拣货单 query对象")
public class OtbWorkorderPickingSlipQuery implements Serializable {

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private Set<String> refNumList;
}
