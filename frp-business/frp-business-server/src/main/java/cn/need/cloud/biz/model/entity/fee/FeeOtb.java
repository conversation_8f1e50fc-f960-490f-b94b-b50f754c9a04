package cn.need.cloud.biz.model.entity.fee;

import cn.need.cloud.biz.model.entity.base.RefNumModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;


/**
 * <p>
 * 费用otb
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("fee_otb")
public class FeeOtb extends RefNumModel {


    @Serial
    private static final long serialVersionUID = 591248171689409559L;
    /**
     * 货币代码（如USD、CNY）
     */
    @TableField("currency")
    private String currency;

    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 费用原始数据表id
     */
    @TableField("fee_original_data_id")
    private Long feeOriginalDataId;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 唯一标识码
     */
    @TableField("ref_num")
    private String refNum;

    /**
     * 唯一标识码
     */
    @TableField("snapshot_ref_num")
    private String snapshotRefNum;

    /**
     * 请求id
     */
    @TableField("snapshot_request_id")
    private Long snapshotRequestId;

    /**
     * 外部唯一标识码
     */
    @TableField("snapshot_request_ref_num")
    private String snapshotRequestRefNum;

    /**
     * 总费用
     */
    @TableField("total_fee")
    private BigDecimal totalFee;

    /**
     * 交易伙伴id
     */
    @TableField("transaction_partner_id")
    private Long transactionPartnerId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

}
