package cn.need.cloud.biz.model.query.warehouse;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;


/**
 * 托盘信息 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "托盘信息 query对象")
public class PalletEmptyProfileQuery extends SuperQuery {

    /**
     * 名字
     */
    @Schema(description = "名字")
    private String name;

    /**
     * 名字
     */
    @Schema(description = "名字")
    private Set<String> nameList;

    /**
     * 托盘-长
     */
    @Schema(description = "托盘-长")
    private BigDecimal palletSizeLength;

    /**
     * 托盘-宽
     */
    @Schema(description = "托盘-宽")
    private BigDecimal palletSizeWidth;

    /**
     * 托盘-高
     */
    @Schema(description = "托盘-高")
    private BigDecimal palletSizeHeight;

    /**
     * 托盘-重量
     */
    @Schema(description = "托盘-重量")
    private BigDecimal palletSizeWeight;

    /**
     * 托盘-重量单位
     */
    @Schema(description = "托盘-重量单位")
    private String palletSizeWeightUnit;

    /**
     * 托盘-长度单位
     */
    @Schema(description = "托盘-长度单位")
    private String palletSizeDimensionUnit;

    /**
     * 是否有效
     */
    @Schema(description = "是否有效")
    private Boolean activeFlag;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private List<String> refNumList;

    /**
     * 使用标识
     */
    @Schema(description = "使用标识")
    private Boolean inUseFlag;


}