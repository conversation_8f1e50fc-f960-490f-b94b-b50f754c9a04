package cn.need.cloud.biz.mapper.otb;

import cn.need.cloud.biz.model.bo.common.DropColumnInfoBO;
import cn.need.cloud.biz.model.entity.otb.OtbShipment;
import cn.need.cloud.biz.model.query.otb.shipment.OtbShipmentQuery;
import cn.need.cloud.biz.model.vo.page.OtbShipmentPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * OTB装运 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtbShipmentMapper extends SuperMapper<OtbShipment> {

    /**
     * 根据条件获取OTB装运列表
     *
     * @param query 查询条件
     * @return OTB装运集合
     */
    default List<OtbShipmentPageVO> listByQuery(OtbShipmentQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取OTB装运分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return OTB装运集合
     */
    List<OtbShipmentPageVO> listByQuery(@Param("qo") OtbShipmentQuery query, @Param("page") Page<?> page);

    /**
     * OTB发货单下拉列表，带Group
     *
     * @param columnList 查询字段名
     * @param qo         查询条件
     * @return OTC工单下拉列表
     */
    List<Map<String, Object>> dropProList(@Param("columnList") List<DropColumnInfoBO> columnList,
                                          @Param("qo") OtbShipmentQuery qo);
}