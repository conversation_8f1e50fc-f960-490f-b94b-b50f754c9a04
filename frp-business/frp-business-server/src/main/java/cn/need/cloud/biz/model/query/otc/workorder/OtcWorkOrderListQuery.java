package cn.need.cloud.biz.model.query.otc.workorder;

import cn.need.cloud.biz.model.query.base.BaseBinLocationQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * OTC工单 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC工单列表query对象")
public class OtcWorkOrderListQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = -5093024911748542227L;
    /**
     * 工单查询条件
     */
    @Schema(description = "工单查询条件")
    private OtcWorkorderQuery otcWorkorderQuery;

    /**
     * 库位查询条件
     */
    @Schema(description = "库位查询条件")
    private BaseBinLocationQuery binLocationQuery;
}