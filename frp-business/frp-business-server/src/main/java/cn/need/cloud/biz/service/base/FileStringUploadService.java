package cn.need.cloud.biz.service.base;

import cn.need.cloud.biz.model.bo.base.FrpFileModel;

import java.util.List;

/**
 * 文件字符串上传并填充Link业务
 *
 * <AUTHOR>
 */
public interface FileStringUploadService {

    /**
     * 上传FRP文件
     * 该方法用于上传实现了FrpFileModel接口的对象
     *
     * @param uploadObject 实现了FrpFileModel接口的上传对象
     * @param <T> 泛型参数，表示任何实现了FrpFileModel接口的类型
     */
    <T extends FrpFileModel> void uploadFrpFile(T uploadObject);

    /**
     * 批量上传标签
     * 该方法用于批量上传实现UploadStringAble接口的对象此方法泛型化，可以接受任何实现了UploadStringAble接口的类型，
     *
     * @param uploadList 一个泛型为T的列表，包含了多个UploadStringAble对象的集合
     *                   这些对象将被批量上传
     * @param <T>        泛型参数，表示任何实现了UploadStringAble接口的类型
     */
    <T extends UploadStringAble> void uploadLabelBatch(List<T> uploadList);

    /**
     * 单个上传标签
     * 该方法用于单个上传实现UploadStringAble接口的对象此方法泛型化，可以接受任何实现了UploadStringAble接口的类型，
     *
     * @param uploadObject 一个泛型为T的列表，
     *                     对象将被进行单个上传
     * @param <T>          泛型参数，表示任何实现了UploadStringAble接口的类型
     */
    <T extends UploadStringAble> void uploadLabel(T uploadObject);
}
