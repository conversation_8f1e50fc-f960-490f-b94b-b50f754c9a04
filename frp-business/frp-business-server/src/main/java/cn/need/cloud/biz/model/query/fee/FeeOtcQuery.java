package cn.need.cloud.biz.model.query.fee;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.List;


/**
 * 费用otc Query对象
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "费用otc Query对象")
public class FeeOtcQuery extends SuperQuery {

    @Serial
    private static final long serialVersionUID = 1759845619177309752L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    // region currency

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY）")
    private String currency;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY） 集合")
    @Condition(value = Keyword.IN, fields = {"currency"})
    private List<String> currencyList;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY） 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"currency"})
    private List<String> currencyNiList;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY）值类型集合")
    private List<String> currencyValueTypeList;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY） 大于")
    @Condition(value = Keyword.GT, fields = {"currency"})
    private String currencyGt;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY） 大于等于")
    @Condition(value = Keyword.GE, fields = {"currency"})
    private String currencyGe;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY） 小于")
    @Condition(value = Keyword.LT, fields = {"currency"})
    private String currencyLt;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY） 小于等于")
    @Condition(value = Keyword.LE, fields = {"currency"})
    private String currencyLe;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY） 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"currency"})
    private String currencyLike;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY） 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"currency"})
    private String currencyLikeLeft;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY） 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"currency"})
    private String currencyLikeRight;

    // endregion currency

    // region deletedNote

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 集合")
    @Condition(value = Keyword.IN, fields = {"deletedNote"})
    private List<String> deletedNoteList;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"deletedNote"})
    private List<String> deletedNoteNiList;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因值类型集合")
    private List<String> deletedNoteValueTypeList;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 大于")
    @Condition(value = Keyword.GT, fields = {"deletedNote"})
    private String deletedNoteGt;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 大于等于")
    @Condition(value = Keyword.GE, fields = {"deletedNote"})
    private String deletedNoteGe;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 小于")
    @Condition(value = Keyword.LT, fields = {"deletedNote"})
    private String deletedNoteLt;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 小于等于")
    @Condition(value = Keyword.LE, fields = {"deletedNote"})
    private String deletedNoteLe;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"deletedNote"})
    private String deletedNoteLike;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"deletedNote"})
    private String deletedNoteLikeLeft;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"deletedNote"})
    private String deletedNoteLikeRight;

    // endregion deletedNote

    // region feeOriginalDataId

    /**
     * 费用原始数据表id
     */
    @Schema(description = "费用原始数据表id")
    private Long feeOriginalDataId;

    /**
     * 费用原始数据表id
     */
    @Schema(description = "费用原始数据表id 集合")
    @Condition(value = Keyword.IN, fields = {"feeOriginalDataId"})
    private List<Long> feeOriginalDataIdList;

    /**
     * 费用原始数据表id
     */
    @Schema(description = "费用原始数据表id 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"feeOriginalDataId"})
    private List<Long> feeOriginalDataIdNiList;

    /**
     * 费用原始数据表id
     */
    @Schema(description = "费用原始数据表id值类型集合")
    private List<String> feeOriginalDataIdValueTypeList;

    /**
     * 费用原始数据表id
     */
    @Schema(description = "费用原始数据表id 大于")
    @Condition(value = Keyword.GT, fields = {"feeOriginalDataId"})
    private Long feeOriginalDataIdGt;

    /**
     * 费用原始数据表id
     */
    @Schema(description = "费用原始数据表id 大于等于")
    @Condition(value = Keyword.GE, fields = {"feeOriginalDataId"})
    private Long feeOriginalDataIdGe;

    /**
     * 费用原始数据表id
     */
    @Schema(description = "费用原始数据表id 小于")
    @Condition(value = Keyword.LT, fields = {"feeOriginalDataId"})
    private Long feeOriginalDataIdLt;

    /**
     * 费用原始数据表id
     */
    @Schema(description = "费用原始数据表id 小于等于")
    @Condition(value = Keyword.LE, fields = {"feeOriginalDataId"})
    private Long feeOriginalDataIdLe;

    /**
     * 费用原始数据表id
     */
    @Schema(description = "费用原始数据表id 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"feeOriginalDataId"})
    private Long feeOriginalDataIdLike;

    /**
     * 费用原始数据表id
     */
    @Schema(description = "费用原始数据表id 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"feeOriginalDataId"})
    private Long feeOriginalDataIdLikeLeft;

    /**
     * 费用原始数据表id
     */
    @Schema(description = "费用原始数据表id 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"feeOriginalDataId"})
    private Long feeOriginalDataIdLikeRight;

    // endregion feeOriginalDataId

    // region note

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 备注
     */
    @Schema(description = "备注 集合")
    @Condition(value = Keyword.IN, fields = {"note"})
    private List<String> noteList;

    /**
     * 备注
     */
    @Schema(description = "备注 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"note"})
    private List<String> noteNiList;

    /**
     * 备注
     */
    @Schema(description = "备注值类型集合")
    private List<String> noteValueTypeList;

    /**
     * 备注
     */
    @Schema(description = "备注 大于")
    @Condition(value = Keyword.GT, fields = {"note"})
    private String noteGt;

    /**
     * 备注
     */
    @Schema(description = "备注 大于等于")
    @Condition(value = Keyword.GE, fields = {"note"})
    private String noteGe;

    /**
     * 备注
     */
    @Schema(description = "备注 小于")
    @Condition(value = Keyword.LT, fields = {"note"})
    private String noteLt;

    /**
     * 备注
     */
    @Schema(description = "备注 小于等于")
    @Condition(value = Keyword.LE, fields = {"note"})
    private String noteLe;

    /**
     * 备注
     */
    @Schema(description = "备注 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"note"})
    private String noteLike;

    /**
     * 备注
     */
    @Schema(description = "备注 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"note"})
    private String noteLikeLeft;

    /**
     * 备注
     */
    @Schema(description = "备注 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"note"})
    private String noteLikeRight;

    // endregion note

    // region refNum

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private List<String> refNumList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"refNum"})
    private List<String> refNumNiList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码值类型集合")
    private List<String> refNumValueTypeList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 大于")
    @Condition(value = Keyword.GT, fields = {"refNum"})
    private String refNumGt;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 大于等于")
    @Condition(value = Keyword.GE, fields = {"refNum"})
    private String refNumGe;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 小于")
    @Condition(value = Keyword.LT, fields = {"refNum"})
    private String refNumLt;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 小于等于")
    @Condition(value = Keyword.LE, fields = {"refNum"})
    private String refNumLe;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"refNum"})
    private String refNumLike;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"refNum"})
    private String refNumLikeLeft;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"refNum"})
    private String refNumLikeRight;

    // endregion refNum

    // region snapshotRefNum

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String snapshotRefNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 集合")
    @Condition(value = Keyword.IN, fields = {"snapshotRefNum"})
    private List<String> snapshotRefNumList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"snapshotRefNum"})
    private List<String> snapshotRefNumNiList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码值类型集合")
    private List<String> snapshotRefNumValueTypeList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 大于")
    @Condition(value = Keyword.GT, fields = {"snapshotRefNum"})
    private String snapshotRefNumGt;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 大于等于")
    @Condition(value = Keyword.GE, fields = {"snapshotRefNum"})
    private String snapshotRefNumGe;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 小于")
    @Condition(value = Keyword.LT, fields = {"snapshotRefNum"})
    private String snapshotRefNumLt;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 小于等于")
    @Condition(value = Keyword.LE, fields = {"snapshotRefNum"})
    private String snapshotRefNumLe;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"snapshotRefNum"})
    private String snapshotRefNumLike;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"snapshotRefNum"})
    private String snapshotRefNumLikeLeft;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"snapshotRefNum"})
    private String snapshotRefNumLikeRight;

    // endregion snapshotRefNum

    // region snapshotRequestId

    /**
     * 请求id
     */
    @Schema(description = "请求id")
    private Long snapshotRequestId;

    /**
     * 请求id
     */
    @Schema(description = "请求id 集合")
    @Condition(value = Keyword.IN, fields = {"snapshotRequestId"})
    private List<Long> snapshotRequestIdList;

    /**
     * 请求id
     */
    @Schema(description = "请求id 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"snapshotRequestId"})
    private List<Long> snapshotRequestIdNiList;

    /**
     * 请求id
     */
    @Schema(description = "请求id值类型集合")
    private List<String> snapshotRequestIdValueTypeList;

    /**
     * 请求id
     */
    @Schema(description = "请求id 大于")
    @Condition(value = Keyword.GT, fields = {"snapshotRequestId"})
    private Long snapshotRequestIdGt;

    /**
     * 请求id
     */
    @Schema(description = "请求id 大于等于")
    @Condition(value = Keyword.GE, fields = {"snapshotRequestId"})
    private Long snapshotRequestIdGe;

    /**
     * 请求id
     */
    @Schema(description = "请求id 小于")
    @Condition(value = Keyword.LT, fields = {"snapshotRequestId"})
    private Long snapshotRequestIdLt;

    /**
     * 请求id
     */
    @Schema(description = "请求id 小于等于")
    @Condition(value = Keyword.LE, fields = {"snapshotRequestId"})
    private Long snapshotRequestIdLe;

    /**
     * 请求id
     */
    @Schema(description = "请求id 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"snapshotRequestId"})
    private Long snapshotRequestIdLike;

    /**
     * 请求id
     */
    @Schema(description = "请求id 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"snapshotRequestId"})
    private Long snapshotRequestIdLikeLeft;

    /**
     * 请求id
     */
    @Schema(description = "请求id 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"snapshotRequestId"})
    private Long snapshotRequestIdLikeRight;

    // endregion snapshotRequestId

    // region snapshotRequestRefNum

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码")
    private String snapshotRequestRefNum;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码 集合")
    @Condition(value = Keyword.IN, fields = {"snapshotRequestRefNum"})
    private List<String> snapshotRequestRefNumList;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"snapshotRequestRefNum"})
    private List<String> snapshotRequestRefNumNiList;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码值类型集合")
    private List<String> snapshotRequestRefNumValueTypeList;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码 大于")
    @Condition(value = Keyword.GT, fields = {"snapshotRequestRefNum"})
    private String snapshotRequestRefNumGt;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码 大于等于")
    @Condition(value = Keyword.GE, fields = {"snapshotRequestRefNum"})
    private String snapshotRequestRefNumGe;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码 小于")
    @Condition(value = Keyword.LT, fields = {"snapshotRequestRefNum"})
    private String snapshotRequestRefNumLt;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码 小于等于")
    @Condition(value = Keyword.LE, fields = {"snapshotRequestRefNum"})
    private String snapshotRequestRefNumLe;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"snapshotRequestRefNum"})
    private String snapshotRequestRefNumLike;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"snapshotRequestRefNum"})
    private String snapshotRequestRefNumLikeLeft;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"snapshotRequestRefNum"})
    private String snapshotRequestRefNumLikeRight;

    // endregion snapshotRequestRefNum

    // region totalFee

    /**
     * 总费用
     */
    @Schema(description = "总费用")
    private BigDecimal totalFee;

    /**
     * 总费用
     */
    @Schema(description = "总费用 集合")
    @Condition(value = Keyword.IN, fields = {"totalFee"})
    private List<BigDecimal> totalFeeList;

    /**
     * 总费用
     */
    @Schema(description = "总费用 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"totalFee"})
    private List<BigDecimal> totalFeeNiList;

    /**
     * 总费用
     */
    @Schema(description = "总费用值类型集合")
    private List<String> totalFeeValueTypeList;

    /**
     * 总费用
     */
    @Schema(description = "总费用 大于")
    @Condition(value = Keyword.GT, fields = {"totalFee"})
    private BigDecimal totalFeeGt;

    /**
     * 总费用
     */
    @Schema(description = "总费用 大于等于")
    @Condition(value = Keyword.GE, fields = {"totalFee"})
    private BigDecimal totalFeeGe;

    /**
     * 总费用
     */
    @Schema(description = "总费用 小于")
    @Condition(value = Keyword.LT, fields = {"totalFee"})
    private BigDecimal totalFeeLt;

    /**
     * 总费用
     */
    @Schema(description = "总费用 小于等于")
    @Condition(value = Keyword.LE, fields = {"totalFee"})
    private BigDecimal totalFeeLe;

    /**
     * 总费用
     */
    @Schema(description = "总费用 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"totalFee"})
    private BigDecimal totalFeeLike;

    /**
     * 总费用
     */
    @Schema(description = "总费用 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"totalFee"})
    private BigDecimal totalFeeLikeLeft;

    /**
     * 总费用
     */
    @Schema(description = "总费用 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"totalFee"})
    private BigDecimal totalFeeLikeRight;

    // endregion totalFee

    // region transactionPartnerId

    /**
     * 交易伙伴id
     */
    @Schema(description = "交易伙伴id")
    private Long transactionPartnerId;

    /**
     * 交易伙伴id
     */
    @Schema(description = "交易伙伴id 集合")
    @Condition(value = Keyword.IN, fields = {"transactionPartnerId"})
    private List<Long> transactionPartnerIdList;

    /**
     * 交易伙伴id
     */
    @Schema(description = "交易伙伴id 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"transactionPartnerId"})
    private List<Long> transactionPartnerIdNiList;

    /**
     * 交易伙伴id
     */
    @Schema(description = "交易伙伴id值类型集合")
    private List<String> transactionPartnerIdValueTypeList;

    /**
     * 交易伙伴id
     */
    @Schema(description = "交易伙伴id 大于")
    @Condition(value = Keyword.GT, fields = {"transactionPartnerId"})
    private Long transactionPartnerIdGt;

    /**
     * 交易伙伴id
     */
    @Schema(description = "交易伙伴id 大于等于")
    @Condition(value = Keyword.GE, fields = {"transactionPartnerId"})
    private Long transactionPartnerIdGe;

    /**
     * 交易伙伴id
     */
    @Schema(description = "交易伙伴id 小于")
    @Condition(value = Keyword.LT, fields = {"transactionPartnerId"})
    private Long transactionPartnerIdLt;

    /**
     * 交易伙伴id
     */
    @Schema(description = "交易伙伴id 小于等于")
    @Condition(value = Keyword.LE, fields = {"transactionPartnerId"})
    private Long transactionPartnerIdLe;

    /**
     * 交易伙伴id
     */
    @Schema(description = "交易伙伴id 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"transactionPartnerId"})
    private Long transactionPartnerIdLike;

    /**
     * 交易伙伴id
     */
    @Schema(description = "交易伙伴id 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"transactionPartnerId"})
    private Long transactionPartnerIdLikeLeft;

    /**
     * 交易伙伴id
     */
    @Schema(description = "交易伙伴id 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"transactionPartnerId"})
    private Long transactionPartnerIdLikeRight;

    // endregion transactionPartnerId

    // region warehouseId

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 集合")
    @Condition(value = Keyword.IN, fields = {"warehouseId"})
    private List<Long> warehouseIdList;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"warehouseId"})
    private List<Long> warehouseIdNiList;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id值类型集合")
    private List<String> warehouseIdValueTypeList;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 大于")
    @Condition(value = Keyword.GT, fields = {"warehouseId"})
    private Long warehouseIdGt;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 大于等于")
    @Condition(value = Keyword.GE, fields = {"warehouseId"})
    private Long warehouseIdGe;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 小于")
    @Condition(value = Keyword.LT, fields = {"warehouseId"})
    private Long warehouseIdLt;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 小于等于")
    @Condition(value = Keyword.LE, fields = {"warehouseId"})
    private Long warehouseIdLe;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"warehouseId"})
    private Long warehouseIdLike;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"warehouseId"})
    private Long warehouseIdLikeLeft;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"warehouseId"})
    private Long warehouseIdLikeRight;

    // endregion warehouseId


}