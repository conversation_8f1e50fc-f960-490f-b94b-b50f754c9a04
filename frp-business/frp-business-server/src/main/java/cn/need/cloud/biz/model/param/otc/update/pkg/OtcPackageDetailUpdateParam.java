package cn.need.cloud.biz.model.param.otc.update.pkg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;


/**
 * OTC包裹详情 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC包裹详情 vo对象")
public class OtcPackageDetailUpdateParam implements Serializable {


    @Serial
    private static final long serialVersionUID = -5399141680560808167L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * c端出货打包id
     */
    @Schema(description = "c端出货打包id")
    private Long otcPackageId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    @Size(max = 64, message = "hazmatVersionRefNum cannot exceed 64 characters")
    private String hazmatVersionRefNum;

    /**
     * 拣货数量
     */
    @Schema(description = "拣货数量")
    private Integer pickedQty;

}