package cn.need.cloud.biz.mapper.otb;

import cn.need.cloud.biz.model.entity.otb.OtbPackageLabel;
import cn.need.cloud.biz.model.query.otb.pkg.OtbPackageLabelQuery;
import cn.need.cloud.biz.model.vo.page.OtbPackageLabelPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * OTB包裹标签 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtbPackageLabelMapper extends SuperMapper<OtbPackageLabel> {

    /**
     * 根据条件获取OTB包裹标签列表
     *
     * @param query 查询条件
     * @return OTB包裹标签集合
     */
    default List<OtbPackageLabelPageVO> listByQuery(OtbPackageLabelQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取OTB包裹标签分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return OTB包裹标签集合
     */
    List<OtbPackageLabelPageVO> listByQuery(@Param("qo") OtbPackageLabelQuery query, @Param("page") Page<?> page);
}