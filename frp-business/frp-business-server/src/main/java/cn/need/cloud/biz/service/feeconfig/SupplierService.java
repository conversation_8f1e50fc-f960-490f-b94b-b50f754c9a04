package cn.need.cloud.biz.service.feeconfig;


import cn.need.cloud.biz.model.entity.feeconfig.Supplier;
import cn.need.cloud.biz.model.param.feeconfig.create.SupplierCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.SupplierUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.SupplierQuery;
import cn.need.cloud.biz.model.vo.feeconfig.SupplierVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.SupplierPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 供应商信息 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public interface SupplierService extends SuperService<Supplier> {

    /**
     * 根据参数新增供应商信息
     *
     * @param createParam 请求创建参数，包含需要插入的供应商信息的相关信息
     * @return 供应商信息ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(SupplierCreateParam createParam);


    /**
     * 根据参数更新供应商信息
     *
     * @param updateParam 请求创建参数，包含需要更新的供应商信息的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(SupplierUpdateParam updateParam);

    /**
     * 根据查询条件获取供应商信息列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个供应商信息对象的列表(分页)
     */
    List<SupplierPageVO> listByQuery(SupplierQuery query);

    /**
     * 根据查询条件获取供应商信息列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个供应商信息对象的列表(分页)
     */
    PageData<SupplierPageVO> pageByQuery(PageSearch<SupplierQuery> search);

    /**
     * 根据ID获取供应商信息
     *
     * @param id 供应商信息ID
     * @return 返回供应商信息VO对象
     */
    SupplierVO detailById(Long id);


    /**
     * 根据交易伙伴ID获取供应商信息
     *
     * @param transactionPartnerId 交易伙伴ID
     * @return 返回对应的供应商实体对象
     */
    SupplierVO getByTransactionPartnerId(Long transactionPartnerId);
}