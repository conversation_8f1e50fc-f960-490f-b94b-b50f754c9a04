package cn.need.cloud.biz.service.inbound.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.RedisConstant;
import cn.need.cloud.biz.client.constant.enums.inbound.InboundRequestStatusEnum;
import cn.need.cloud.biz.model.bo.inbound.InboundRequestAuditContextBO;
import cn.need.cloud.biz.model.entity.inbound.InboundRequest;
import cn.need.cloud.biz.model.query.inbound.InboundRequestQuery;
import cn.need.cloud.biz.model.vo.inbound.request.InboundRequestAuditVO;
import cn.need.cloud.biz.model.vo.page.InboundRequestPageVO;
import cn.need.cloud.biz.service.inbound.InboundRequestIgnoreWarehouseService;
import cn.need.cloud.biz.service.inbound.InboundRequestService;
import cn.need.cloud.biz.service.inbound.InboundWorkorderService;
import cn.need.cloud.biz.util.TimeUtils;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.redis.RedissonKit;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 入库请求单忽略仓库服务实现类
 * </p>
 * <p>
 * 该服务主要处理不需要指定仓库的入库请求，具体功能包括：
 * 1. 审核入库请求
 * 2. 分页查询入库请求
 * </p>
 * <p>
 * 该服务与普通入库请求服务的区别在于，忽略了仓库限制，可以处理跨仓库的入库请求。
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
@Service
public class InboundRequestIgnoreWarehouseServiceImpl implements InboundRequestIgnoreWarehouseService {

    /**
     * 入库工单服务，用于生成工单
     */
    @Resource
    private InboundWorkorderService inboundWorkorderService;

    /**
     * 入库请求服务，用于处理基础的入库请求操作
     */
    @Resource
    private InboundRequestService inboundRequestService;

    /**
     * 入库请求审批
     * <p>
     * 该方法用于审批入库请求单，包括同意和拒绝两种操作：
     * 1. 同意：生成入库工单，并更新请求状态为已批准
     * 2. 拒绝：直接更新请求状态为已拒绝
     * </p>
     * <p>
     * 该方法使用分布式锁确保并发安全，避免重复审批
     * </p>
     *
     * @param param 审批参数，包含审批结果类型、备注等信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(InboundRequestAuditVO param) {
        //加载上下文信息
        InboundRequestAuditContextBO context = new InboundRequestAuditContextBO();
        context.setParam(param);
        //数据校验
        dataValid(context);
        //拒绝
        RedissonKit.getInstance().lock(RedisConstant.INBOUND_REQUEST_AUDIT_LOCK_PREFIX + param.getId(), lock -> {
            if (StringUtil.equals(param.getAuditResultType(), StringUtil.toString(DataState.DISABLED))) {
                context.setStatus(InboundRequestStatusEnum.REJECTED.getStatus());
                inboundRequestService.updateRequestStatus(context);
                return;
            }
            //同意
            // 1 生成入库工单
            inboundWorkorderService.generateWorkOrder(context);
            // 2 更新入库请求单状态
            context.setStatus(InboundRequestStatusEnum.APPROVED.getStatus());
            context.setDescription(StringUtil.format("Create InBoundWorkOrderRefNum:{}", context.getInboundWorkOrderRefNum()));
            inboundRequestService.updateRequestStatus(context);
        });
    }

    /**
     * 分页查询入库请求
     * <p>
     * 该方法用于分页查询入库请求，忽略仓库限制，直接调用标准入库请求服务的分页查询方法
     * </p>
     *
     * @param search 包含分页参数和查询条件的搜索对象
     * @return 包含分页数据的结果对象
     * <p>
     * TODO: 方法直接委托给标准服务，没有添加任何特殊处理，存在重复代码问题
     * 优化建议：如果确实不需要特殊处理，考虑合并相关服务或使用组合模式
     */
    @Override
    public PageData<InboundRequestPageVO> pageByQuery(PageSearch<InboundRequestQuery> search) {
        return inboundRequestService.pageByQuery(search);
    }

    /**
     * 校验数据
     * <p>
     * 该方法用于验证入库请求审批前的数据有效性，主要检查：
     * 1. 预计到达时间是否合理（不早于当前时间7天）
     * 2. 入库请求状态是否可审批（只有新建和已拒绝的请求才能审批）
     * </p>
     *
     * @param context 入库请求单审批上下文信息
     * @throws BusinessException 如果验证失败，则抛出业务异常
     */
    private void dataValid(InboundRequestAuditContextBO context) {
        // TODO: 方法中存在重复验证，先throw异常又使用Validate进行验证
        // 优化建议：移除冗余的验证代码，统一使用Validate工具类进行验证

        InboundRequestAuditVO param = context.getParam();
        // 校验预计到达时间
        // Long diffTime = FormatUtil.timeCompare(param.getEstimateArrivalDate());
        // if (diffTime > 7L) {
        //     throw new BusinessException("EstimateArrivalDate cannot be earlier than 7 days from the current time Error");
        // }
        // Validate.isTrue(diffTime <= 7L, "EstimateArrivalDate cannot be earlier than 7 days from the current time Error");

        if (ObjectUtil.isEmpty(param.getEstimateArrivalDate())) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "lastShipDate"));
        }
        if (param.getEstimateArrivalDate().isBefore(TimeUtils.now().minusDays(7))) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_INVALID, "lastShipDate", "Cannot be earlier than 7 days from the current time"));
        }

        //获取入库请求单
        InboundRequest inboundRequest = inboundRequestService.getById(param.getId());
        //状态容器
        List<String> list = Lists.arrayList(InboundRequestStatusEnum.NEW.getStatus(), InboundRequestStatusEnum.REJECTED.getStatus());
        if (!list.contains(inboundRequest.getInboundRequestStatus())) {
            throw new BusinessException(inboundRequest.getInboundRequestStatus() + " is audited");
        }
    }
}
