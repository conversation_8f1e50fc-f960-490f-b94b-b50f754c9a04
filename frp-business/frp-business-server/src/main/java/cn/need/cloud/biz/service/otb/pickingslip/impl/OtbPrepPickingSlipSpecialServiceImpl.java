package cn.need.cloud.biz.service.otb.pickingslip.impl;

import cn.hutool.core.lang.Pair;
import cn.need.cloud.biz.client.constant.ErrorConstant;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.ProcessType;
import cn.need.cloud.biz.client.constant.enums.binlocation.BinLocationLogEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbPrepPickingSlipStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.PutAwaySlipType;
import cn.need.cloud.biz.client.constant.special.RollbackConstant;
import cn.need.cloud.biz.model.bo.base.AllocationBO;
import cn.need.cloud.biz.model.bo.base.ChangeQtyLogBO;
import cn.need.cloud.biz.model.bo.base.LockedRollbackBO;
import cn.need.cloud.biz.model.bo.base.WorkorderProcessBO;
import cn.need.cloud.biz.model.bo.base.pickingslip.UnpickDetailBO;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailChangeBO;
import cn.need.cloud.biz.model.bo.common.RefTableBO;
import cn.need.cloud.biz.model.bo.otb.pickingslip.OtbPrepPickingSlipSplitBO;
import cn.need.cloud.biz.model.bo.otb.pickingslip.OtbPrepPickingSlipSplitDetailBO;
import cn.need.cloud.biz.model.bo.otb.pickingslip.OtbPrepPickingSlipUnpickBO;
import cn.need.cloud.biz.model.bo.otb.putawayslip.OtbPrepPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.bo.otb.workorder.OtbPrepWorkorderSplitBO;
import cn.need.cloud.biz.model.bo.otb.workorder.OtbPrepWorkorderSplitDetailBO;
import cn.need.cloud.biz.model.bo.otb.workorder.OtbWorkorderSplitBO;
import cn.need.cloud.biz.model.bo.otb.workorder.OtbWorkorderSplitDetailBO;
import cn.need.cloud.biz.model.entity.base.BaseWorkorderBinLocationModel;
import cn.need.cloud.biz.model.entity.base.pickingslip.BasePickingSlipDetailModel;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.entity.otb.*;
import cn.need.cloud.biz.model.param.base.update.PickingSlipCancelUpdateParam;
import cn.need.cloud.biz.model.param.base.update.RollbackPutawayUnitsUpdateParam;
import cn.need.cloud.biz.model.param.otb.update.pickingslip.prep.OtbPrepPickingSlipUnpickCreateParam;
import cn.need.cloud.biz.model.query.base.WorkorderRollbackListQuery;
import cn.need.cloud.biz.model.vo.base.PrepUnpickVO;
import cn.need.cloud.biz.model.vo.base.pickingslip.PrepPickingSlipUnpickDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderRollbackPutawayUnitsVO;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderConfirmDetailVO;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailLockedService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.service.helper.LockedHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbPrepPickingSlipAuditLogHelper;
import cn.need.cloud.biz.service.helper.pickingslip.OtbPickingSlipHelper;
import cn.need.cloud.biz.service.helper.pickingslip.PickingSlipHelper;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPrepPickingSlipDetailService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPrepPickingSlipService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPrepPickingSlipSpecialService;
import cn.need.cloud.biz.service.otb.putawayslip.OtbPrepPutawaySlipService;
import cn.need.cloud.biz.service.otb.workorder.OtbPrepWorkorderBinLocationService;
import cn.need.cloud.biz.service.otb.workorder.OtbPrepWorkorderService;
import cn.need.cloud.biz.service.otb.workorder.OtbPrepWorkorderSpecialService;
import cn.need.cloud.biz.util.AllocationUtil;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.model.IdModel;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;


/**
 * <p>
 * OTC预提货单 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@AllArgsConstructor(onConstructor = @__(@Lazy))
public class OtbPrepPickingSlipSpecialServiceImpl implements OtbPrepPickingSlipSpecialService {

    private final OtbPrepPickingSlipService otbPrepPickingSlipService;
    private final OtbPrepWorkorderService otbPrepWorkorderService;
    private final OtbPrepWorkorderSpecialService otbPrepWorkorderSpecialService;
    private final OtbPrepPutawaySlipService otbPrepPutawaySlipService;
    private final OtbPrepPickingSlipDetailService otbPrepPickingSlipDetailService;
    private final OtbPrepWorkorderBinLocationService otbPrepWorkorderBinLocationService;
    private final BinLocationDetailLockedService binLocationDetailLockedService;
    private final BinLocationDetailService binLocationDetailService;


    @Override
    public void processTriggering(WorkorderProcessBO process) {
        List<Long> prepPickingSlipIds = process.getPrepPickingSlipIds();
        if (ObjectUtil.isEmpty(prepPickingSlipIds)) {
            return;
        }
        List<OtbPrepPickingSlip> pickingSlipList = otbPrepPickingSlipService.listByIds(prepPickingSlipIds);

        ProcessType processType = process.getProcessType();
        pickingSlipList.forEach(obj -> obj.setProcessType(processType.getType()));

        // 更新
        Validate.isTrue(otbPrepPickingSlipService.updateBatch(pickingSlipList) == pickingSlipList.size(),
                "Update PickingSlip status [{}] failed", processType.getType()
        );

        // 记录日志
        OtbPrepPickingSlipAuditLogHelper.recordLog(pickingSlipList, processType.getType(), null, process.getNote(), BaseTypeLogEnum.PROCESS_TYPE.getType());
    }

    @Override
    public PrepUnpickVO unpickByWorkorderId(Long workorderId) {
        List<PrepPickingSlipUnpickDetailVO> unpickList = this.listByCanUnpick(workorderId)
                .stream()
                .filter(obj -> obj.getCanRollbackQty() > 0)
                .toList();

        // 工单信息
        WorkorderRollbackListQuery query = new WorkorderRollbackListQuery();
        query.setIdList(Collections.singletonList(workorderId));
        List<PrepWorkorderConfirmDetailVO> workorderDetailList = otbPrepWorkorderSpecialService.rollbackList(query);
        return new PrepUnpickVO(unpickList, workorderDetailList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unpick(OtbPrepPickingSlipUnpickCreateParam createParam) {

        OtbPrepPickingSlipUnpickBO query = BeanUtil.copyNew(createParam, OtbPrepPickingSlipUnpickBO.class);
        query.setDetailList(BeanUtil.copyNew(createParam.getDetailList(), UnpickDetailBO.class));

        OtbPrepWorkorder workorder = otbPrepWorkorderService.getById(query.getWorkorderId());

        Validate.notNull(workorder, "WorkorderId {} is null", query.getWorkorderId());

        OtbPrepPickingSlip pickingSlip = otbPrepPickingSlipService.getById(workorder.getOtbPrepPickingSlipId());
        Validate.isTrue(ProcessType.abnormal().contains(pickingSlip.getProcessType()),
                "{} is not {} type, can not unpick", pickingSlip.refNumLog(), ProcessType.abnormal()
        );

        Validate.isTrue(ProcessType.abnormal().contains(workorder.getProcessType()),
                "{} is not {} type, can not unpick", workorder.refNumLog(), ProcessType.abnormal()
        );

        // 当前可上架详情列表
        List<PrepPickingSlipUnpickDetailVO> currentUnpickList = this.listByCanUnpick(workorder.getId());

        query.setCurrentUnpickList(currentUnpickList);
        query.setWorkorder(workorder);
        query.setPickingSlip(pickingSlip);
        query.setPutawaySlipClass(OtbPrepPutawaySlip.class);
        query.setPutawaySlipDetailClass(OtbPrepPutawaySlipDetail.class);
        // 上架类型
        query.setPutawaySlipType(ProcessType.ROLLBACKING.getType().equals(pickingSlip.getProcessType())
                ? PutAwaySlipType.ROLLBACK.getType()
                : PutAwaySlipType.CANCEL.getType()
        );

        // 校验
        PickingSlipHelper.checkUnpickCanRollback(query, currentUnpickList);

        // 创建上架单
        otbPrepPutawaySlipService.unpick(query);
    }

    @Override
    public void rollback(OtbPrepPutawaySlipPutAwayBO putawayParam) {
        OtbPrepPutawaySlip putawaySlip = putawayParam.getPutawaySlip();
        List<OtbPrepPickingSlipDetail> psDetails = otbPrepPickingSlipDetailService.listByOtbPrepPickingSlipId(putawaySlip.getPrepPickingSlipId());
        OtbPrepPickingSlip pickingSlip = otbPrepPickingSlipService.getById(putawaySlip.getPrepPickingSlipId());

        putawayParam.setPickingSlip(pickingSlip);
        putawayParam.setPickingSlipDetails(psDetails);
        // 拣货单详情
        Map<Long, OtbPrepPickingSlipDetail> psDetailMap = StreamUtils.toMap(psDetails, IdModel::getId);

        // Rollback
        List<ChangeQtyLogBO> changeList = putawayParam.getDetailList().stream()
                .map(param -> {
                    OtbPrepPutawaySlipDetail putawaySlipDetail = param.getPutawaySlipDetail();
                    OtbPrepPickingSlipDetail psDetail = psDetailMap.get(putawaySlipDetail.getPrepPickingSlipDetailId());
                    psDetail.setPickedQty(psDetail.getPickedQty() - param.getPutawayQty());

                    // 变更数量
                    ChangeQtyLogBO change = new ChangeQtyLogBO();
                    change.setBeforeQty(psDetail.getPickedQty() + param.getPutawayQty());
                    change.setAfterQty(psDetail.getPickedQty());
                    change.setProductId(psDetail.getProductId());

                    // 库位变更
                    RefTableBO refTable = new RefTableBO();
                    refTable.setRefTableId(psDetail.getId());
                    refTable.setRefTableName(psDetail.getClass().getSimpleName());
                    refTable.setRefTableRefNum(String.valueOf(psDetail.getLineNum()));
                    refTable.setRefTableShowName(pickingSlip.getClass().getSimpleName());
                    refTable.setRefTableShowRefNum(pickingSlip.getRefNum());
                    param.setChangeLogRefTable(refTable);

                    // 设置拣货单库位锁
                    param.setPickBinLocationDetailLockedId(psDetail.getBinLocationDetailLockedId());
                    param.setPickBinLocationDetailId(psDetail.getBinLocationDetailId());
                    param.setPickBinLocationId(psDetail.getBinLocationId());
                    return change;
                })
                .toList();

        boolean isCancelled = psDetails.stream().allMatch(obj -> obj.getQty() == 0);
        boolean allRollback = psDetails.stream().allMatch(obj -> obj.getPickedQty() == 0);
        String oldStatus = pickingSlip.getOtbPrepPickingSlipStatus();

        pickingSlip.setOtbPrepPickingSlipStatus(isCancelled
                ? OtbPrepPickingSlipStatusEnum.CANCELLED.getStatus()
                : allRollback
                ? OtbPrepPickingSlipStatusEnum.NEW.getStatus()
                : OtbPrepPickingSlipStatusEnum.IN_PICKING.getStatus()
        );
        if (!Objects.equals(oldStatus, pickingSlip.getOtbPrepPickingSlipStatus())) {
            Validate.isTrue(otbPrepPickingSlipService.update(pickingSlip) == 1, "Update PickingSlip status is fail");
            OtbPrepPickingSlipAuditLogHelper.recordLog(pickingSlip, null, putawayParam.getNote());
        }

        // 回滚详情
        List<OtbPrepPickingSlipDetail> rollbackDetails = putawayParam.getPickingSlipDetails();

        Validate.isTrue(otbPrepPickingSlipDetailService.updateBatch(rollbackDetails) == rollbackDetails.size(),
                "Update PickingSlipDetail pickedQty is fail"
        );

        // 记录日志
        OtbPrepPickingSlipAuditLogHelper.recordLog(pickingSlip, RollbackConstant.ROLLBACK_PICKED_QTY, JsonUtil.toJson(changeList),
                putawayParam.getNote(), BaseTypeLogEnum.OPERATION.getType()
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCancel(PickingSlipCancelUpdateParam param) {
        List<OtbPrepPickingSlip> cancelList = otbPrepPickingSlipService.listByIds(param.getIdList());
        Validate.notEmpty(cancelList, "PickingSlipId: {} is not exist", param.getIdList());

        // 校验流程类型
        cancelList.forEach(obj -> {
            ProcessType.checkAbnormal(obj.getProcessType(), obj.refNumLog(), "batchCancel");

            // New才可以回滚
            Validate.isTrue(Objects.equals(OtbPrepPickingSlipStatusEnum.NEW.getStatus(), obj.getOtbPrepPickingSlipStatus()),
                    ErrorConstant.STATUS_ERROR_FORMAT,
                    obj.refNumLog(), "batchCancel", OtbPrepPickingSlipStatusEnum.NEW.getStatus(), obj.getOtbPrepPickingSlipStatus()
            );

            // Cancelled
            obj.setOtbPrepPickingSlipStatus(OtbPrepPickingSlipStatusEnum.CANCELLED.getStatus());
        });

        Validate.isTrue(otbPrepPickingSlipService.updateBatch(cancelList) == cancelList.size(),
                "Update PickingSlip status is fail"
        );

        // 记录日志
        OtbPrepPickingSlipAuditLogHelper.recordLog(cancelList, null, param.getNote());

        List<Long> pickingSlipIdList = StreamUtils.distinctMap(cancelList, IdModel::getId);

        // 释放锁
        List<OtbPrepPickingSlipDetail> details = otbPrepPickingSlipDetailService.listByOtbPrepPickingSlipIds(pickingSlipIdList);
        List<Long> lockedIds = StreamUtils.distinctMap(details, OtbPrepPickingSlipDetail::getBinLocationDetailLockedId);

        binLocationDetailLockedService.releaseAll(lockedIds);

        // 工单Cancel
        otbPrepWorkorderSpecialService.cancelWithPickingSlip(pickingSlipIdList);
        return true;
    }

    @Override
    public PrepWorkorderRollbackPutawayUnitsVO rollbackPutAwayUnitsList(Long prepWorkorderId) {

        OtbPrepWorkorder prepWorkorder = otbPrepWorkorderService.getById(prepWorkorderId);
        Validate.notNull(prepWorkorder, "PrepWorkorderId: {} not found in PrepWorkorder", prepWorkorderId);

        ProcessType.checkAbnormal(prepWorkorder.getProcessType(), prepWorkorder.refNumLog(), "rollbackPutAwayUnitsList");

        // 构建Units对象
        return BeanUtil.copyNew(prepWorkorder, PrepWorkorderRollbackPutawayUnitsVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rollbackPutAwayUnits(RollbackPutawayUnitsUpdateParam param) {
        // 工单Rollback
        OtbPrepWorkorder prepWorkorder = otbPrepWorkorderSpecialService.rollbackPutAwayUnits(param);

        // 拣货单Rollback
        OtbPrepPickingSlip prepPickingSlip = otbPrepPickingSlipService.getById(prepWorkorder.getOtbPrepPickingSlipId());

        // 流程校验
        ProcessType.checkAbnormal(prepPickingSlip.getProcessType(), prepPickingSlip.refNumLog(), "rollbackPutAwayUnits");

        prepPickingSlip.setPutawayQty(prepPickingSlip.getPutawayQty() - param.getRollbackQty());

        List<OtbPrepPickingSlipDetail> psDetails = otbPrepPickingSlipDetailService.listByOtbPrepPickingSlipId(prepWorkorder.getOtbPrepPickingSlipId());
        psDetails.forEach(obj -> {
            // Detail PutAwayQty = Detail.qty * Header.putawayQty / Header.qty
            obj.setPutawayQty(obj.getQty() * prepPickingSlip.getPutawayQty() / prepPickingSlip.getQty());
        });

        Validate.isTrue(otbPrepPickingSlipDetailService.updateBatch(psDetails) == psDetails.size(),
                "Update PickingSlipDetail putawayQty is fail"
        );

        String oldStatus = prepPickingSlip.getOtbPrepPickingSlipStatus();

        boolean isPutaway = Objects.equals(prepPickingSlip.getOtbPrepPickingSlipStatus(), OtbPrepPickingSlipStatusEnum.PUT_AWAY.getStatus());
        prepPickingSlip.setOtbPrepPickingSlipStatus(isPutaway
                ? OtbPrepPickingSlipStatusEnum.PICKED.getStatus()
                : oldStatus
        );
        Validate.isTrue(otbPrepPickingSlipService.update(prepPickingSlip) == 1, "Update PickingSlip status is fail");

        // 状态变更
        if (Objects.equals(oldStatus, OtbPrepPickingSlipStatusEnum.PUT_AWAY.getStatus())) {
            OtbPrepPickingSlipAuditLogHelper.recordLog(prepPickingSlip, null, param.getNote());
        }
        // 记录日志
        OtbPrepPickingSlipAuditLogHelper.recordLog(prepPickingSlip, RollbackConstant.ROLLBACK_PUTAWAY_QTY,
                null, param.getNote(), BaseTypeLogEnum.ROLLBACK_PUTAWAY_UNITS.getType()
        );

        // 虚拟库位(上架库位) -> ReadyToGo ，工单锁拆分 -> ReadyToGo锁

        int rollbackQty = param.getRollbackQty();

        // 回滚上架库位
        this.rollbackPutAwayBinLocationDetail(prepWorkorder, rollbackQty);

        // 回滚ReadyToGo库位
        this.rollbackReadyToGoBinLocationDetail(prepWorkorder, psDetails, qty -> qty * rollbackQty / prepPickingSlip.getQty());

        return true;
    }


    @Override
    public void split(List<OtbWorkorderSplitBO> splitHolders) {
        // Prep 拣货单： 拆单
        var prepPickingSlipIds = splitHolders.stream()
                .map(OtbWorkorderSplitBO::getDetailHolders)
                .flatMap(Collection::stream)
                .map(OtbWorkorderSplitDetailBO::getPrepWorkorderHolders)
                .flatMap(Collection::stream)
                .map(OtbPrepWorkorderSplitBO::getPrepWorkorder)
                .map(OtbPrepWorkorder::getOtbPrepPickingSlipId)
                .toList();
        var prepPickingSlips = otbPrepPickingSlipService.listByIds(prepPickingSlipIds);
        if (ObjectUtil.isEmpty(prepPickingSlips)) {
            return;
        }

        var prepPickingSlipDetailsMap = otbPrepPickingSlipDetailService.groupByOtbPrepPickingSlipIds(prepPickingSlipIds);
        var prepPickingSlipMap = StreamUtils.toMap(prepPickingSlips, IdModel::getId);

        // 拆单
        var prepPsHolders = splitHolders.stream()
                // 一个Holder 算一次拆单
                .flatMap(holder -> holder.getDetailHolders().stream()
                        .map(OtbWorkorderSplitDetailBO::getPrepWorkorderHolders)
                        .filter(ObjectUtil::isNotEmpty)
                        .flatMap(Collection::stream)
                        .map(entry -> splitSingle(entry, prepPickingSlipMap, prepPickingSlipDetailsMap)))
                .toList();

        // 拣货单 & Split PrepPickingSlip: Update 状态
        OtbPickingSlipHelper.refreshPrepStatus(prepPickingSlips, prepPickingSlipDetailsMap);
        OtbPickingSlipHelper.refreshPrepStatus(prepPsHolders.stream()
                        .map(OtbPrepPickingSlipSplitBO::getSplitPrepPickingSlip)
                        .toList(),
                prepPsHolders.stream().flatMap(obj -> obj.getPrepDetailHolders().stream()
                                .map(OtbPrepPickingSlipSplitDetailBO::getSplitPrepDetail))
                        .collect(Collectors.groupingBy(OtbPrepPickingSlipDetail::getOtbPrepPickingSlipId))
        );

        // Step: 锁拆单
        this.lockSplit(splitHolders);

        // 更新
        Validate.isTrue(otbPrepPickingSlipService.updateBatch(prepPickingSlips) == prepPickingSlips.size(),
                "Update PrepPickingSlip status failed"
        );
        var details = prepPsHolders.stream()
                .flatMap(obj -> obj.getPrepDetailHolders().stream().map(OtbPrepPickingSlipSplitDetailBO::getPrepDetail))
                .toList();
        Validate.isTrue(otbPrepPickingSlipDetailService.updateBatch(details) == details.size(),
                "Update PrepPickingSlipDetail status failed"
        );

        // 拆单更新
        var splitPickingSlips = prepPsHolders.stream()
                .map(OtbPrepPickingSlipSplitBO::getSplitPrepPickingSlip)
                .toList();
        // 拆单 Detail
        var splitDetails = prepPsHolders.stream()
                .flatMap(holder -> holder.getPrepDetailHolders().stream().map(OtbPrepPickingSlipSplitDetailBO::getSplitPrepDetail))
                .toList();

        otbPrepPickingSlipService.insertBatch(splitPickingSlips);
        otbPrepPickingSlipDetailService.insertBatch(splitDetails);
    }

    /**
     * 锁拆单
     *
     * @param splitHolders 工单拆单
     */
    private void lockSplit(List<OtbWorkorderSplitBO> splitHolders) {
        // 拣货锁
        this.pickLockedSplit(splitHolders);

        // ReadyToGo锁、拣货分配仓储信息
        this.readyToGoSplit(splitHolders);

    }

    /**
     * ReadyToGo锁拆分
     *
     * @param splitHolders 工单拆分信息
     */
    private void readyToGoSplit(List<OtbWorkorderSplitBO> splitHolders) {
        var prepPsHolders = splitHolders.stream()
                .flatMap(obj -> obj.getDetailHolders().stream().flatMap(o -> o.getPrepWorkorderHolders()
                        .stream().map(OtbPrepWorkorderSplitBO::getPrepPickingSlipHolder)))
                .toList();

        // 分配仓储信息拆
        var wkDetailIds = prepPsHolders.stream()
                .flatMap(obj -> obj.getPrepDetailHolders().stream()
                        .map(OtbPrepPickingSlipSplitDetailBO::getSplitWorkorderDetailHolder)
                        .map(OtbPrepWorkorderSplitDetailBO::getPrepDetail)
                        .map(IdModel::getId))
                .toList();
        // 拣货记录
        var wkBinLocationsMap = otbPrepWorkorderBinLocationService.groupByPrepWorkorderDetailId(wkDetailIds);

        // ReadyToGo锁
        var readyToGoLockedIds = wkBinLocationsMap.entrySet()
                .stream()
                .flatMap(obj -> obj.getValue().stream())
                .map(BaseWorkorderBinLocationModel::getBinLocationDetailLockedId)
                .toList();
        var readyToLockedList = binLocationDetailLockedService.listByIds(readyToGoLockedIds);
        var readyToGoLockedMap = StreamUtils.toMap(readyToLockedList, IdModel::getId);

        // Prep工单拆单信息
        var splitPrepWkMap = splitHolders.stream()
                .flatMap(obj -> obj.getDetailHolders().stream().flatMap(o -> o.getPrepWorkorderHolders().stream()))
                .map(OtbPrepWorkorderSplitBO::getSplitPrepWorkorder)
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));

        // ReadyToGo锁和拣货分配拆单
        var splitTriples = readyToGoAndWorkorderBinLocationSplit(splitHolders, wkBinLocationsMap, readyToGoLockedMap, splitPrepWkMap);

        var splitWkBinLocations = splitTriples.stream().map(Pair::getKey).toList();
        otbPrepWorkorderBinLocationService.insertBatch(splitWkBinLocations);
        var wkBinLocations = wkBinLocationsMap.values().stream().flatMap(Collection::stream).toList();
        Validate.isTrue(otbPrepWorkorderBinLocationService.updateBatch(wkBinLocations) == wkBinLocations.size(),
                "Update PrepWorkorderBinLocation qty is fail"
        );

        // 锁更新
        Validate.isTrue(binLocationDetailLockedService.updateBatch(readyToLockedList) == readyToLockedList.size(),
                "Update BinLocationLocked qty fail"
        );

        var splitReadyToGoLockedList = splitTriples.stream().map(Pair::getValue).toList();
        binLocationDetailLockedService.insertBatch(splitReadyToGoLockedList);
    }

    /**
     * 锁和工单分配库位信息拆单
     *
     * @param splitHolders       splitHolders
     * @param wkBinLocationsMap  wkBinLocationsMap
     * @param readyToGoLockedMap readyToGoLockedMap
     * @param splitPrepWkMap     splitPrepWkMap
     * @return /
     */
    private static List<Pair<OtbPrepWorkorderBinLocation, BinLocationDetailLocked>> readyToGoAndWorkorderBinLocationSplit(List<OtbWorkorderSplitBO> splitHolders,
                                                                                                                          Map<Long, List<OtbPrepWorkorderBinLocation>> wkBinLocationsMap,
                                                                                                                          Map<Long, BinLocationDetailLocked> readyToGoLockedMap,
                                                                                                                          Map<Long, OtbPrepWorkorder> splitPrepWkMap) {
        // 锁、分配仓储信息、原单锁释放
        return splitHolders.stream()
                .flatMap(obj -> obj.getDetailHolders().stream())
                .flatMap(wkDetailHolder -> wkDetailHolder.getPrepWorkorderHolders().stream()
                        .map(OtbPrepWorkorderSplitBO::getPrepPickingSlipHolder)
                        .flatMap(obj -> obj.getPrepDetailHolders().stream())
                        // 拆单有拣货数量就需要拆仓储信息
                        .filter(obj -> obj.getSplitPrepDetail().getPickedQty() > 0)
                        // 单个拣货单详情拆单处理
                        .flatMap(psDetailHolder -> pickingSlipDetailSplit(wkBinLocationsMap, readyToGoLockedMap, splitPrepWkMap, wkDetailHolder, psDetailHolder)))
                .toList();
    }

    @NotNull
    private static Stream<Pair<OtbPrepWorkorderBinLocation, BinLocationDetailLocked>> pickingSlipDetailSplit(Map<Long, List<OtbPrepWorkorderBinLocation>> wkBinLocationsMap,
                                                                                                             Map<Long, BinLocationDetailLocked> readyToGoLockedMap,
                                                                                                             Map<Long, OtbPrepWorkorder> splitPrepWkMap,
                                                                                                             OtbWorkorderSplitDetailBO wkDetailHolder,
                                                                                                             OtbPrepPickingSlipSplitDetailBO psDetailHolder) {
        // 当前分配的仓储信息
        var currentWkBinLocations = wkBinLocationsMap.get(psDetailHolder.getPrepDetail().getId());

        var allocationList = BeanUtil.copyNew(currentWkBinLocations, AllocationBO.class);
        // 拆单需要的拣货数量
        var splitPrepDetail = psDetailHolder.getSplitPrepDetail();
        var pickedQty = splitPrepDetail.getPickedQty();
        // 分配
        AllocationUtil.checkAndAllocationQty(allocationList, pickedQty);

        var currentBinLocationMap = StreamUtils.toMap(currentWkBinLocations, IdModel::getId);
        return allocationList.stream()
                .filter(obj -> obj.getChangeQty() > 0)
                .map(allocation -> {
                    var splitPrepWkDetail = psDetailHolder.getSplitWorkorderDetailHolder().getSplitPrepDetail();
                    // 变更数量
                    var changeQty = allocation.getChangeQty();
                    var currentBinLocation = currentBinLocationMap.get(allocation.getId());
                    currentBinLocation.setQty(currentBinLocation.getQty() - changeQty);

                    var splitWkBin = BeanUtil.copyNew(currentBinLocation, OtbPrepWorkorderBinLocation.class);
                    splitWkBin.setId(IdWorker.getId());
                    splitWkBin.setQty(changeQty);
                    splitWkBin.setOtbPrepPickingSlipId(splitPrepDetail.getOtbPrepPickingSlipId());
                    splitWkBin.setOtbPrepPickingSlipDetailId(splitPrepDetail.getId());
                    // Prep工单信息
                    splitWkBin.setOtbPrepWorkorderId(splitPrepWkDetail.getOtbPrepWorkorderId());
                    splitWkBin.setOtbPrepWorkorderDetailId(splitPrepWkDetail.getId());
                    // 工单信息
                    splitWkBin.setOtbWorkorderId(wkDetailHolder.getSplitDetail().getOtbWorkorderId());
                    splitWkBin.setOtbWorkorderDetailId(wkDetailHolder.getSplitDetail().getId());

                    // Step: ReadToGo拆
                    var currentLocked = readyToGoLockedMap.get(currentBinLocation.getBinLocationDetailLockedId());
                    currentLocked.setQty(currentLocked.getQty() - changeQty);
                    var splitFinishQty = currentLocked.getFinishQty() > currentLocked.getQty()
                            ? currentLocked.getFinishQty() - currentLocked.getQty() : 0;
                    currentLocked.setFinishQty(currentLocked.getFinishQty() - splitFinishQty);
                    // 刷新锁状态
                    LockedHelper.statusRefresh(currentLocked);

                    // 拆单上锁
                    BinLocationDetailLocked splitLocked = BeanUtil.copyNew(currentLocked, BinLocationDetailLocked.class);
                    splitLocked.setId(IdWorker.getId());

                    // 设置数量更新状态
                    splitLocked.setQty(changeQty);
                    splitLocked.setFinishQty(splitFinishQty);
                    LockedHelper.statusRefresh(splitLocked);

                    splitLocked.setRefTableId(splitPrepWkDetail.getId());
                    splitLocked.setRefTableRefNum(String.valueOf(splitPrepWkDetail.getLineNum()));

                    Optional.ofNullable(splitPrepWkMap.get(splitPrepWkDetail.getOtbPrepWorkorderId()))
                            .ifPresent(slip -> splitLocked.setRefTableShowRefNum(slip.getRefNum()));

                    splitWkBin.setBinLocationDetailLockedId(splitLocked.getId());
                    // 锁、分配仓储信息、原单锁释放
                    return Pair.of(splitWkBin, splitLocked);
                });
    }

    /**
     * 拣货锁相关拆分
     *
     * @param splitHolders 工单拆单信息
     */
    private void pickLockedSplit(List<OtbWorkorderSplitBO> splitHolders) {
        // 拣货单拆单信息
        var prepPsHolders = splitHolders.stream()
                .flatMap(obj -> obj.getDetailHolders().stream().flatMap(o -> o.getPrepWorkorderHolders().stream().map(OtbPrepWorkorderSplitBO::getPrepPickingSlipHolder)))
                .toList();
        // 拣货单锁
        var splitDetails = prepPsHolders.stream()
                .flatMap(obj -> obj.getPrepDetailHolders().stream().map(OtbPrepPickingSlipSplitDetailBO::getSplitPrepDetail))
                .toList();

        // 拣货单库位锁
        var lockedIds = splitDetails.stream()
                .map(BasePickingSlipDetailModel::getBinLocationDetailLockedId)
                .toList();
        var lockedList = binLocationDetailLockedService.listByIds(lockedIds);
        var lockedMap = StreamUtils.toMap(lockedList, IdModel::getId);

        var splitPrepPsMap = prepPsHolders.stream()
                .map(OtbPrepPickingSlipSplitBO::getSplitPrepPickingSlip)
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));

        // 拆单上锁
        var splitLockedList = splitDetails.stream()
                .map(splitDetail -> {
                    var currentLocked = lockedMap.get(splitDetail.getBinLocationDetailLockedId());
                    currentLocked.setQty(currentLocked.getQty() - splitDetail.getQty());
                    var splitFinishQty = currentLocked.getFinishQty() > currentLocked.getQty()
                            ? currentLocked.getFinishQty() - currentLocked.getQty() : 0;
                    // Locked: FinishQty
                    currentLocked.setFinishQty(currentLocked.getFinishQty() - splitFinishQty);
                    LockedHelper.statusRefresh(currentLocked);

                    // 拆单上锁
                    BinLocationDetailLocked locked = BeanUtil.copyNew(currentLocked, BinLocationDetailLocked.class);
                    locked.setId(IdWorker.getId());

                    // 设置数量更新状态
                    locked.setQty(splitDetail.getQty());
                    locked.setFinishQty(splitFinishQty);
                    LockedHelper.statusRefresh(locked);

                    locked.setRefTableId(splitDetail.getId());
                    locked.setRefTableRefNum(String.valueOf(splitDetail.getLineNum()));
                    Optional.ofNullable(splitPrepPsMap.get(splitDetail.getOtbPrepPickingSlipId()))
                            .ifPresent(slip -> locked.setRefTableShowRefNum(slip.getRefNum()));

                    // 更新库位锁
                    splitDetail.setBinLocationDetailLockedId(locked.getId());
                    return locked;
                })
                .toList();
        binLocationDetailLockedService.insertBatch(splitLockedList);
        Validate.isTrue(binLocationDetailLockedService.updateBatch(lockedList) == lockedList.size(),
                "Update BinLocationLocked qty fail"
        );
    }

    /**
     * 单个工单拆单
     *
     * @param prepHolder                prepHolder
     * @param prepPickingSlipMap        prepPickingSlipMap
     * @param prepPickingSlipDetailsMap prepPickingSlipDetailsMap
     * @return /
     */
    private static OtbPrepPickingSlipSplitBO splitSingle(OtbPrepWorkorderSplitBO prepHolder,
                                                         Map<Long, OtbPrepPickingSlip> prepPickingSlipMap,
                                                         Map<Long, List<OtbPrepPickingSlipDetail>> prepPickingSlipDetailsMap) {
        // 原拣货单
        var prepPickingSlip = prepPickingSlipMap.get(prepHolder.getPrepWorkorder().getOtbPrepPickingSlipId());

        // 拆单
        var splitPrepPs = BeanUtil.copyNew(prepPickingSlip, OtbPrepPickingSlip.class);
        splitPrepPs.setId(IdWorker.getId());
        splitPrepPs.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTC_PREP_PICKING_SLIP.getCode()));
        splitPrepPs.setAllocatePutawayQty(0);

        var splitPrepWorkorder = prepHolder.getSplitPrepWorkorder();
        // 设置原拣货单数量
        prepPickingSlip.setQty(prepPickingSlip.getQty() - prepHolder.getSplitQty());
        prepPickingSlip.setPutawayQty(prepPickingSlip.getPutawayQty() - splitPrepWorkorder.getPutawayQty());

        // 拆单设置
        splitPrepPs.setQty(splitPrepPs.getQty() + prepHolder.getSplitQty());
        splitPrepPs.setPutawayQty(splitPrepPs.getPutawayQty() + splitPrepWorkorder.getPutawayQty());

        // Prep工单拆单设置回拣货单id
        splitPrepWorkorder.setOtbPrepPickingSlipId(splitPrepPs.getId());

        // Details拆分
        var splitPrepPsDetailHolders = prepHolder.getPrepDetailHolders().stream()
                .flatMap(prepDetailHolder -> {
                    var currentPrepDetails = prepPickingSlipDetailsMap.get(prepPickingSlip.getId()).stream()
                            .filter(obj -> Objects.equals(obj.getProductId(), prepDetailHolder.getPrepDetail().getProductId()))
                            .toList();

                    var currentPrepDetailMap = StreamUtils.toMap(currentPrepDetails, IdModel::getId);
                    // Qty数量分配
                    var currentSplitPsDetailHolders = allocationQty(prepDetailHolder, currentPrepDetails, splitPrepPs);
                    // 分配拣货数量
                    allocationPickedQty(prepDetailHolder, currentSplitPsDetailHolders, currentPrepDetailMap);
                    // 分配上架数量
                    allocationPutawayQty(prepDetailHolder, currentSplitPsDetailHolders, currentPrepDetailMap);

                    return currentSplitPsDetailHolders.stream();
                })
                .toList();

        // 设置LineNum
        var splitDetails = splitPrepPsDetailHolders.stream()
                .map(OtbPrepPickingSlipSplitDetailBO::getSplitPrepDetail)
                .toList();

        IntStream.range(0, splitDetails.size())
                .forEach(idx -> {
                    splitDetails.get(idx).setLineNum(idx + 1);
                    splitDetails.get(idx).setId(IdWorker.getId());
                });

        // 封装结果
        var prepPsHolder = new OtbPrepPickingSlipSplitBO();
        prepPsHolder.setPrepPickingSlip(prepPickingSlip);
        prepPsHolder.setSplitPrepPickingSlip(splitPrepPs);
        prepPsHolder.setPrepDetailHolders(splitPrepPsDetailHolders);

        prepHolder.setPrepPickingSlipHolder(prepPsHolder);
        return prepPsHolder;
    }

    /**
     * 分配数量
     *
     * @param prepDetailHolder   prepDetailHolder
     * @param currentPrepDetails currentPrepDetails
     * @param splitPrepPs        splitPrepPs
     * @return /
     */
    private static List<OtbPrepPickingSlipSplitDetailBO> allocationQty(OtbPrepWorkorderSplitDetailBO prepDetailHolder,
                                                                       List<OtbPrepPickingSlipDetail> currentPrepDetails,
                                                                       OtbPrepPickingSlip splitPrepPs) {
        var currentPrepDetailMap = StreamUtils.toMap(currentPrepDetails, IdModel::getId);
        List<AllocationBO> qtyAllocations = currentPrepDetails.stream()
                .map(obj -> {
                    var allocation = BeanUtil.copyNew(obj, AllocationBO.class);
                    allocation.setAllocationQty(0);
                    allocation.setAllocationBeforeQty(0);
                    return allocation;
                })
                .toList();

        // 分配
        AllocationUtil.checkAndAllocationQty(qtyAllocations, prepDetailHolder.getSplitQty());

        // 拆分好的PrepDetail
        return qtyAllocations.stream()
                .filter(obj -> obj.getChangeQty() > 0)
                .map(allocation -> {
                    var prepDetail = currentPrepDetailMap.get(allocation.getId());
                    prepDetail.setQty(prepDetail.getQty() - allocation.getChangeQty());

                    // Detail 拆分
                    var splitPsPrepDetail = BeanUtil.copyNew(prepDetail, OtbPrepPickingSlipDetail.class);
                    splitPsPrepDetail.setOtbPrepPickingSlipId(splitPrepPs.getId());
                    splitPsPrepDetail.setQty(allocation.getChangeQty());

                    // 封装结果
                    var prepPsDetailHolder = new OtbPrepPickingSlipSplitDetailBO();
                    prepPsDetailHolder.setPrepDetail(prepDetail);
                    prepPsDetailHolder.setSplitPrepDetail(splitPsPrepDetail);
                    prepPsDetailHolder.setSplitQty(allocation.getChangeQty());
                    prepPsDetailHolder.setSplitWorkorderDetailHolder(prepDetailHolder);
                    return prepPsDetailHolder;
                })
                .toList();
    }

    /**
     * 分配拣货数量
     *
     * @param prepDetailHolder            prepDetailHolder
     * @param currentSplitPsDetailHolders currentSplitPsDetailHolders
     * @param currentPrepDetailMap        currentPrepDetailMap
     */
    private static void allocationPickedQty(OtbPrepWorkorderSplitDetailBO prepDetailHolder,
                                            List<OtbPrepPickingSlipSplitDetailBO> currentSplitPsDetailHolders,
                                            Map<Long, OtbPrepPickingSlipDetail> currentPrepDetailMap) {

        // 拆分的Details
        var splitPsPrepDetails = StreamUtils.distinctMap(currentSplitPsDetailHolders, OtbPrepPickingSlipSplitDetailBO::getSplitPrepDetail);

        List<AllocationBO> pickedQtyAllocations = splitPsPrepDetails.stream()
                .map(obj -> {
                    var allocation = BeanUtil.copyNew(obj, AllocationBO.class);
                    allocation.setQty(obj.getPickedQty());
                    allocation.setAllocationQty(0);
                    allocation.setAllocationBeforeQty(0);
                    return allocation;
                })
                .toList();

        // 分配
        AllocationUtil.checkAndAllocationQty(pickedQtyAllocations, prepDetailHolder.getSplitPrepDetail().getPickedQty());

        var splitPrepDetailMap = StreamUtils.toMap(splitPsPrepDetails, IdModel::getId);
        pickedQtyAllocations.stream()
                .filter(obj -> obj.getChangeQty() > 0)
                .forEach(obj -> {
                    var detail = currentPrepDetailMap.get(obj.getId());
                    detail.setPickedQty(detail.getPickedQty() - obj.getChangeQty());

                    var splitPsPrepDetail = splitPrepDetailMap.get(obj.getId());
                    splitPsPrepDetail.setPickedQty(obj.getChangeQty());
                });
    }

    /**
     * 分配上架数量
     *
     * @param prepDetailHolder            prepDetailHolder
     * @param currentSplitPsDetailHolders currentSplitPsDetailHolders
     * @param currentPrepDetailMap        currentPrepDetailMap
     */
    private static void allocationPutawayQty(OtbPrepWorkorderSplitDetailBO prepDetailHolder,
                                             List<OtbPrepPickingSlipSplitDetailBO> currentSplitPsDetailHolders,
                                             Map<Long, OtbPrepPickingSlipDetail> currentPrepDetailMap) {
        // 拆分的Details
        var splitPsPrepDetails = StreamUtils.distinctMap(currentSplitPsDetailHolders, OtbPrepPickingSlipSplitDetailBO::getSplitPrepDetail);

        List<AllocationBO> putawayQtyAllocations = splitPsPrepDetails.stream()
                .map(obj -> {
                    var allocation = BeanUtil.copyNew(obj, AllocationBO.class);
                    allocation.setQty(obj.getPutawayQty());
                    allocation.setAllocationQty(0);
                    allocation.setAllocationBeforeQty(0);
                    return allocation;
                })
                .toList();

        // 分配
        AllocationUtil.checkAndAllocationQty(putawayQtyAllocations, prepDetailHolder.getSplitPrepDetail().getPutawayQty());

        var splitPrepDetailMap = StreamUtils.toMap(splitPsPrepDetails, IdModel::getId);
        putawayQtyAllocations.stream()
                .filter(obj -> obj.getChangeQty() > 0)
                .forEach(obj -> {
                    var detail = currentPrepDetailMap.get(obj.getId());
                    detail.setPutawayQty(detail.getPutawayQty() - obj.getChangeQty());

                    var splitPsPrepDetail = splitPrepDetailMap.get(obj.getId());
                    splitPsPrepDetail.setPutawayQty(obj.getChangeQty());
                });
    }


    /**
     * 回滚上架ReadyToGo库位
     *
     * @param prepWorkorder    Prep工单
     * @param psDetails        prep拣货单详情
     * @param rollbackFunction 回滚数量计算
     */
    private void rollbackReadyToGoBinLocationDetail(OtbPrepWorkorder prepWorkorder, List<OtbPrepPickingSlipDetail> psDetails, Function<Integer, Integer> rollbackFunction) {
        // ReadyToGo回滚
        List<OtbPrepWorkorderBinLocation> wkBinLocations = otbPrepWorkorderBinLocationService.listByPrepWorkorderId(prepWorkorder.getId());
        List<Long> readyToGoLockIdList = StreamUtils.distinctMap(wkBinLocations, BaseWorkorderBinLocationModel::getBinLocationDetailLockedId);

        // ReadyToGo锁
        Map<Long, List<BinLocationDetailLocked>> readyToGoGroupByProductMap = binLocationDetailLockedService.listByIds(readyToGoLockIdList).stream()
                // 取释放的锁
                .filter(obj -> obj.getFinishQty() > 0)
                .collect(Collectors.groupingBy(BinLocationDetailLocked::getProductId));

        // 子产品需要回滚的数量
        Map<Long, Integer> productRollbackQtyMap = psDetails.stream()
                .map(obj -> {
                    // int detailRollbackQty = obj.getQty() * rollbackQty / prepPickingSlip.getQty();
                    int detailRollbackQty = rollbackFunction.apply(obj.getQty());
                    return Pair.of(obj.getProductId(), detailRollbackQty);
                })
                .collect(Collectors.groupingBy(Pair::getKey, Collectors.summingInt(Pair::getValue)));

        List<LockedRollbackBO> lockedHolderList = readyToGoGroupByProductMap.entrySet()
                .stream()
                .filter(entry -> productRollbackQtyMap.containsKey(entry.getKey()))
                .flatMap(entry -> {
                    AtomicInteger currentRollbackQty = new AtomicInteger(productRollbackQtyMap.get(entry.getKey()));
                    List<LockedRollbackBO> rollbacks = entry.getValue().stream()
                            .filter(obj -> currentRollbackQty.get() > 0)
                            .map(allocation -> {
                                // 当前可以分配的数量
                                int currentAllocation = Math.min(allocation.getFinishQty(), currentRollbackQty.get());
                                if (currentAllocation > 0) {
                                    allocation.setFinishQty(allocation.getFinishQty() - currentAllocation);
                                    currentRollbackQty.addAndGet(-currentAllocation);
                                    return new LockedRollbackBO(allocation, currentAllocation);
                                }
                                return null;
                            }).toList();
                    Validate.isTrue(currentRollbackQty.get() == 0, "Rollback Locked update fail");
                    return rollbacks.stream();
                })
                .filter(Objects::nonNull)
                .toList();

        List<BinLocationDetailLocked> rollbackLockedList = StreamUtils.distinctMap(lockedHolderList, LockedRollbackBO::getLocked);
        Validate.isTrue(binLocationDetailLockedService.updateBatch(rollbackLockedList) == rollbackLockedList.size(),
                "Rollback Locked update fail"
        );

        // ReadyToGo库位回滚
        List<Long> readyToGoBinLocationDetailIds = lockedHolderList.stream()
                .map(LockedRollbackBO::getLocked)
                .map(BinLocationDetailLocked::getBinLocationDetailId)
                .distinct()
                .toList();
        Map<Long, BinLocationDetail> readyToGoBinMap = StreamUtils.toMap(binLocationDetailService.listByIds(readyToGoBinLocationDetailIds), IdModel::getId);
        List<BinLocationDetailChangeBO> readyToGoChangeList = lockedHolderList.stream()
                .map(obj -> {
                    BinLocationDetailChangeBO change = new BinLocationDetailChangeBO();
                    change.setSource(readyToGoBinMap.get(obj.getLocked().getBinLocationDetailId()));
                    change.setDest(change.getSource());
                    change.setChangeQty(obj.getRollbackQty());

                    change.setRefTable(BeanUtil.copyNew(obj.getLocked(), RefTableBO.class));
                    change.setChangeType(BinLocationLogEnum.ROLLBACK_PUT_AWAY_QTY_UNITS.getStatus());
                    return change;
                })
                .toList();
        binLocationDetailService.updateInStockByChange(readyToGoChangeList);
    }

    /**
     * 回滚上架数量
     *
     * @param prepWorkorder Prep工单
     * @param rollbackQty   回滚数量
     */
    private void rollbackPutAwayBinLocationDetail(OtbPrepWorkorder prepWorkorder, int rollbackQty) {
        // 虚拟库位锁
        Long workorderDetailId = prepWorkorder.getOtbWorkorderDetailId();
        List<BinLocationDetailLocked> lockedList
                = binLocationDetailLockedService.listByRefTableIdLocked(workorderDetailId);

        // 释放虚拟库位锁
        List<AllocationBO> lockedAllocationList = lockedList.stream()
                .map(obj -> {
                    AllocationBO allocation = BeanUtil.copyNew(obj, AllocationBO.class);
                    allocation.setAllocationQty(obj.getFinishQty());
                    allocation.setAllocationBeforeQty(obj.getFinishQty());
                    return allocation;
                })
                .toList();
        // 释放锁
        AllocationUtil.checkAndAllocationQty(lockedAllocationList, rollbackQty);

        Map<Long, AllocationBO> changeLockedMap = lockedAllocationList.stream()
                .filter(obj -> obj.getChangeQty() > 0)
                .collect(Collectors.toMap(AllocationBO::getId, Function.identity()));
        List<BinLocationDetailLocked> changeLockedList = lockedList.stream()
                .map(obj -> {
                    if (changeLockedMap.containsKey(obj.getId())) {
                        obj.setFinishQty(changeLockedMap.get(obj.getId()).getAllocationQty());
                        return obj;
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .toList();
        Validate.isTrue(binLocationDetailLockedService.updateBatch(changeLockedList) == changeLockedList.size(),
                "Rollback Locked update fail"
        );

        // 回滚上架库位
        List<Long> putawayBinLocationDetailIds = StreamUtils.distinctMap(changeLockedList, BinLocationDetailLocked::getBinLocationDetailId);
        Map<Long, BinLocationDetail> putawayDetailMap = StreamUtils.toMap(binLocationDetailService.listByIds(putawayBinLocationDetailIds), IdModel::getId);
        List<BinLocationDetailChangeBO> rollbackChangeList = changeLockedList.stream()
                .map(locked -> {
                    BinLocationDetail putawayDetail = putawayDetailMap.get(locked.getBinLocationDetailId());

                    Validate.notNull(putawayDetail, "LockedId {} Rollback Putaway BinLocationDetail id {} is null",
                            locked.getId(),
                            locked.getBinLocationDetailId()
                    );

                    // 变更数量
                    int changeQty = changeLockedMap.get(locked.getId()).getChangeQty();
                    BinLocationDetailChangeBO putawayRollbackChange = new BinLocationDetailChangeBO();
                    putawayRollbackChange.setChangeQty(changeQty);
                    putawayRollbackChange.setSource(putawayDetail);
                    putawayRollbackChange.setDest(putawayDetail);

                    RefTableBO refTable = BeanUtil.copyNew(locked, RefTableBO.class);
                    putawayRollbackChange.setRefTable(refTable);
                    putawayRollbackChange.setChangeType(BinLocationLogEnum.ROLLBACK_PUT_AWAY_QTY_UNITS.getStatus());
                    return putawayRollbackChange;
                })
                .toList();

        binLocationDetailService.updateInStockByChange(rollbackChangeList);
    }

    /**
     * 获取可unpick的工单仓储位置信息
     *
     * @param workorderId 工单
     * @return /
     */
    public List<PrepPickingSlipUnpickDetailVO> listByCanUnpick(Long workorderId) {
        return otbPrepWorkorderSpecialService.unpickList(Collections.singletonList(workorderId));
    }

}
