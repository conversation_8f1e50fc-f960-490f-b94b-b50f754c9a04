package cn.need.cloud.biz.service.otb.ri;

import cn.need.cloud.biz.model.entity.otb.OtbRoutingInstruction;
import cn.need.cloud.biz.model.entity.otb.OtbShipment;
import cn.need.cloud.biz.model.param.otb.create.ri.OtbRoutingInstructionCreateParam;
import cn.need.cloud.biz.model.param.otb.update.routing.OtbRoutingInstructionUpdateParam;
import cn.need.cloud.biz.model.query.otb.routing.OtbRoutingInstructionQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.otb.page.OtbRoutingInstructionPageVO;
import cn.need.cloud.biz.model.vo.otb.ri.OtbRoutingInstructionVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * otb发货指南 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbRoutingInstructionService extends SuperService<OtbRoutingInstruction> {

    /**
     * 根据参数新增otb发货指南
     *
     * @param createParam 请求创建参数，包含需要插入的otb发货指南的相关信息
     * @return otb发货指南实体对象
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    OtbRoutingInstruction insertByParam(OtbRoutingInstructionCreateParam createParam);

    /**
     * 根据货运单号检查是否存在对应的路由指令
     *
     * @param shippingRefNum 货运单号，用于查询路由指令
     * @return 返回一个布尔值，如果存在对应的路由指令则为true，否则为false
     */
    boolean existRoutingInstructionByShipmentRefNum(String shippingRefNum);

    /**
     * 根据参数更新otb发货指南
     *
     * @param updateParam 请求创建参数，包含需要更新的otb发货指南的相关信息
     * @return 返回更新后的otb发货指南实体对象
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    OtbRoutingInstruction updateByParam(OtbRoutingInstructionUpdateParam updateParam);

    /**
     * 根据查询条件获取otb发货指南列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个otb发货指南对象的列表(分页)
     */
    List<OtbRoutingInstructionPageVO> listByQuery(OtbRoutingInstructionQuery query);

    /**
     * 根据查询条件获取otb发货指南列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个otb发货指南对象的列表(分页)
     */
    PageData<OtbRoutingInstructionPageVO> pageByQuery(PageSearch<OtbRoutingInstructionQuery> search);

    /**
     * 根据ID获取otb发货指南
     *
     * @param id otb发货指南ID
     * @return 返回otb发货指南VO对象
     */
    OtbRoutingInstructionVO detailById(Long id);

    /**
     * 根据otb发货指南唯一编码获取otb发货指南
     *
     * @param refNum otb发货指南唯一编码
     * @return 返回otb发货指南VO对象
     */
    OtbRoutingInstructionVO detailByRefNum(String refNum);


    /**
     * 处理提交操作
     *
     * @param id 需要进行提交操作的实体的ID
     * @return 返回操作结果，如果操作成功则返回非空的Integer值，否则返回null
     */
    Integer committed(Long id);

    /**
     * 下拉列表pro 统计
     *
     * @param query 查询条件
     * @return /
     */
    List<DropProVO> countPreDay(OtbRoutingInstructionQuery query);

    /**
     * 获取otb发货指南
     *
     * @param otbShipmentId otb发货单ID
     * @return 返回otb发货指南VO对象
     */
    OtbRoutingInstruction getByShipmentId(Long otbShipmentId);

    /**
     * 通道确认
     *
     * @param otbRoutingInstruction otb发货指南对象
     * @param otbShipment           otb发货单对象
     */
    void channelConfirmed(OtbRoutingInstruction otbRoutingInstruction, OtbShipment otbShipment);

    /**
     * 生成标签
     *
     * @param instruction otb发货指南对象
     * @param otbShipment otb发货单对象
     */
    void generateLabel(OtbRoutingInstruction instruction, OtbShipment otbShipment);

    /**
     * 根据otb发货单ID列表获取otb发货指南列表
     *
     * @param otbShipmentIdList otb发货单ID列表
     * @return 返回otb发货指南VO对象的列表
     */
    List<OtbRoutingInstructionVO> listByShipmentIdList(Set<Long> otbShipmentIdList);

    /**
     * otb路由请求字段去重下拉
     *
     * @param query 查询条件
     * @return 返回下拉列表
     */
    List<DropProVO> distinctValue(OtbRoutingInstructionQuery query);

}