package cn.need.cloud.biz.model.query.otb.pkg;

import cn.need.cloud.biz.model.query.otb.pickingslip.OtbPickingSlipQuery;
import cn.need.cloud.biz.model.query.otb.request.OtbRequestQuery;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkorderQuery;
import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;
import java.util.Set;


/**
 * OTB包裹 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTB包裹 query对象")
public class OtbPackageQuery extends SuperQuery {

    @Serial
    private static final long serialVersionUID = 5624283369006319090L;
    /**
     * Serial Shipping Container Code，序列化货运容器代码
     */
    @Schema(description = "Serial Shipping Container Code，序列化货运容器代码")
    private String ssccNum;

    /**
     * Serial Shipping Container Code，序列化货运容器代码
     */
    @Schema(description = "Serial Shipping Container Code，序列化货运容器代码集合")
    @Condition(value = Keyword.IN, fields = {"ssccnum"})
    private List<String> ssccNumList;

    /**
     * Serial Shipping Container Code，序列化货运容器代码
     */
    @Schema(description = "PalletSsccNum")
    private String otbPalletSsccNum;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * otb请求id
     */
    @Schema(description = "otb请求id")
    private Set<Long> otbRequestIdList;

    /**
     * otb请求requestRefNum
     */
    @Schema(description = "otb请求requestRefNum")
    private Set<String> requestOfRequestRefNumList;

    /**
     * otb工单id
     */
    @Schema(description = "otb工单id")
    private Set<Long> otbWorkorderIdList;

    /**
     * otb工单id
     */
    @Schema(description = "otb工单id")
    private Long otbWorkorderId;

    /**
     * otb工单refNum
     */
    @Schema(description = "otb工单refNum")
    private Set<String> otbWorkorderRefNumList;

    /**
     * otb装运id
     */
    @Schema(description = "otb装运id")
    private Long otbShipmentId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private List<String> refNumList;

    /**
     * otb托盘id
     */
    @Schema(description = "otb托盘id")
    private Long otbPalletId;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNum;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private Set<String> orderNumList;

    /**
     * otb包裹状态
     */
    @Schema(description = "otb包裹状态")
    private String otbPackageStatus;

    /**
     * otb包裹状态
     */
    @Schema(description = "otb包裹状态集合")
    @Condition(value = Keyword.IN, fields = {"otbPackageStatus"})
    private List<String> otbPackageStatusList;

    /**
     * otb包裹类型
     */
    @Schema(description = "otb包裹类型")
    private String otbPackageType;

    /**
     * otb包裹类型
     */
    @Schema(description = "otb包裹类型集合")
    @Condition(value = Keyword.IN, fields = {"otbPackageType"})
    private List<String> otbPackageTypeList;

    /**
     * otb托盘类型
     */
    @Schema(description = "otb托盘类型")
    private String otbPalletType;

    /**
     * otb托盘类型
     */
    @Schema(description = "otb托盘类型集合")
    @Condition(value = Keyword.IN, fields = {"otbPalletType"})
    private List<String> otbPalletTypeList;

    /**
     * otb拣货单id
     */
    @Schema(description = "otb拣货单id")
    private Set<Long> otbPickingSlipIdList;

    /**
     * otb拣货单id
     */
    @Schema(description = "otb拣货单id")
    private Long otbPickingSlipId;

    /**
     * otb拣货单refNum
     */
    @Schema(description = "otb拣货单refNum")
    private Set<String> otbPickingSlipRefNumList;

    /**
     * 短ssccnum
     */
    @Schema(description = "短ssccnum")
    private String shortSsccNum;

    /**
     * 短ssccnum
     */
    @Schema(description = "短ssccnum集合")
    @Condition(value = Keyword.IN, fields = {"shortSsccNum"})
    private List<String> shortSsccNumList;

    /**
     * 发货站
     */
    @Schema(description = "发货站")
    private String station;

    /**
     * 发货站
     */
    @Schema(description = "发货站集合")
    @Condition(value = Keyword.IN, fields = {"station"})
    private List<String> stationList;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司集合")
    @Condition(value = Keyword.IN, fields = {"shipCarrier"})
    private List<String> shipCarrierList;

    /**
     * 运输方式
     */
    @Schema(description = "运输方式")
    private String shipMethod;

    /**
     * 运输方式
     */
    @Schema(description = "运输方式集合")
    @Condition(value = Keyword.IN, fields = {"shipMethod"})
    private List<String> shipMethodList;

    /**
     * 快递Api配置RefNum
     */
    @Schema(description = "快递Api配置RefNum")
    private String shipApiProfileRefNum;

    /**
     * 快递Api配置RefNum
     */
    @Schema(description = "快递Api配置RefNum集合")
    @Condition(value = Keyword.IN, fields = {"shipApiProfileRefNum"})
    private List<String> shipApiProfileRefNumList;

    /**
     * 快递号
     */
    @Schema(description = "快递号")
    private String trackingNum;

    /**
     * 快递号
     */
    @Schema(description = "快递号集合")
    @Condition(value = Keyword.IN, fields = {"trackingNum"})
    private List<String> trackingNumList;

    /**
     * 打印状态
     */
    @Schema(description = "快递号集合")
    private List<String> printStatusList;

    /**
     * 包裹id
     */
    @Schema(description = "包裹id")
    private List<Long> idList;

    @Schema(description = "otb工单查询条件")
    private OtbWorkorderQuery otbWorkorderQuery;

    @Schema(description = "otb拣货单查询条件")
    private OtbPickingSlipQuery otbPickingSlipQuery;

    @Schema(description = "otb请求查询条件")
    private OtbRequestQuery otbRequestQuery;

    @Schema(description = "otb包裹标签查询条件")
    private OtbPackageLabelQuery otbPackageLabelQuery;

    @Schema(description = "流程类型")
    private String processType;
}