package cn.need.cloud.biz.service.otc.pickingslip.impl;

import cn.need.cloud.biz.client.constant.enums.base.PickFromType;
import cn.need.cloud.biz.client.constant.enums.base.PrintStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcBuildShipPackageEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcOrderTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPickingSlipStatusEnum;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.otc.OtcPickingSlip;
import cn.need.cloud.biz.model.entity.otc.OtcPickingSlipDetail;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorder;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorderDetail;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.session.Users;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/5/21
 */


public class OtcPickingSlipBuildHelper {

    /**
     * 创建拣货单
     *
     * @param orderType 订单类型
     * @param orders    工单集合
     * @return /
     */
    public static OtcPickingSlip createPickingSlip(OtcOrderTypeEnum orderType, List<OtcWorkorder> orders) {
        OtcWorkorder first = orders.get(0);
        OtcPickingSlip slip = new OtcPickingSlip();
        slip.setId(IdWorker.getId());
        slip.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTC_PICKING_SLIP.getCode()));
        slip.setOrderType(orderType.getStatus());
        slip.setPickFromType(PickFromType.NONE.getType());
        slip.setPickToStation(first.getPickToStation());
        slip.setHasCusShipRequire(first.getRequestSnapshotHasCusShipRequire());
        slip.setOnSitePackFlag(OtcBuildShipPackageEnum.BY_WAREHOUSE.getStatus().equals(first.getBuildShipPackageType()));
        slip.setAssignedUserId(Objects.requireNonNull(Users.getUser()).getId());
        slip.setPickingSlipStatus(OtcPickingSlipStatusEnum.NEW.getStatus());
        slip.setPrintStatus(PrintStatusEnum.NONE.getStatus());
        slip.setTransactionPartnerId(first.getRequestSnapshotTransactionPartnerId());
        slip.setPickingSlipProductType(first.getWorkorderProductType());
        // 工单赋值
        orders.forEach(obj -> obj.setOtcPickingSlipId(slip.getId()));
        return slip;
    }

    /**
     * 构建拣货单详情
     *
     * @param workOrder         工单
     * @param workOrderDetail   工单详情
     * @param binLocationDetail 库位详情
     * @return 拣货单详情
     */
    public static OtcPickingSlipDetail buildPickingSlipDetail(OtcWorkorder workOrder, OtcWorkorderDetail workOrderDetail, BinLocationDetail binLocationDetail) {
        OtcPickingSlipDetail slipDetail = new OtcPickingSlipDetail();
        BeanUtil.copy(workOrderDetail, slipDetail);

        // 手动设置id
        slipDetail.setId(IdWorker.getId());
        slipDetail.setLineNum(0);
        // 拣货单id
        slipDetail.setOtcPickingSlipId(workOrder.getOtcPickingSlipId());
        // 库位信息
        slipDetail.setBinLocationDetailId(binLocationDetail.getId());
        slipDetail.setBinLocationId(binLocationDetail.getBinLocationId());
        // 产品信息
        slipDetail.setProductVersionId(binLocationDetail.getProductVersionId());
        slipDetail.setProductId(binLocationDetail.getProductId());

        // 数量赋值
        slipDetail.setAllocateQty(0);
        // 拣货数量
        slipDetail.setPickedQty(0);
        return slipDetail;
    }
}
