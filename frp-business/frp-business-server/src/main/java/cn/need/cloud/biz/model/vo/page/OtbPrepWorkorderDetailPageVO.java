package cn.need.cloud.biz.model.vo.page;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * OTB预提工单详情 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTB预提工单详情 vo对象")
public class OtbPrepWorkorderDetailPageVO implements Serializable {


    @Serial
    private static final long serialVersionUID = 4257890880876627525L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 库存锁定id
     */
    @Schema(description = "库存锁定id")
    private Long inventoryLockedId;

    /**
     * 拣货数量
     */
    @Schema(description = "拣货数量")
    private Integer pickedQty;

    /**
     * 上架数量
     */
    @Schema(description = "上架数量")
    private Integer putawayQty;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    private String hazmatVersionRefNum;

    /**
     * otb预工单id
     */
    @Schema(description = "otb预工单id")
    private Long otbPrepWorkorderId;


}