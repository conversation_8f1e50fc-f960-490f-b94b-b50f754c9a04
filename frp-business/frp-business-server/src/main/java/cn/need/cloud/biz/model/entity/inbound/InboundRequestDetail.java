package cn.need.cloud.biz.model.entity.inbound;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * 入库请求详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inbound_request_detail")
public class InboundRequestDetail extends SuperModel {


    /**
     * 行序号
     */
    @TableField("line_num")
    private Integer lineNum;

    /**
     * 备注
     */
    @TableField("note")
    private String note;


    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 数量
     */
    @TableField("qty")
    private Integer qty;

    /**
     * 入库请求Id
     */
    @TableField("inbound_request_id")
    private Long inboundRequestId;

    /**
     * 产品id
     */
    @TableField("product_version_id")
    private Long productVersionId;

    /**
     * 详情请求参考编号
     */
    @TableField("detail_request_ref_num")
    private String detailRequestRefNum;

    /**
     * 详情类型
     */
    @TableField("detail_type")
    private String detailType;

    /**
     * 产品类型
     */
    @TableField("product_type")
    private String productType;

}
