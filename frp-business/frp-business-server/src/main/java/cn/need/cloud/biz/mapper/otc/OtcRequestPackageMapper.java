package cn.need.cloud.biz.mapper.otc;

import cn.need.cloud.biz.model.entity.otc.OtcRequestPackage;
import cn.need.cloud.biz.model.query.otc.request.OtcRequestPackageQuery;
import cn.need.cloud.biz.model.vo.otc.page.OtcRequestPackagePageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * OTC请求包裹 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtcRequestPackageMapper extends SuperMapper<OtcRequestPackage> {

    /**
     * 根据条件获取OTC请求包裹列表
     *
     * @param query 查询条件
     * @return OTC请求包裹集合
     */
    default List<OtcRequestPackagePageVO> listByQuery(OtcRequestPackageQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取OTC请求包裹分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return OTC请求包裹集合
     */
    List<OtcRequestPackagePageVO> listByQuery(@Param("qo") OtcRequestPackageQuery query, @Param("page") Page<?> page);
}