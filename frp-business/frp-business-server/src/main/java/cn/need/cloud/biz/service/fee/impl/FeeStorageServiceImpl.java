package cn.need.cloud.biz.service.fee.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.fee.FeeStorageConverter;
import cn.need.cloud.biz.mapper.fee.FeeStorageMapper;
import cn.need.cloud.biz.model.entity.fee.FeeStorage;
import cn.need.cloud.biz.model.param.fee.create.FeeStorageCreateParam;
import cn.need.cloud.biz.model.param.fee.update.FeeStorageUpdateParam;
import cn.need.cloud.biz.model.query.fee.FeeStorageQuery;
import cn.need.cloud.biz.model.vo.fee.FeeStorageVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeStoragePageVO;
import cn.need.cloud.biz.service.fee.FeeStorageService;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 费用storage service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Service
public class FeeStorageServiceImpl extends SuperServiceImpl<FeeStorageMapper, FeeStorage> implements FeeStorageService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(FeeStorageCreateParam createParam) {
        // 检查传入费用storage参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 将费用storage参数对象转换为实体对象并初始化
        FeeStorage entity = initFeeStorage(createParam);

        // 插入费用storage实体对象到数据库
        super.insert(entity);

        // 返回费用storageID
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(FeeStorageUpdateParam updateParam) {
        // 检查传入费用storage参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 将费用storage参数对象转换为实体对象
        FeeStorage entity = initFeeStorage(updateParam);

        // 执行更新费用storage操作
        return super.update(entity);

    }

    @Override
    public List<FeeStoragePageVO> listByQuery(FeeStorageQuery query) {
        if (ObjectUtil.isEmpty(query)) {
            return Collections.emptyList();
        }
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<FeeStoragePageVO> pageByQuery(PageSearch<FeeStorageQuery> search) {
        Page<FeeStorage> page = Conditions.page(search, entityClass);
        List<FeeStoragePageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public FeeStorageVO detailById(Long id) {
        FeeStorage entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "FeeStorage", id));
        }
        return buildFeeStorageVO(entity);
    }

    @Override
    public FeeStorageVO detailByRefNum(String refNum) {
        FeeStorage entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "FeeStorage", "refNum", refNum));
        }
        return buildFeeStorageVO(entity);
    }

    /**
     * 初始化费用storage对象
     * 此方法用于设置费用storage对象的必要参数，确保其处于有效状态
     *
     * @param createParam 费用storage 新增对象，不应为空
     * @return 返回初始化后的费用storage
     * @throws BusinessException 如果传入的费用storage为空，则抛出此异常
     */
    private FeeStorage initFeeStorage(FeeStorageCreateParam createParam) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "FeeStorageCreateParam"));
        }

        // 获取费用storage转换器实例，用于将费用storage参数对象转换为实体对象
        FeeStorageConverter converter = Converters.get(FeeStorageConverter.class);

        // 将费用storage参数对象转换为实体对象并初始化
        FeeStorage entity = converter.toEntity(createParam);

        entity.setId(IdWorker.getId());
        entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.FEE_STORAGE));

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 初始化费用storage对象
     * 此方法用于设置费用storage对象的必要参数，确保其处于有效状态
     *
     * @param updateParam 费用storage 修改对象，不应为空
     * @return 返回初始化后的费用storage
     * @throws BusinessException 如果传入的费用storage为空，则抛出此异常
     */
    private FeeStorage initFeeStorage(FeeStorageUpdateParam updateParam) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(updateParam)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "FeeStorageUpdateParam"));
        }

        // 获取费用storage转换器实例，用于将费用storage参数对象转换为实体对象
        FeeStorageConverter converter = Converters.get(FeeStorageConverter.class);

        // 将费用storage参数对象转换为实体对象并初始化
        FeeStorage entity = converter.toEntity(updateParam);

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 构建费用storageVO对象
     *
     * @param entity 费用storage对象
     * @return 返回包含详细信息的费用storageVO对象
     */
    private FeeStorageVO buildFeeStorageVO(FeeStorage entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的费用storageVO对象
        return Converters.get(FeeStorageConverter.class).toVO(entity);
    }

}
