package cn.need.cloud.biz.mapper.otc;

import cn.need.cloud.biz.model.entity.otc.OtcRequestDetail;
import cn.need.cloud.biz.model.query.otc.request.OtcRequestDetailQuery;
import cn.need.cloud.biz.model.vo.otc.page.OtcRequestDetailPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * OTC请求详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtcRequestDetailMapper extends SuperMapper<OtcRequestDetail> {

    /**
     * 根据条件获取OTC请求详情列表
     *
     * @param query 查询条件
     * @return OTC请求详情集合
     */
    default List<OtcRequestDetailPageVO> listByQuery(OtcRequestDetailQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取OTC请求详情分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return OTC请求详情集合
     */
    List<OtcRequestDetailPageVO> listByQuery(@Param("qo") OtcRequestDetailQuery query, @Param("page") Page<?> page);
}