package cn.need.cloud.biz.model.vo.warehouse;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 仓库基础信息 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "仓库基础信息 vo对象")
public class WarehouseVO extends BaseSuperVO {


    /**
     * 仓库编号
     */
    @Schema(description = "仓库编号")
    private String code;

    /**
     * 仓库名称
     */
    @Schema(description = "仓库名称")
    private String name;

    /**
     * 时区
     */
    @Schema(description = "时区")
    private String timeZone;

    /**
     * 名字
     */
    @Schema(description = "名字")
    private String addressName;

    /**
     * 公司
     */
    @Schema(description = "公司")
    private String addressCompany;

    /**
     * 国家
     */
    @Schema(description = "国家")
    private String addressCountry;

    /**
     * 洲
     */
    @Schema(description = "洲")
    private String addressState;

    /**
     * 城市
     */
    @Schema(description = "城市")
    private String addressCity;

    /**
     * 邮编
     */
    @Schema(description = "邮编")
    private String addressZipCode;

    /**
     * 地址1
     */
    @Schema(description = "地址1")
    private String addressAddr1;

    /**
     * 地址2
     */
    @Schema(description = "地址2")
    private String addressAddr2;

    /**
     * 地址3
     */
    @Schema(description = "地址3")
    private String addressAddr3;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String addressEmail;

    /**
     * 电话
     */
    @Schema(description = "电话")
    private String addressPhone;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String addressNote;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 是否是住宅地址
     */
    @Schema(description = "是否是住宅地址")
    private Integer addressIsResidential;

    /**
     * amazonShipProfileShipApiRefNum
     */
    @Schema(description = "amazonShipProfileShipApiRefNum")
    private String amazonShipProfileShipApiRefNum;

    /**
     * fedexShipProfileShipApiRefNum
     */
    @Schema(description = "fedexShipProfileShipApiRefNum")
    private String fedexShipProfileShipApiRefNum;

    /**
     * upsshipProfileShipApiRefNum
     */
    @Schema(description = "upsshipProfileShipApiRefNum")
    private String upsshipProfileShipApiRefNum;

    @Schema(description = "ontracShipProfileShipApiRefNum")
    private String ontracShipProfileShipApiRefNum;

    /**
     * 是否有效
     */
    @Schema(description = "是否有效")
    private Boolean activeFlag;

    /**
     * amazonShipPalletProfileShipApiRefNum
     */
    @Schema(description = "amazonShipPalletProfileShipApiRefNum")
    private String amazonShipPalletProfileShipApiRefNum;

    @Schema(description = "uspsShipPalletProfileShipApiRefNum")
    private String uspsShipPalletProfileShipApiRefNum;

    /**
     * 亚马逊仓库编码
     */
    @Schema(description = "亚马逊仓库编码")
    private String amazonWarehouseCode;

    /**
     * ssccprefix
     */
    @Schema(description = "ssccprefix")
    private String ssccprefix;

    /**
     * OCWAssembly 默认库位
     */
    @Schema(description = "OCWAssembly 默认库位")
    private Long defaultOTCAssemblyBinLocationId;

    /**
     * OCWAssembly 默认库位名称
     */
    @Schema(description = "OCWAssembly 默认库位名称")
    private String defaultOTCAssemblyBinLocationName;

    /**
     * OCWConvert 默认库位
     */
    @Schema(description = "OCWConvert 默认库位")
    private Long defaultOTCConvertBinLocationId;

    /**
     * OCWConvert 默认库位名称
     */
    @Schema(description = "OCWConvert 默认库位名称")
    private String defaultOTCConvertBinLocationName;

    /**
     * OCWMultiBox 默认库位
     */
    @Schema(description = "OCWMultiBox 默认库位")
    private Long defaultOTCMultiBoxBinLocationId;

    /**
     * OCWMultiBox 默认库位名称
     */
    @Schema(description = "OCWMultiBox 默认库位名称")
    private String defaultOTCMultiBoxBinLocationName;

    /**
     * OTCReadyToGo 默认库位
     */
    @Schema(description = "OTCReadyToGo 默认库位")
    private Long defaultOTCReadyToGoBinLocationId;

    /**
     * OTCReadyToGo 默认库位名称
     */
    @Schema(description = "OTCReadyToGo 默认库位名称")
    private String defaultOTCReadyToGoBinLocationName;

    /**
     * OTCOutSide 默认库位
     */
    @Schema(description = "OTCOutSide 默认库位")
    private Long defaultOTCOutSideBinLocationId;

    /**
     * OTCOutSide 默认库位名称
     */
    @Schema(description = "OTCOutSide 默认库位名称")
    private String defaultOTCOutSideBinLocationName;

    /**
     * OBWMultiBox 默认库位
     */
    @Schema(description = "OBWMultiBox 默认库位")
    private Long defaultOTBMultiBoxBinLocationId;

    /**
     * OBWMultiBox 默认库位
     */
    @Schema(description = "OBWMultiBox 默认库位")
    private String defaultOTBMultiBoxBinLocationName;

    /**
     * OBWConvert 默认库位
     */
    @Schema(description = "OBWConvert 默认库位")
    private Long defaultOTBConvertBinLocationId;

    /**
     * OBWConvert 默认库位名称
     */
    @Schema(description = "OBWConvert 默认库位名称")
    private String defaultOTBConvertBinLocationName;

    /**
     * OBWAssembly 默认库位
     */
    @Schema(description = "OBWAssembly 默认库位")
    private Long defaultOTBAssemblyBinLocationId;

    /**
     * OBWAssembly 默认库位名称
     */
    @Schema(description = "OBWAssembly 默认库位名称")
    private String defaultOTBAssemblyBinLocationName;

    /**
     * OTBReadyToGo 默认库位
     */
    @Schema(description = "OTBReadyToGo 默认库位")
    private Long defaultOTBReadyToGoBinLocationId;

    /**
     * OTBReadyToGo 默认库位名称
     */
    @Schema(description = "OTBReadyToGo 默认库位名称")
    private String defaultOTBReadyToGoBinLocationName;

    /**
     * OTBOutSide 默认库位
     */
    @Schema(description = "OTBOutSide 默认库位")
    private Long defaultOTBOutSideBinLocationId;

    /**
     * OTBOutSide 默认库位名称
     */
    @Schema(description = "OTBOutSide 默认库位名称")
    private String defaultOTBOutSideBinLocationName;

    @Schema(description = "销售方信息")
    private String amazonSellingPartyCode;

    @Schema(description = "amazonVendorContainerCode")
    private String amazonVendorContainerCode;

}