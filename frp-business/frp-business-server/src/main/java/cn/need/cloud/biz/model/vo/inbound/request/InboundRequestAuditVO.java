package cn.need.cloud.biz.model.vo.inbound.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 入库请求审批 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@Schema(description = "入库请求审批 vo对象")
@NoArgsConstructor
@AllArgsConstructor
public class InboundRequestAuditVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;


    /**
     * 预计到达日期
     */
    @Schema(description = "预计到达日期")
    private LocalDateTime estimateArrivalDate;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 审批状态 1：同意，0：拒绝
     */
    @Schema(description = "审批状态 1：同意，0：拒绝")
    private String auditResultType;

    /**
     * 入库请求单id
     */
    @Schema(description = "入库请求单id")
    private Long id;

    /**
     * 审批备注
     */
    @Schema(description = "审批备注")
    private String note;

    /**
     * 预计到达时间可审批时间范围  单位 天
     */
    @Schema(description = "预计到达时间可审批时间范围  单位 天")
    private Long diff = 7L;

}
