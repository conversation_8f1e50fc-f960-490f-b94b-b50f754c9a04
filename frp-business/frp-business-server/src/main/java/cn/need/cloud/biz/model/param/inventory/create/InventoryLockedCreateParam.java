package cn.need.cloud.biz.model.param.inventory.create;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值 vo对象")
public class InventoryLockedCreateParam implements Serializable {


    /**
     * refTableId
     */
    @Schema(description = "refTableId")
    private Long refTableId;

    /**
     * refTableName
     */
    @Schema(description = "refTableName")
    private String refTableName;

    /**
     * refTableRefNum
     */
    @Schema(description = "refTableRefNum")
    private String refTableRefNum;

    /**
     * refTableShowName
     */
    @Schema(description = "refTableShowName")
    private String refTableShowName;

    /**
     * refTableShowRefNum
     */
    @Schema(description = "refTableShowRefNum")
    private String refTableShowRefNum;


    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;


    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;


    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

}