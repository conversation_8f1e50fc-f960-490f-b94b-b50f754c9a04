package cn.need.cloud.biz.model.param.otb.create.ri;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * otb发货指南托盘标签 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "otb发货指南托盘标签 vo对象")
public class OtbRoutingInstructionPalletLabelCreateParam implements Serializable {


    /**
     * 路由指令ID
     */
    @Schema(description = "路由指令ID")
    private Long otbRoutingInstructionId;

    /**
     * 托盘SSCC编号
     */
    @Schema(description = "托盘SSCC编号")
    private String palletSsccNum;

    /**
     * 面单类型
     */
    @Schema(description = "面单类型")
    private String labelType;

    /**
     * label RefNum
     */
    @Schema(description = "label RefNum")
    private String labelRefNum;

    /**
     * 纸张类型
     */
    @Schema(description = "纸张类型")
    private String paperType;

    /**
     * 数据类型
     */
    @Schema(description = "数据类型")
    private String rawDataType;

    /**
     * 文件系统数据类型
     */
    @Schema(description = "文件系统数据类型")
    private String fileIdRawDataType;

    /**
     * label数据类型
     */
    @Schema(description = "label数据类型")
    private String labelRawData;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;


}