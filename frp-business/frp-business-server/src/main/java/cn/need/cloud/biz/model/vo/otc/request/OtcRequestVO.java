package cn.need.cloud.biz.model.vo.otc.request;

import cn.need.cloud.biz.model.entity.base.RefNumModel;
import cn.need.cloud.biz.model.vo.base.BasePartnerVO;
import cn.need.cloud.biz.model.vo.base.BaseWarehouseVO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * OTC请求 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTC请求 vo对象")
public class OtcRequestVO extends RefNumModel {


    @Schema(description = "参考编号")
    private String refNum;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码")
    private String requestRefNum;

    @Schema(description = "仓库ID")
    private Long warehouseId;


    @Schema(description = "基础仓库信息")
    private BaseWarehouseVO baseWarehouseVO;

    /**
     * 是否快递标志
     */
    @Schema(description = "是否快递标志")
    private Boolean shipExpressFlag;

    /**
     * 运输方式
     */
    @Schema(description = "运输方式")
    private String shipMethod;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    /**
     * 快递Api配置RefNum
     */
    @Schema(description = "快递Api配置RefNum")
    private String shipApiProfileRefNum;

    /**
     * 最后发货日期
     */
    @Schema(description = "最后发货日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastShipDate;

    /**
     * 保险金额
     */
    @Schema(description = "保险金额")
    private BigDecimal insuranceAmountAmount;

    /**
     * 签名类型
     */
    @Schema(description = "签名类型")
    private String signatureType;

    /**
     * 收货地址名称
     */
    @Schema(description = "收货地址名称")
    private String shipToAddressName;

    /**
     * 收货地址公司
     */
    @Schema(description = "收货地址公司")
    private String shipToAddressCompany;

    /**
     * 收货地址国家
     */
    @Schema(description = "收货地址国家")
    private String shipToAddressCountry;

    /**
     * 收货地址州/省
     */
    @Schema(description = "收货地址州/省")
    private String shipToAddressState;

    /**
     * 收货地址城市
     */
    @Schema(description = "收货地址城市")
    private String shipToAddressCity;

    /**
     * 收货地址邮编
     */
    @Schema(description = "收货地址邮编")
    private String shipToAddressZipCode;

    /**
     * 收货地址1
     */
    @Schema(description = "收货地址1")
    private String shipToAddressAddr1;

    /**
     * 收货地址2
     */
    @Schema(description = "收货地址2")
    private String shipToAddressAddr2;

    /**
     * 收货地址3
     */
    @Schema(description = "收货地址3")
    private String shipToAddressAddr3;

    /**
     * 收货地址邮箱
     */
    @Schema(description = "收货地址邮箱")
    private String shipToAddressEmail;

    /**
     * 收货地址电话
     */
    @Schema(description = "收货地址电话")
    private String shipToAddressPhone;

    /**
     * 收货地址备注
     */
    @Schema(description = "收货地址备注")
    private String shipToAddressNote;


    /**
     * 是否提供运输标签
     */
    @Schema(description = "是否提供运输标签")
    private Boolean provideShippingLabelFlag;


    /**
     * 交易伙伴ID
     */
    @Schema(description = "交易伙伴ID")
    private Long transactionPartnerId;


    @Schema(description = "交易伙伴信息")
    private BasePartnerVO transactionPartnerVO;

    /**
     * 请求状态
     */
    @Schema(description = "请求状态")
    private String otcRequestStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 渠道
     */
    @Schema(description = "渠道")
    private String channel;


    /**
     * 保险金额货币
     */
    @Schema(description = "保险金额类型")
    private String insuranceAmountCurrency;

    /**
     * 发货地址1
     */
    @Schema(description = "发货地址1")
    private String shipFromAddressAddr1;

    /**
     * 发货地址2
     */
    @Schema(description = "发货地址2")
    private String shipFromAddressAddr2;

    /**
     * 发货地址3
     */
    @Schema(description = "发货地址3")
    private String shipFromAddressAddr3;

    /**
     * 发货地址城市
     */
    @Schema(description = "发货地址城市")
    private String shipFromAddressCity;

    /**
     * 发货地址公司
     */
    @Schema(description = "发货地址公司")
    private String shipFromAddressCompany;

    /**
     * 发货地址国家
     */
    @Schema(description = "发货地址国家")
    private String shipFromAddressCountry;

    /**
     * 发货地址邮箱
     */
    @Schema(description = "发货地址邮箱")
    private String shipFromAddressEmail;


    /**
     * 发货地址名称
     */
    @Schema(description = "发货地址名称")
    private String shipFromAddressName;

    /**
     * 发货地址备注
     */
    @Schema(description = "发货地址备注")
    private String shipFromAddressNote;

    /**
     * 发货地址电话
     */
    @Schema(description = "发货地址电话")
    private String shipFromAddressPhone;

    /**
     * 发货地址州/省
     */
    @Schema(description = "发货地址州/省")
    private String shipFromAddressState;

    /**
     * 发货地址邮编
     */
    @Schema(description = "发货地址邮编")
    private String shipFromAddressZipCode;


    @Schema(description = "发货产品详情")
    private List<OtcRequestDetailFullVO> detailList;

    @Schema(description = "包裹信息")
    private List<OtcRequestPackageFullVO> packageList;

    @Schema(description = "是否付保险金")
    private Boolean setPaymentFlag;

    /**
     * 订单类型
     */
    @TableField("order_type")
    private String orderType;

    /**
     * 是否有客户运输要求
     */
    @TableField("has_cus_ship_require")
    private Boolean hasCusShipRequire;

    /**
     * 处理开始时间
     */
    @Schema(description = "处理开始时间")
    private LocalDateTime processStartTime;

    /**
     * 处理完成时间
     */
    @Schema(description = "处理完成时间")
    private LocalDateTime processEndTime;

    /**
     * 计费状态
     */
    @Schema(description = "计费状态")
    private String feeStatus;

}