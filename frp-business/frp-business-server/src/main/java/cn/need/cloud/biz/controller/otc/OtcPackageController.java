package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.converter.otc.OtcPackageConverter;
import cn.need.cloud.biz.model.entity.otc.OtcPackage;
import cn.need.cloud.biz.model.param.otc.create.pkg.OtcMultiBoxPackageWarehouseCreateParam;
import cn.need.cloud.biz.model.param.otc.create.pkg.OtcPackageWarehouseCreateParam;
import cn.need.cloud.biz.model.param.otc.create.pkg.OtcSompPackageWarehouseCreateParam;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageBinLocationQuery;
import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageListQuery;
import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageShippedQuery;
import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageTrackingNumQuery;
import cn.need.cloud.biz.model.vo.otc.page.OtcPackageBinLocationPageVO;
import cn.need.cloud.biz.model.vo.otc.pkg.OtcPackageByTrackingNumVO;
import cn.need.cloud.biz.model.vo.otc.pkg.OtcPackageFullOutputVO;
import cn.need.cloud.biz.model.vo.otc.pkg.OtcPackageVO;
import cn.need.cloud.biz.model.vo.page.OtcPackagePageVO;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageBinLocationService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * OTC包裹 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otc-package")
@Tag(name = "OTC包裹")
public class OtcPackageController extends AbstractRestController<OtcPackageService, OtcPackage, OtcPackageConverter, OtcPackageVO> {

    @Resource
    private OtcPackageBinLocationService otcPackageBinLocationService;

    @Operation(summary = "根据id获取OTC包裹详情", description = "根据数据主键id，从数据库中获取其对应的OTC包裹详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtcPackageVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取OTC包裹详情
        OtcPackageVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取OTC包裹详情", description = "根据数据RefNum，从数据库中获取其对应的OTC包裹详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtcPackageVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取OTC包裹详情
        OtcPackageVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "获取OTC包裹分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC包裹列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtcPackagePageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtcPackageListQuery> search) {

        // 获取OTC包裹分页
        PageData<OtcPackagePageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "包裹发货 markShipped", description = "markShipped")
    @PostMapping(value = "/mark-shipped")
    public Result<Boolean> markSipped(@RequestBody @Validated @Parameter(description = "搜索条件参数", required = true) OtcPackageShippedQuery query) {

        // 返回结果
        return success(service.markSipped(query));
    }

    @Operation(summary = "filterMarkShipped", description = "markShipped")
    @PostMapping(value = "/filter-mark-shipped")
    public Result<Boolean> filterMarkShipped(@RequestBody @Validated @Parameter(description = "列表查询条件参数", required = true) OtcPackageListQuery query) {

        // 返回结果
        return success(service.filterMarkShipped(query));
    }

    @Operation(summary = "包裹-仓库人员创建 MultiBox Package", description = "根据条件，仓库人员创建Package")
    @PostMapping(value = "/multi-box/build-by-warehouse")
    public Result<OtcPackageFullOutputVO> multiBoxBuildByWarehouse(@RequestBody @Valid @Parameter(description = "构建对象", required = true)
                                                                   OtcMultiBoxPackageWarehouseCreateParam warehouseCreateParam) {

        // 返回结果
        return success(service.multiBoxBuildByWarehouse(warehouseCreateParam));
    }

    @Operation(summary = "包裹-仓库人员创建 SOSP Package", description = "根据条件，仓库人员创建Package")
    @PostMapping(value = "/single-order-single-piece/build-by-warehouse")
    public Result<OtcPackageFullOutputVO> sospBuildByWarehouse(@RequestBody @Valid @Parameter(description = "构建对象", required = true)
                                                               OtcPackageWarehouseCreateParam warehouseCreateParam) {

        // 返回结果
        return success(service.buildByWarehouse(warehouseCreateParam));
    }

    @Operation(summary = "包裹-仓库人员创建 SlapAndGo Package", description = "根据条件，仓库人员创建Package")
    @PostMapping(value = "/slap-and-go/build-by-warehouse")
    public Result<OtcPackageFullOutputVO> slapAndGoBuildByWarehouse(@RequestBody @Valid @Parameter(description = "构建对象", required = true)
                                                                    OtcPackageWarehouseCreateParam warehouseCreateParam) {

        // 返回结果
        return success(service.buildByWarehouse(warehouseCreateParam));
    }

    @Operation(summary = "包裹-仓库人员创建 SOMP Package", description = "根据条件，仓库人员创建Package")
    @PostMapping(value = "/single-order-multiple-piece/build-by-warehouse")
    public Result<OtcPackageFullOutputVO> sompBuildByWarehouse(@RequestBody @Valid @Parameter(description = "构建对象", required = true)
                                                               OtcSompPackageWarehouseCreateParam sompWarehouseCreateParam) {

        // 返回结果
        return success(service.sompBuildByWarehouse(sompWarehouseCreateParam));
    }

    @Operation(summary = "标签标记打印", description = "根据传入的搜索条件参数，更新拣货单PrintStatus")
    @PostMapping(value = "/label/mark-printed")
    public Result<Boolean> labelMarkPrinted(@RequestBody @Valid PrintQuery query) {

        // 返回结果
        return success(service.labelMarkPrinted(query));
    }

    @Operation(summary = "获取OTC包裹仓储位置分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC工单仓储位置列表")
    @PostMapping(value = "/bin-location/list")
    public Result<PageData<OtcPackageBinLocationPageVO>> binLocationList(
            @Valid @RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtcPackageBinLocationQuery> search) {

        // 获取OTC工单仓储位置分页
        PageData<OtcPackageBinLocationPageVO> resultPage = otcPackageBinLocationService.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "根据TrackingNum获取OTC包裹", description = "根据TrackingNum获取OTC包裹")
    @PostMapping(value = "/list-by-tracking-num")
    public Result<List<OtcPackageByTrackingNumVO>> detailByTrackingNum(
            @RequestBody @Valid @Parameter(description = "trackingNum", required = true) OtcPackageTrackingNumQuery query) {

        // 获取OTC包裹详情
        List<OtcPackageByTrackingNumVO> detailVo = service.detailByTrackingNum(query);
        // 返回结果
        return success(detailVo);
    }

    // TODO 定时任务获取TrackingNumber
}
