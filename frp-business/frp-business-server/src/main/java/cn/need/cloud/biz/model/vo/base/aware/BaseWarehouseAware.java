package cn.need.cloud.biz.model.vo.base.aware;

import cn.need.cloud.biz.model.vo.base.BaseWarehouseVO;
import cn.need.cloud.biz.util.WarehouseCacheUtil;
import cn.need.framework.common.core.bean.BeanUtil;

import java.util.Optional;

/**
 * 仓库日志感知接口
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@FunctionalInterface
public interface BaseWarehouseAware {

    /**
     * 仓库id
     *
     * @return 仓库id
     */
    Long getWarehouseId();

    /**
     * 产品日志返回实体实现
     *
     * @return 产品日志
     */
    default BaseWarehouseVO getBaseWarehouseVO() {
        return Optional.ofNullable(this.getWarehouseId())
                .map(WarehouseCacheUtil::getById)
                .map(binLocationCache -> BeanUtil.copyNew(binLocationCache, BaseWarehouseVO.class))
                .orElse(null);
    }
}
