package cn.need.cloud.biz.model.vo.otb.shipment;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.BaseWarehouseVO;
import cn.need.cloud.biz.model.vo.otb.pallet.OtbPalletVO;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbPackageVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024年10月28日 15:09:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTB装运信息 vo对象")
public class OtbShipmentInfoVO extends BaseSuperVO {

    /**
     * 包裹列表
     */
    @Schema(description = "包裹列表")
    private List<OtbPackageVO> packageList;
    /**
     * 托盘列表
     */
    @Schema(description = "托盘列表")
    private List<OtbPalletVO> palletList;
    /**
     * 装运详细信息列表
     */
    @Schema(description = "装运详细信息列表")
    private List<OtbShipmentDetailVO> detailList;
    /**
     * 装运状态
     */
    @Schema(description = "装运状态")
    private String otbShipmentStatus;
    /**
     * 装运类型
     */
    @Schema(description = "装运类型")
    private String otbShipmentType;
    /**
     * 请求参考编号
     */
    @Schema(description = "请求参考编号")
    private String requestOfRefNum;
    /**
     * 请求请求参考编号
     */
    @Schema(description = "请求请求参考编号")
    private String requestOfRequestRefNum;
    /**
     * 请求ID
     */
    @Schema(description = "请求ID")
    private Long otbRequestId;
    /**
     * 工作订单参考编号
     */
    @Schema(description = "工作订单参考编号")
    private String workOrderOfRefNum;
    /**
     * 工作订单请求参考编号
     */
    @Schema(description = "工作订单请求参考编号")
    private String workOrderOfRequestRefNum;
    /**
     * 工作订单ID
     */
    @Schema(description = "工作订单ID")
    private Long otbWorkorderId;
    /**
     * 托盘文件状态
     */
    @Schema(description = "托盘文件状态")
    private String palletFileStatus;
    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;
    /**
     * 包裹数量
     */
    @Schema(description = "包裹数量")
    private Integer packageCount;
    /**
     * 托盘数量
     */
    @Schema(description = "托盘数量")
    private Integer palletCount;
    /**
     * 总重量单位
     */
    @Schema(description = "总重量单位")
    private String totalWeightUnit;
    /**
     * 总重量值
     */
    @Schema(description = "总重量值")
    private BigDecimal totalWeightValue;
    /**
     * 体积单位
     */
    @Schema(description = "体积单位")
    private String volumeUnit;
    /**
     * 体积值
     */
    @Schema(description = "体积值")
    private BigDecimal volumeValue;
    /**
     * 拣货单ID
     */
    @Schema(description = "拣货单ID")
    private Long otbPickingSlipId;
    /**
     * 拣货单参考编号
     */
    @Schema(description = "拣货单参考编号")
    private String pickingSlipRefNum;
    /**
     * 参考编号
     */
    @Schema(description = "参考编号")
    private String refNum;
    /**
     * 仓库ID
     */
    @Schema(description = "仓库ID")
    private Long warehouseId;
    /**
     * 基础仓库信息
     */
    @Schema(description = "基础仓库信息")
    private BaseWarehouseVO baseWarehouseVO;
}
