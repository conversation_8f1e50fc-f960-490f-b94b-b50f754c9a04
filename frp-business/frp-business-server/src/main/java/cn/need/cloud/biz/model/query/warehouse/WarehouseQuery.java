package cn.need.cloud.biz.model.query.warehouse;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Set;


/**
 * 仓库基础信息 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "仓库基础信息 query对象")
public class WarehouseQuery extends SuperQuery {

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 仓库编号
     */
    @Schema(description = "仓库编号")
    private String code;

    /**
     * 仓库编号
     */
    @Schema(description = "仓库编号")
    private List<String> codeList;

    /**
     * 仓库名称
     */
    @Schema(description = "仓库名称")
    private String name;

    /**
     * 仓库名称
     */
    @Schema(description = "仓库名称")
    private Set<String> nameList;


    /**
     * 名字
     */
    @Schema(description = "名字")
    private String addressName;

    /**
     * 公司
     */
    @Schema(description = "公司")
    private String addressCompany;

    /**
     * 国家
     */
    @Schema(description = "国家")
    private String addressCountry;

    /**
     * 洲
     */
    @Schema(description = "洲")
    private String addressState;

    /**
     * 城市
     */
    @Schema(description = "城市")
    private String addressCity;

    /**
     * 邮编
     */
    @Schema(description = "邮编")
    private String addressZipCode;

    /**
     * 地址1
     */
    @Schema(description = "地址1")
    private String addressAddr1;

    /**
     * 地址2
     */
    @Schema(description = "地址2")
    private String addressAddr2;

    /**
     * 地址3
     */
    @Schema(description = "地址3")
    private String addressAddr3;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String addressEmail;

    /**
     * 电话
     */
    @Schema(description = "电话")
    private String addressPhone;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String addressNote;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private List<String> refNumList;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 是否是住宅地址
     */
    @Schema(description = "是否是住宅地址")
    private Integer addressIsResidential;

    /**
     * amazonShipProfileShipApiRefNum
     */
    @Schema(description = "amazonShipProfileShipApiRefNum")
    private String amazonShipProfileShipApiRefNum;

    /**
     * amazonShipProfileShipApiRefNum
     */
    @Schema(description = "amazonShipProfileShipApiRefNum集合")
    @Condition(value = Keyword.IN, fields = {"amazonShipProfileShipApiRefNum"})
    private List<String> amazonShipProfileShipApiRefNumList;

    /**
     * fedexShipProfileShipApiRefNum
     */
    @Schema(description = "fedexShipProfileShipApiRefNum")
    private String fedexShipProfileShipApiRefNum;

    /**
     * fedexShipProfileShipApiRefNum
     */
    @Schema(description = "fedexShipProfileShipApiRefNum集合")
    @Condition(value = Keyword.IN, fields = {"fedexShipProfileShipApiRefNum"})
    private List<String> fedexShipProfileShipApiRefNumList;

    /**
     * upsshipProfileShipApiRefNum
     */
    @Schema(description = "upsshipProfileShipApiRefNum")
    private String upsshipProfileShipApiRefNum;

    /**
     * upsshipProfileShipApiRefNum
     */
    @Schema(description = "upsshipProfileShipApiRefNum集合")
    @Condition(value = Keyword.IN, fields = {"upsshipProfileShipApiRefNum"})
    private List<String> upsshipProfileShipApiRefNumList;

    /**
     * 是否有效
     */
    @Schema(description = "是否有效")
    private Boolean activeFlag;

    /**
     * amazonShipPalletProfileShipApiRefNum
     */
    @Schema(description = "amazonShipPalletProfileShipApiRefNum")
    private String amazonShipPalletProfileShipApiRefNum;

    /**
     * amazonShipPalletProfileShipApiRefNum
     */
    @Schema(description = "amazonShipPalletProfileShipApiRefNum集合")
    @Condition(value = Keyword.IN, fields = {"amazonShipPalletProfileShipApiRefNum"})
    private List<String> amazonShipPalletProfileShipApiRefNumList;

    /**
     * 亚马逊仓库编码
     */
    @Schema(description = "亚马逊仓库编码")
    private String amazonWarehouseCode;

    /**
     * ssccprefix
     */
    @Schema(description = "ssccprefix")
    private String ssccprefix;


}