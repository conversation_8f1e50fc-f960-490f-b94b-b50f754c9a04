package cn.need.cloud.biz.service.otb.shipment.impl;

import cn.hutool.core.collection.CollUtil;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.PrintStatusEnum;
import cn.need.cloud.biz.client.constant.enums.base.RequestStatusEnum;
import cn.need.cloud.biz.client.constant.enums.base.ShowLogEnum;
import cn.need.cloud.biz.client.constant.enums.binlocation.BinLocationLogEnum;
import cn.need.cloud.biz.client.constant.enums.feeconfig.FeeStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otb.*;
import cn.need.cloud.biz.converter.otb.OtbShipmentConverter;
import cn.need.cloud.biz.mapper.otb.OtbShipmentMapper;
import cn.need.cloud.biz.model.bo.common.RefTableBO;
import cn.need.cloud.biz.model.bo.otb.OtbBuildShipmentContextBO;
import cn.need.cloud.biz.model.bo.otb.OtbShipmentShipWorkOrderDetailBO;
import cn.need.cloud.biz.model.bo.otb.WaitChannelConfirmContextBO;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.entity.otb.*;
import cn.need.cloud.biz.model.param.otb.create.shipment.OtbShipmentCreateParam;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.otb.shipment.OtbShipmentQuery;
import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.otb.pallet.OtbPalletLabelVO;
import cn.need.cloud.biz.model.vo.otb.pallet.OtbPalletVO;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbPackageVO;
import cn.need.cloud.biz.model.vo.otb.ri.OtbRoutingInstructionVO;
import cn.need.cloud.biz.model.vo.otb.shipment.OtbShipmentDetailVO;
import cn.need.cloud.biz.model.vo.otb.shipment.OtbShipmentVO;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbWorkorderDetailVO;
import cn.need.cloud.biz.model.vo.page.OtbShipmentPageVO;
import cn.need.cloud.biz.service.base.PickingSlipService;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.*;
import cn.need.cloud.biz.service.otb.impl.logutil.OtbWorkorderLogUtil;
import cn.need.cloud.biz.service.otb.pallet.OtbPalletDetailService;
import cn.need.cloud.biz.service.otb.pallet.OtbPalletLabelService;
import cn.need.cloud.biz.service.otb.pallet.OtbPalletService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPickingSlipService;
import cn.need.cloud.biz.service.otb.pkg.OtbPackageDetailService;
import cn.need.cloud.biz.service.otb.pkg.OtbPackageService;
import cn.need.cloud.biz.service.otb.request.OtbRequestService;
import cn.need.cloud.biz.service.otb.ri.OtbRoutingInstructionService;
import cn.need.cloud.biz.service.otb.shipment.OtbShipmentDetailService;
import cn.need.cloud.biz.service.otb.shipment.OtbShipmentService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderBinLocationService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderDetailService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderService;
import cn.need.cloud.biz.util.*;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.NumberUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * OTB发货单服务实现类
 * </p>
 * <p>
 * 该类实现了OTB发货单（Shipment）的核心业务逻辑，负责发货单的创建、查询、状态管理和相关操作。
 * 主要功能包括：
 * 1. 发货单的创建与生成，包括大件（LTL）和小件（Small Parcel）两种类型
 * 2. 发货单的查询功能，支持根据ID、参考编号等多种方式查询
 * 3. 发货单状态的管理，包括状态更新、渠道确认、准备发货等
 * 4. 发货单与工单、请求单、托盘、包装等实体的关联管理
 * 5. 发货单数据的分页查询和详情展示
 * </p>
 * <p>
 * 发货单是物流过程中的重要组成部分，负责将已打包的商品发送给客户。
 * 发货单可以是大件（LTL）或小件（Small Parcel）类型，并且有自己的生命周期状态管理。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtbShipmentServiceImpl extends SuperServiceImpl<OtbShipmentMapper, OtbShipment> implements OtbShipmentService {
    /**
     * OTB托盘服务，用于管理托盘信息和托盘与发货单的关系
     */
    @Resource
    private OtbPalletService otbPalletService;

    /**
     * OTB托盘标签服务，用于管理托盘标签信息
     */
    @Resource
    private OtbPalletLabelService otbPalletLabelService;

    /**
     * OTB托盘详情服务，用于管理托盘详情信息，包括产品、数量等
     */
    @Resource
    private OtbPalletDetailService otbPalletDetailService;

    /**
     * OTB包装服务，用于管理包装信息和包装与发货单的关系
     */
    @Resource
    private OtbPackageService otbPackageService;

    /**
     * OTB发货单详情服务，用于管理发货单详情信息，包括产品、数量等
     * 使用@Lazy注解避免循环依赖
     */
    @Resource
    @Lazy
    private OtbShipmentDetailService otbShipmentDetailService;

    /**
     * OTB工单服务，用于管理工单信息和工单与发货单的关系
     */
    @Resource
    private OtbWorkorderService otbWorkorderService;

    /**
     * OTB请求服务，用于管理请求信息和请求与发货单的关系
     */
    @Resource
    private OtbRequestService otbRequestService;

    /**
     * OTB拣货单服务，用于管理拣货单信息和拣货单与发货单的关系
     */
    @Resource
    private OtbPickingSlipService otbPickingSlipService;

    /**
     * OTB工单详情服务，用于管理工单详情信息，包括产品、数量等
     */
    @Resource
    private OtbWorkorderDetailService otbWorkorderDetailService;

    /**
     * OTB工单库位服务，用于管理工单与库位的关联信息
     */
    @Resource
    private OtbWorkorderBinLocationService otbWorkorderBinLocationService;

    /**
     * 拣货单服务，用于管理拣货单信息和拣货单与库位的关系
     */
    @Resource
    private PickingSlipService pickingSlipService;

    /**
     * OTB路由指令服务，用于管理路由指令信息和路由指令与发货单的关系
     * 使用@Lazy注解避免循环依赖
     */
    @Resource
    @Lazy
    private OtbRoutingInstructionService otbRoutingInstructionService;

    /**
     * OTB包装详情服务，用于管理包装详情信息，包括产品、数量等
     */
    @Resource
    private OtbPackageDetailService otbPackageDetailService;

    /**
     * 根据查询条件获取发货单列表
     * <p>
     * 该方法根据指定的查询条件获取发货单列表，不带分页。
     * 查询条件可以包括发货单状态、发货单类型、工单ID、请求ID等多种条件。
     * </p>
     *
     * @param query 发货单查询条件对象
     * @return 发货单列表，包含发货单基本信息和关联的工单、请求、拣货单等信息
     */
    @Override
    public List<OtbShipmentPageVO> listByQuery(OtbShipmentQuery query) {
        return mapper.listByQuery(query);
    }

    /**
     * 根据查询条件分页获取发货单列表
     * <p>
     * 该方法根据指定的查询条件和分页参数获取发货单列表。
     * 主要流程包括：
     * 1. 根据分页参数创建分页对象
     * 2. 获取查询条件并填充工单ID、请求单ID等关联信息
     * 3. 执行分页查询并获取数据列表
     * 4. 填充仓库信息和工单、请求、拣货单等的参考编号
     * 5. 返回带分页信息的数据列表
     * </p>
     *
     * @param search 包含查询条件和分页参数的对象
     * @return 带分页信息的发货单列表，包含发货单基本信息和关联的工单、请求、拣货单等信息
     * <p>
     * TODO: 考虑使用缓存机制优化参考编号的查询性能
     */
    @Override
    public PageData<OtbShipmentPageVO> pageByQuery(PageSearch<OtbShipmentQuery> search) {
        // 分页参数
        Page<OtbShipment> page = Conditions.page(search, entityClass);
        // 获取查询条件
        OtbShipmentQuery condition = search.getCondition();
        // 填充id
        fillId(condition);
        // 分页列表
        List<OtbShipmentPageVO> dataList = mapper.listByQuery(condition, page);
        // 填充仓库信息
        WarehouseCacheUtil.filledWarehouse(dataList);
        // 填充工单refNum
        fillInfo(dataList);
        // 返回分页列表
        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取发货单详情
     * <p>
     * 该方法根据发货单ID获取发货单的详细信息，包括发货单基本信息、发货单详情、包装信息等。
     * 如果指定ID的发货单不存在，则抛出业务异常。
     * </p>
     *
     * @param id 发货单ID
     * @return 发货单详情VO对象，包含发货单基本信息、详情、包装、托盘信息等
     * @throws BusinessException 如果发货单不存在，则抛出业务异常
     */
    @Override
    public OtbShipmentVO detailById(Long id) {
        OtbShipment entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in OtbShipment");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtbShipment", id));
        }
        return buildOtbShipmentVO(entity);
    }

    /**
     * 根据参考编号获取发货单详情
     * <p>
     * 该方法根据发货单的参考编号（RefNum）获取发货单的详细信息，包括发货单基本信息、发货单详情、包装信息等。
     * 如果指定参考编号的发货单不存在，则抛出业务异常。
     * </p>
     *
     * @param refNum 发货单参考编号
     * @return 发货单详情VO对象，包含发货单基本信息、详情、包装、托盘信息等
     * @throws BusinessException 如果发货单不存在，则抛出业务异常
     */
    @Override
    public OtbShipmentVO detailByRefNum(String refNum) {
        OtbShipment entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("RefNum: " + refNum + " not found in OtbShipment");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtbShipment", "refNum", refNum));
        }
        return buildOtbShipmentVO(entity);
    }

    /**
     * 发货单打印后的处理
     * <p>
     * 该方法用于处理发货单打印后的操作，主要是记录打印操作的日志。
     * 打印操作日志包含打印状态、操作类型和备注信息。
     * </p>
     *
     * @param query 打印查询对象，包含打印状态和备注
     * @param model 发货单实体对象
     */
    @Override
    public void afterPrinted(PrintQuery query, OtbShipment model) {
        OtbShipmentAuditLogHelper.recordWithStatus(
                model,
                StringUtil.concat(ShowLogEnum.PRINT.getStatus(), query.getPrintStatus()),
                BaseTypeLogEnum.OPERATION.getType(),
                query.getNote()
        );
    }

    /**
     * 生成大件发货单（LTL）
     * <p>
     * 该方法用于生成大件发货单（Less Than Truckload，LTL），主要用于处理托盘类型的发货。
     * 主要流程包括：
     * 1. 构建发货单实体对象，设置发货单类型、状态、参考编号等信息
     * 2. 将发货单实体持久化到数据库
     * 3. 将发货单实体加载到上下文中，供后续处理使用
     * 4. 生成发货单详情信息
     * </p>
     * <p>
     * 大件发货单通常包含多个托盘，每个托盘又包含多个包装。
     * 该方法在托盘打包完成后调用，将托盘组织成发货单。
     * </p>
     *
     * @param context 构建发货单的上下文对象，包含托盘、包装、详情等信息
     *                <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                   TODO: 考虑将发货单构建逻辑拆分为更小的方法，提高可维护性
     */
    @Override
    public void generateShipment(OtbBuildShipmentContextBO context) {
        // 构建OtbShipment对象
        OtbShipment otbShipment = buildOtbShipment(context);
        super.insert(otbShipment);
        // 加载到上下文
        context.setOtbShipment(otbShipment);
        // 生成货单明细
        otbShipmentDetailService.generateShipmentDetail(context);
    }

    /**
     * 生成小件发货单（Small Parcel）
     * <p>
     * 该方法用于生成小件发货单（Small Parcel），主要用于处理包装类型的发货。
     * 主要流程包括：
     * 1. 获取创建参数和包装数据
     * 2. 构建小件发货单实体对象，设置发货单类型、状态、参考编号等信息
     * 3. 将发货单实体持久化到数据库
     * 4. 将发货单实体加载到上下文中，供后续处理使用
     * 5. 生成发货单详情信息
     * </p>
     * <p>
     * 小件发货单通常包含一个或多个包装，不包含托盘。
     * 该方法在包装完成后调用，将包装组织成发货单。
     * </p>
     *
     * @param context 构建发货单的上下文对象，包含包装、详情等信息
     *                <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                   TODO: 考虑将小件发货单构建逻辑拆分为更小的方法，提高可维护性
     */
    @Override
    public void generateSmallParcel(OtbBuildShipmentContextBO context) {
        // 获取前端create参数
        OtbShipmentCreateParam param = context.getParam();
        // 获取包裹数据
        OtbPackage otbPackage = context.getOtbPackageList()
                .stream()
                .findFirst()
                .orElse(new OtbPackage());
        OtbShipment otbShipment = buildSmallShipment(context, param, otbPackage);
        // 持久化
        super.insert(otbShipment);
        // 加载到上下文
        context.setOtbShipment(otbShipment);
        // 生成货单明细
        otbShipmentDetailService.generateSmallDetail(context);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void waitChannelConfirm(List<Long> idList) {
        // 创建上下文信息
        WaitChannelConfirmContextBO context = new WaitChannelConfirmContextBO();
        context.setIdList(idList);
        context.setAuditShowLogList(Lists.arrayList());
        loadContext(context);
        // 日志容器
        List<AuditShowLog> auditShowLogList = context.getAuditShowLogList();
        // 分配shipmentQty
        allocateShipmentQty(context);
        // 更新包裹状态
        List<OtbPackage> otbPackageList = otbPackageService.listByShipmentId(idList);
        otbPackageService.updateStatusBatch(otbPackageList, OtbPackageStatusEnum.WAIT_CHANNEL_CONFIRM);
        OtbPackageAuditLogHelper.recordLog(otbPackageList);
        //更新打托单
        List<OtbPallet> otbPalletList = otbPalletService.listByShipmentId(idList);
        otbPalletService.updateStatusBatch(otbPalletList, OtbPalletStatusEnum.WAIT_CHANNEL_CONFIRM);
        OtbPalletAuditLogHelper.recordLog(otbPalletList);
        //获取发货单集合
        List<OtbShipment> otbShipmentList = context.getOtbShipmentList();
        // 获取请求单集合
        List<OtbRequest> otbRequestList = context.getRequestList();
        // 获取工单集合
        List<OtbWorkorder> otbWorkorderList = context.getOtbWorkorderList();
        // 获取工单详情集合
        List<OtbWorkorderDetail> otbWorkorderDetailList = context.getOtbWorkorderDetailList();
        // 持久化发货单
        super.updateBatch(otbShipmentList);
        // 持久化请求单
        otbRequestService.updateBatch(otbRequestList);
        // 持久化工单单
        otbWorkorderService.updateBatch(otbWorkorderList);
        // 持久化工单详情
        otbWorkorderDetailService.updateBatch(otbWorkorderDetailList);
        // 记录日志
        AuditLogHolder.record(auditShowLogList);
    }

    @Override
    public void markProcessingOrReadyToShip(Long otbShipmentId, Long otbPackageId) {
        // 获取发货单
        OtbShipment otbShipment = super.getById(otbShipmentId);
        // 发货单类型校验
        Validate.isTrue(StringUtil.equals(otbShipment.getOtbShipmentType(), OtbShipmentTypeEnum.SMALL_PARCEL.getType()),
                String.format(ErrorMessages.STATUS_REQUIRED, "OtbShipment", "SMALL_PARCEL", otbShipment.getOtbShipmentType()));
        // 发货单状态校验
        if (StringUtil.equals(otbShipment.getOtbShipmentStatus(), OtbShipmentStatusEnum.CHANNEL_CONFIRMED.getStatus())) {
            markProcessing(otbShipment);
        }
        // 校验这个发货单下包裹
        boolean isAnyShipmentNotReadyToShip = otbPackageService.existNotReadyToShip(otbShipment.getId(), otbPackageId);
        if (!isAnyShipmentNotReadyToShip) {
            otbShipment.setOtbShipmentStatus(OtbShipmentStatusEnum.READY_TO_SHIP.getStatus());
            OtbShipmentAuditLogHelper.recordLog(otbShipment);
        }
        // 持久化
        mapper.updateById(otbShipment);
    }

    @Override
    public void markProcessing(OtbShipment otbShipment) {
        // 校验状态
        boolean flag = ObjectUtil.equal(otbShipment.getOtbShipmentStatus(), OtbShipmentStatusEnum.CHANNEL_CONFIRMED.getStatus());
        Validate.isTrue(flag, String.format(ErrorMessages.STATUS_INVALID_OPERATION, "update", "OtbShipment", otbShipment.getOtbShipmentStatus()));
        // 更新发货单状态
        otbShipment.setOtbShipmentStatus(OtbShipmentStatusEnum.PROCESSING.getStatus());
        OtbShipmentAuditLogHelper.recordLog(otbShipment);
        // 更新路径
        OtbRoutingInstruction instruction = otbRoutingInstructionService.getById(otbShipment.getRoutingInstructionId());
        instruction.setOtbRoutingInstructionStatus(OtbRIStatusEnum.PROCESSING.getStatus());
        otbRoutingInstructionService.update(instruction);
        OtbRIAuditLogHelper.recordLog(instruction);
    }

    @Override
    public void markProcessed(OtbShipment otbShipment) {
        // 状态校验
        Validate.isTrue(StringUtil.equals(otbShipment.getOtbShipmentStatus(), OtbShipmentStatusEnum.READY_TO_SHIP.getStatus()),
                String.format(ErrorMessages.STATUS_REQUIRED, "OtbShipment", "READY_TO_SHIP", otbShipment.getOtbShipmentStatus()));
        // 更新发货单状态
        otbShipment.setOtbShipmentStatus(OtbShipmentStatusEnum.SHIPPED.getStatus());
        mapper.updateById(otbShipment);
        // 记录日志
        OtbShipmentAuditLogHelper.recordLog(otbShipment);
        // 请求单下是否存在发货单状态不为Processed
        boolean flag = existShipment(otbShipment.getOtbRequestId(), otbShipment.getId(), otbShipment.getOtbShipmentStatus());
        // 获取工单信息
        OtbWorkorder otbWorkorder = otbWorkorderService.getById(otbShipment.getOtbWorkorderId());
        // 释放ReadyToGo库位库存
        releaseReadyToGoBinLocations(otbShipment, otbWorkorder);
        // 获取请求单数据
        OtbRequest otbRequest = otbRequestService.getById(otbWorkorder.getOtbRequestId());
        // 请求单下是否存在发货单状态不为Processed
        if (!flag) {
            otbRequest.setRequestShipmentStatus(OtbRequestShipmentStatusEnum.CHANNEL_CONFIRMED.getStatus());
            otbWorkorder.setOtbRequestShipmentStatus(OtbRequestShipmentStatusEnum.CHANNEL_CONFIRMED.getStatus());
            // 获取工单详情
            List<OtbWorkorderDetailVO> workorderDetailList = otbWorkorderDetailService.listByOtbWorkorderId(otbWorkorder.getId());
            // 获取NeedAllocateShipmentQty
            List<OtbWorkorderDetailVO> list = workorderDetailList
                    .stream()
                    .filter(item -> !ObjectUtil.equal(item.getQty() - item.getShipmentQty(), DataState.DISABLED))
                    .toList();
            if (ObjectUtil.isEmpty(list)) {
                // 更新状态
                otbWorkorder.setOtbWorkorderStatus(OtbWorkorderEnum.SHIPED.getStatus());
                otbRequest.setOtbRequestStatus(RequestStatusEnum.PROCESSED.getStatus());
                otbRequest.setProcessEndTime(TimeUtils.now());
                otbRequest.setFeeStatus(FeeStatusEnum.NEW.getStatus());
                // 持久化
                otbWorkorderService.update(otbWorkorder);
                otbRequestService.update(otbRequest);
                OtbWorkorderAuditLogHelper.recordLog(otbWorkorder);
                OtbRequestAuditLogHelper.recordLog(otbRequest);
            }
        }
        // OTBRoutingInstruction 标记为处理完
        OtbRoutingInstruction instruction = otbRoutingInstructionService.getById(otbShipment.getRoutingInstructionId());
        instruction.setOtbRoutingInstructionStatus(OtbRIStatusEnum.PROCESSED.getStatus());
        // 持久化
        otbRoutingInstructionService.update(instruction);
        OtbRIAuditLogHelper.recordLog(instruction);
    }

    @Override
    public boolean existShipment(Long otbRequestId, Long id, String otbShipmentStatus) {
        return lambdaQuery()
                .eq(OtbShipment::getOtbRequestId, otbRequestId)
                .ne(OtbShipment::getId, id)
                .ne(OtbShipment::getOtbShipmentStatus, otbShipmentStatus)
                .exists();

    }

    @Override
    public void releaseReadyToGoBinLocations(OtbShipment otbShipment, OtbWorkorder otbWorkorder) {
        // // 不处理 2024-12-03号之后的数据
        // LocalDateTime parse = LocalDateTime.parse("2024-12-03 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        // if (FormatUtil.timeCompare(parse, otbShipment.getCreateTime())) {
        //     return;
        // }
        // 获取发货单详情
        List<OtbShipmentDetailVO> otbShipmentDetailList = otbShipmentDetailService.listByOtbShipmentId(otbShipment.getId());
        // 获取工单详情
        List<OtbWorkorderDetailVO> otbWorkorderDetailList = otbWorkorderDetailService.listByOtbWorkorderId(otbWorkorder.getId());
        // 工单详情容器
        List<OtbShipmentShipWorkOrderDetailBO> detailList = Lists.arrayList();
        // 遍历发货单详情
        for (OtbShipmentDetailVO detail : otbShipmentDetailList) {
            Integer totalShippedQty = detail.getQty();
            for (OtbWorkorderDetailVO otbWorkorderDetailVO : otbWorkorderDetailList) {
                if (totalShippedQty <= 0) {
                    break;
                }

                if (productFlag(detail, otbWorkorderDetailVO)) {
                    continue;
                }

                if (otbWorkorderDetailVO.canShippedQty() <= 0) {
                    continue;
                }

                int currentShippedQty = Math.min(otbWorkorderDetailVO.canShippedQty(), totalShippedQty);

                otbWorkorderDetailVO.allocateShippedQty(currentShippedQty);

                // 释放 WorkOrder 对应的ReadyToGo 的库存
                detailList.add(new OtbShipmentShipWorkOrderDetailBO(otbWorkorderDetailVO, currentShippedQty));
                totalShippedQty -= currentShippedQty;
            }
            // 校验总卸货数量
            Validate.isTrue(ObjectUtil.equal(totalShippedQty, 0), String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Shipment " + otbShipment.getRefNum() + ": The quantity in the shipment details does not match the quantity in the work order details"));
        }
        // 释放 ReadyToGo 库存
        releaseReadyToGoLockedAndInStock(otbWorkorderDetailList, detailList);

    }

    /**
     * 产品信息校验
     *
     * @param detail               发货单详情
     * @param otbWorkorderDetailVO 工单详情
     * @return true or false
     */
    private boolean productFlag(OtbShipmentDetailVO detail, OtbWorkorderDetailVO otbWorkorderDetailVO) {
        return ObjectUtil.notEqual(otbWorkorderDetailVO.getProductId(), detail.getProductId()) ||
                !StringUtil.equals(otbWorkorderDetailVO.getDetailSnapshotProductBarcode(), detail.getProductBarcode()) ||
                !StringUtil.equals(otbWorkorderDetailVO.getDetailSnapshotProductChannelSku(), detail.getProductChannelSku());
    }

    /**
     * 分配发货数量
     *
     * @param context 上下文信息
     */
    private void allocateShipmentQty(WaitChannelConfirmContextBO context) {
        // 获取上下文信息
        List<OtbShipment> otbShipmentList = context.getOtbShipmentList();
        Map<Long, OtbWorkorder> workorderMap = context.getWorkorderMap();
        Map<Long, List<OtbWorkorderDetail>> workorderDetailMap = context.getWorkorderDetailMap();
        Map<Long, List<OtbShipmentDetail>> otbShipmentDetailMap = context.getOtbShipmentDetailMap();
        Map<Long, OtbRequest> requestMap = context.getRequestMap();
        // 工单集合
        List<OtbWorkorder> otbWorkorderList = context.getOtbWorkorderList();
        // 遍历发货单
        otbShipmentList.forEach(item -> {
            // 更新发货单状态
            item.setOtbShipmentStatus(OtbShipmentStatusEnum.WAIT_CHANNEL_CONFIRM.getStatus());
            // 获取工单信息
            OtbWorkorder otbWorkorder = workorderMap.get(item.getOtbWorkorderId());
            Validate.notNull(otbWorkorder, String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtbWorkorder", "for shipment " + item.getRefNum()));
            // 更新状态
            otbWorkorder.setOtbRequestShipmentStatus(OtbRequestShipmentStatusEnum.WAIT_CHANNEL_CONFIRM.getStatus());
            // 获取工单详情
            List<OtbWorkorderDetail> workorderDetailList = workorderDetailMap.get(otbWorkorder.getId());
            // 获取发货单详情
            List<OtbShipmentDetail> shipmentDetailList = otbShipmentDetailMap.get(item.getId());
            // 遍历发货单详情
            shipmentDetailList.forEach(obj -> {
                // 发货数量
                Integer totalShipmentQty = obj.getQty();
                // 遍历工单详情
                for (OtbWorkorderDetail otbWorkorderDetail : workorderDetailList) {
                    if (checkProduct(obj, otbWorkorderDetail)) {
                        continue;
                    }
                    // 可发货数量
                    int canShipmentQty = otbWorkorderDetail.getPackedQty() - otbWorkorderDetail.getShipmentQty();
                    // 可发货数量小于等于0
                    if (canShipmentQty <= 0) {
                        continue;
                    }
                    // 可发货数量小于发货数量
                    int currentShipmentQty = Math.min(canShipmentQty, totalShipmentQty);
                    // 更新发货数量
                    otbWorkorderDetail.setShipmentQty(otbWorkorderDetail.getShipmentQty() + currentShipmentQty);

                    totalShipmentQty -= currentShipmentQty;
                }
                if (totalShipmentQty != 0) {
                    // throw new BusinessException(item.getRefNum() + "/" + obj.getProductBarcode() + "WaitChannelConfirm  AllocateShipmentQty Error");
                    throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Allocation error for shipment " + item.getRefNum() + " with product " + obj.getProductBarcode() + " during channel confirmation"));
                }
            });
            addAuditLog(item, otbWorkorder, requestMap);
        });
        // 更新工单
        setWorkorderStatus(otbWorkorderList, workorderDetailMap);
    }

    /**
     * 校验产品信息
     *
     * @param obj                发货单详情
     * @param otbWorkorderDetail 工单详情
     * @return true or false
     */
    private boolean checkProduct(OtbShipmentDetail obj, OtbWorkorderDetail otbWorkorderDetail) {
        return !ObjectUtil.equal(otbWorkorderDetail.getProductId(), obj.getProductId()) ||
                !StringUtil.equals(otbWorkorderDetail.getDetailSnapshotProductBarcode(), obj.getProductBarcode()) ||
                !StringUtil.equals(otbWorkorderDetail.getDetailSnapshotProductChannelSku(), obj.getProductChannelSku());
    }

    /**
     * 加载上下文
     *
     * @param context 上下文信息
     */
    private void loadContext(WaitChannelConfirmContextBO context) {
        // 获取发货单
        List<OtbShipment> otbShipmentList = super.listByIds(context.getIdList());
        // 判断发货单状态
        List<String> list = otbShipmentList
                .stream()
                .filter(item -> !StringUtil.equals(OtbShipmentStatusEnum.NEW.getStatus(), item.getOtbShipmentStatus()))
                .map(OtbShipment::getRefNum)
                .toList();
        Validate.isTrue(ObjectUtil.isEmpty(list), String.format(ErrorMessages.STATUS_REQUIRED, "OtbShipment", "NEW", JsonUtil.toJson(list)));
        // 获取工单集合
        List<OtbWorkorder> otbWorkorderList = otbWorkorderService.listByIds(otbShipmentList
                .stream()
                .map(OtbShipment::getOtbWorkorderId)
                .toList());
        // 根据工单id映射工单
        Map<Long, OtbWorkorder> workorderMap = ObjectUtil.toMap(otbWorkorderList, OtbWorkorder::getId);
        // 获取工单详情
        List<OtbWorkorderDetail> otbWorkorderDetailList = otbWorkorderDetailService.listItemsByOtbWorkOrderId(otbWorkorderList
                .stream()
                .map(OtbWorkorder::getId)
                .toList());
        // 根据工单详情id
        Map<Long, List<OtbWorkorderDetail>> workorderDetailMap = ObjectUtil.toMapList(otbWorkorderDetailList, OtbWorkorderDetail::getOtbWorkorderId);
        // 获取发货单详情
        List<OtbShipmentDetail> otbShipmentDetailList = otbShipmentDetailService.listByOtbShipmentIds(context.getIdList());
        // 根据发货单id映射发货单详情
        Map<Long, List<OtbShipmentDetail>> otbShipmentDetailMap = ObjectUtil.toMapList(otbShipmentDetailList, OtbShipmentDetail::getOtbShipmentId);
        // 获取请求单集合
        List<OtbRequest> otbRequestList = otbRequestService.listByIds(otbShipmentList
                .stream()
                .map(OtbShipment::getOtbRequestId)
                .toList());
        // 根据请求单id映射请求单
        Map<Long, OtbRequest> requestMap = ObjectUtil.toMap(otbRequestList, OtbRequest::getId);
        // 加载单上下文
        context.setRequestMap(requestMap);
        context.setWorkorderMap(workorderMap);
        context.setOtbShipmentDetailMap(otbShipmentDetailMap);
        context.setWorkorderDetailMap(workorderDetailMap);
        context.setOtbShipmentList(otbShipmentList);
        context.setOtbWorkorderList(otbWorkorderList);
        context.setOtbWorkorderDetailList(otbWorkorderDetailList);
        context.setRequestList(otbRequestList);
    }

    /**
     * 保存auditLog
     *
     * @param item         发货单
     * @param otbWorkorder 工单
     * @param requestMap   请求单映射关系
     */
    private void addAuditLog(OtbShipment item, OtbWorkorder otbWorkorder, Map<Long, OtbRequest> requestMap) {
        // 记录工单日志
        String requestShipmentStatus = "RequestShipmentStatus";
        AuditShowLog auditShowLog = OtbWorkorderLogUtil.getAuditShowLog(otbWorkorder, OtbRequestShipmentStatusEnum.WAIT_CHANNEL_CONFIRM.getStatus(), "", requestShipmentStatus);
        AuditLogHolder.record(auditShowLog);
        // 记录请求单日志
        OtbRequest otbRequest = requestMap.get(item.getOtbRequestId());
        otbRequest.setRequestShipmentStatus(OtbRequestShipmentStatusEnum.WAIT_CHANNEL_CONFIRM.getStatus());
        OtbRequestAuditLogHelper.recordWithStatus(
                otbRequest,
                otbRequest.getRequestShipmentStatus(),
                requestShipmentStatus
        );
        // 记录发货单日志
        OtbShipmentAuditLogHelper.recordLog(
                item,
                OtbRequestShipmentStatusEnum.WAIT_CHANNEL_CONFIRM.getStatus(),
                BaseTypeLogEnum.STATUS.getType(),
                null,
                requestShipmentStatus
        );
    }

    /**
     * 生成小件发货单
     *
     * @param context    上下文信息
     * @param param      前端create参数
     * @param otbPackage 包裹
     * @return OtbShipment对象
     */
    @NotNull
    private OtbShipment buildSmallShipment(OtbBuildShipmentContextBO context, OtbShipmentCreateParam param, OtbPackage otbPackage) {
        // 构建OtbShipment对象
        OtbShipment otbShipment = new OtbShipment();
        // 流水号生成
        otbShipment.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTB_SHIPMENT));
        // 发货单类型
        otbShipment.setOtbShipmentType(param.getOtbShipmentType());
        // 发货单状态
        otbShipment.setOtbShipmentStatus(OtbShipmentStatusEnum.NEW.getStatus());
        // 请求单id
        otbShipment.setOtbRequestId(otbPackage.getOtbRequestId());
        // 工单id
        otbShipment.setOtbWorkorderId(otbPackage.getOtbWorkorderId());
        // 拣货单id
        otbShipment.setOtbPickingSlipId(otbPackage.getOtbPickingSlipId());
        // 打印状态
        otbShipment.setPrintStatus(PrintStatusEnum.NONE.getStatus());
        // 包裹数量
        otbShipment.setPackageCount(context.getOtbPackageList().size());
        // 打托单数量
        otbShipment.setPalletCount(0);
        // PalletFile打印状态
        otbShipment.setPalletFileStatus(OtbPalletFileStatusEnum.NONE.getStatus());
        // 重量单位
        otbShipment.setTotalWeightUnit(otbPackage.getCartonSizeWeightUnit());
        // 仓库id
        otbShipment.setWarehouseId(otbPackage.getWarehouseId());
        // 创建时间
        otbShipment.setCreateTime(TimeUtils.now());
        // 更新时间
        otbShipment.setUpdateTime(TimeUtils.now());
        // 总重量
        context.getOtbPackageList()
                .stream()
                .map(OtbPackage::getCartonSizeWeight)
                .reduce(BigDecimal::add)
                .ifPresent(otbShipment::setTotalWeightValue);
        // 体积单位
        otbShipment.setVolumeUnit(otbPackage.getCartonSizeDimensionUnit());
        // 总体积
        context.getOtbPackageList()
                .stream()
                .map(item -> NumberUtil.mul(item.getCartonSizeLength(), item.getCartonSizeWeight(), item.getCartonSizeHeight()))
                .reduce(BigDecimal::add)
                .ifPresent(otbShipment::setVolumeValue);
        return otbShipment;
    }

    /**
     * 填充refNum
     *
     * @param dataList 发货单列表
     */
    private void fillInfo(List<OtbShipmentPageVO> dataList) {
        // 填充工单refNum
        Set<Long> otbWorkorderIdList = dataList
                .stream()
                .map(OtbShipmentPageVO::getOtbWorkorderId)
                .collect(Collectors.toSet());
        Map<Long, String> workorderRefNumMap = otbWorkorderService.getRefNum(otbWorkorderIdList);
        // 获取请求单refNum
        Set<Long> otbRequestIdList = dataList
                .stream()
                .map(OtbShipmentPageVO::getOtbRequestId)
                .collect(Collectors.toSet());
        Map<Long, OtbRequest> requestMap = otbRequestService.getRefNum(otbRequestIdList);
        // 获取拣货单refNum
        Set<Long> otbPickingSlipIdList = dataList
                .stream()
                .map(OtbShipmentPageVO::getOtbPickingSlipId)
                .collect(Collectors.toSet());
        Map<Long, String> pickingSlipRefNumMap = otbPickingSlipService.getRefNum(otbPickingSlipIdList);
        // 获取shipmentId
        Set<Long> otbShipmentIdList = dataList.stream().map(OtbShipmentPageVO::getId).collect(Collectors.toSet());
        // 获取ri
        Map<Long, OtbRoutingInstructionVO> routingInstructionMap = ObjectUtil.toMap(otbRoutingInstructionService.listByShipmentIdList(otbShipmentIdList), OtbRoutingInstructionVO::getOtbShipmentId);
        // 填充
        dataList.forEach(item -> {
            // 填充工单refNum
            item.setOtbWorkorderRefNum(workorderRefNumMap.get(item.getOtbWorkorderId()));
            // 填充拣货单refNum
            item.setOtbPickingSlipRefNum(pickingSlipRefNumMap.get(item.getOtbPickingSlipId()));
            // 填充请求单refNum
            OtbRequest otbRequest = requestMap.get(item.getOtbRequestId());
            if (ObjectUtil.isNotEmpty(otbRequest)) {
                item.setOtbRequestOfRefNum(otbRequest.getRefNum());
                item.setOtbRequestOfRequestNum(otbRequest.getRequestRefNum());
            }
            // 填充ri
            item.setOtbRoutingInstructionVO(routingInstructionMap.get(item.getId()));
        });
    }

    /**
     * 构建OtbShipment对象
     *
     * @param context 上下文信息
     * @return OtbShipment对象
     */
    private OtbShipment buildOtbShipment(OtbBuildShipmentContextBO context) {
        // 取打托单数据
        OtbPallet otbPallet = context.getOtbPalletList()
                .stream()
                .findFirst()
                .orElse(new OtbPallet());
        // 构建OtbShipment对象
        OtbShipment otbShipment = new OtbShipment();
        // 流水号生成
        otbShipment.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTB_SHIPMENT));
        // 发货单类型
        otbShipment.setOtbShipmentType(OtbShipmentTypeEnum.LTL.getType());
        // 发货单状态
        otbShipment.setOtbShipmentStatus(OtbShipmentStatusEnum.NEW.getStatus());
        // 请求单id
        otbShipment.setOtbRequestId(otbPallet.getOtbRequestId());
        // 工单id
        otbShipment.setOtbWorkorderId(otbPallet.getOtbWorkorderId());
        // 拣货单id
        otbShipment.setOtbPickingSlipId(otbPallet.getOtbPickingSlipId());
        // 打印状态
        otbShipment.setPrintStatus(PrintStatusEnum.NONE.getStatus());
        // 包裹数量
        otbShipment.setPackageCount(context.getOtbPackageList().size());
        // 打托单数量
        otbShipment.setPalletCount(context.getOtbPalletList().size());
        // 重量单位
        otbShipment.setTotalWeightUnit(otbPallet.getPalletSizeWeightUnit());
        // PalletFile打印状态
        otbShipment.setPalletFileStatus(OtbPalletFileStatusEnum.NONE.getStatus());
        // 仓库id
        otbShipment.setWarehouseId(otbPallet.getWarehouseId());
        // 总重量
        context.getOtbPalletList()
                .stream()
                .map(OtbPallet::getPalletSizeWeight)
                .reduce(BigDecimal::add)
                .ifPresent(otbShipment::setTotalWeightValue);
        // 体积单位
        otbShipment.setVolumeUnit(otbPallet.getPalletSizeDimensionUnit());
        // 总体积
        context.getOtbPalletList()
                .stream()
                .map(item -> NumberUtil.mul(item.getPalletSizeLength(), item.getPalletSizeWeight(), item.getPalletSizeHeight()))
                .reduce(BigDecimal::add)
                .ifPresent(otbShipment::setVolumeValue);
        // 返回发货单
        return otbShipment;
    }

    @Override
    public List<DropProVO> countPreDay(OtbShipmentQuery query) {
        return distinctValuePro(query);
    }

    @Override
    public List<DropProVO> distinctValuePro(OtbShipmentQuery query) {
        return DropListUtil.dropProList(
                query.getColumnNameList(),
                OtbShipment.class,
                columnInfos -> mapper.dropProList(columnInfos, query)
        );
    }

    @Override
    public Boolean isAnyNotStatus(Long requestId, Long shipmentId, List<String> status) {
        return lambdaQuery()
                .eq(OtbShipment::getOtbRequestId, requestId)
                .ne(OtbShipment::getId, shipmentId)
                .in(OtbShipment::getOtbShipmentStatus, status)
                .exists();
    }

    /**
     * 构建OTB装运VO对象
     *
     * @param entity OTB装运对象
     * @return 返回包含详细信息的OTB装运VO对象
     */
    private OtbShipmentVO buildOtbShipmentVO(OtbShipment entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的OTB装运VO对象
        OtbShipmentVO otbShipmentVO = Converters.get(OtbShipmentConverter.class).toVO(entity);
        // 填充仓库信息
        WarehouseCacheUtil.filledWarehouse(otbShipmentVO);
        // 填充发货单明细
        List<OtbShipmentDetailVO> otbShipmentDetailList = otbShipmentDetailService.listByOtbShipmentId(otbShipmentVO.getId());
        // 填充产品
        ProductCacheUtil.filledProduct(otbShipmentDetailList);
        otbShipmentVO.setOtbShipmentDetailList(otbShipmentDetailList);
        // 填充包裹
        otbShipmentVO.setOtbPackageList(otbPackageService.listWithLabelByShipmentId(otbShipmentVO.getId()));
        // 填充工单refNum
        otbShipmentVO.setOtbWorkorderRefNum(otbWorkorderService.getRefNum(otbShipmentVO.getOtbWorkorderId()));
        // 填充请求单refNum
        OtbRequest otbRequest = otbRequestService.getRefNum(otbShipmentVO.getOtbRequestId());
        if (ObjectUtil.isNotEmpty(otbRequest)) {
            otbShipmentVO.setOtbRequestOfRefNum(otbRequest.getRefNum());
            otbShipmentVO.setOtbRequestOfRequestRefNum(otbRequest.getRequestRefNum());
        }
        // 填充拣货单refNum
        otbShipmentVO.setOtbPickingSlipRefNum(otbPickingSlipService.getRefNum(otbShipmentVO.getOtbPickingSlipId()));
        // 填充打托单
        fillOtbPalletList(otbShipmentVO);
        // 获取ri
        otbShipmentVO.setRoutingInstructionList(otbRoutingInstructionService.listByShipmentIdList(CollUtil.newHashSet(otbShipmentVO.getId())));
        // 返回发货单详情
        return otbShipmentVO;
    }

    /**
     * 加载上下文信息
     *
     * @param context 上下文信息
     */
    @Override
    public void loadContext(OtbBuildShipmentContextBO context) {
        // 获取前端create参数
        OtbShipmentCreateParam param = context.getParam();
        // 判断是大件小件
        if (StringUtil.equals(OtbShipmentTypeEnum.SMALL_PARCEL.getType(), param.getOtbShipmentType())) {
            // 获取拣货单的所有包裹
            List<OtbPackage> otbPackageList = otbPackageService.listBySsccNum(param.getSsccNumList());
            context.setOtbPackageList(otbPackageList);
            // 获取包裹明细
            List<OtbPackageDetail> otbPackageDetailList = otbPackageDetailService.listByOtbPackageId(otbPackageList
                    .stream()
                    .map(OtbPackage::getId)
                    .toList());
            // 加载到上下文
            context.setOtbPackageDetailList(otbPackageDetailList);
            return;
        }
        // 加载打托单信息
        List<OtbPallet> otbPalletList = otbPalletService.listBySsccNumList(param.getSsccNumList());
        context.setOtbPalletList(otbPalletList);
        // 获取打托单id
        List<Long> otbPalletIdList = otbPalletList
                .stream()
                .map(OtbPallet::getId)
                .toList();
        // 加载打托单详情
        List<OtbPalletDetail> otbPalletDetailList = otbPalletDetailService.listByPalletIdList(otbPalletIdList);
        Validate.notEmpty(otbPalletDetailList, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "otbPalletDetailList"));
        context.setOtbPalletDetailList(otbPalletDetailList);
        // 加载包裹信息
        List<OtbPackage> otbPackageList = otbPackageService.listByPalletIdList(otbPalletIdList);
        Validate.notEmpty(otbPackageList, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "otbPackageList"));
        context.setOtbPackageList(otbPackageList);
    }

    /**
     * 构建打印信息
     *
     * @param context 上下文信息
     * @return OtbShipmentInfoVO对象
     */
    @Override
    public OtbShipmentVO buildShipmentPrintInfo(OtbBuildShipmentContextBO context) {
        // 获取前端create参数
        OtbShipmentVO otbShipmentVO = BeanUtil.copyNew(context.getOtbShipment(), OtbShipmentVO.class);
        // 填充仓库id
        WarehouseCacheUtil.filledWarehouse(otbShipmentVO);
        // 填充发货单明细
        ProductCacheUtil.filledProduct(context.getOtbShipmentDetailList());
        otbShipmentVO.setOtbShipmentDetailList(context.getOtbShipmentDetailList());
        // 填充包裹信息
        otbShipmentVO.setOtbPackageList(BeanUtil.copyNew(context.getOtbPackageList(), OtbPackageVO.class));
        // 填充打托单信息
        otbShipmentVO.setOtbPalletList(BeanUtil.copyNew(context.getOtbPalletList(), OtbPalletVO.class));
        // 填充工单refNum
        otbShipmentVO.setOtbWorkorderRefNum(otbWorkorderService.getRefNum(otbShipmentVO.getOtbWorkorderId()));
        // 填充请求单refNum
        OtbRequest otbRequest = otbRequestService.getRefNum(otbShipmentVO.getOtbRequestId());
        if (ObjectUtil.isNotEmpty(otbRequest)) {
            otbShipmentVO.setOtbRequestOfRefNum(otbRequest.getRefNum());
            otbShipmentVO.setOtbRequestOfRequestRefNum(otbRequest.getRequestRefNum());
        }
        // 填充拣货单refNum
        otbShipmentVO.setOtbPickingSlipRefNum(otbPickingSlipService.getRefNum(otbShipmentVO.getOtbPickingSlipId()));
        // 返回
        return otbShipmentVO;
    }

    @Override
    public List<OtbShipment> listByWorkorderIds(List<Long> workorderIds) {
        if (ObjectUtil.isNotEmpty(workorderIds)) {
            return lambdaQuery().in(OtbShipment::getOtbWorkorderId, workorderIds).list();
        }
        return List.of();
    }

    @Override
    public List<OtbShipment> listByRequestIds(List<Long> requestIds) {
        if (ObjectUtil.isNotEmpty(requestIds)) {
            return lambdaQuery().in(OtbShipment::getOtbRequestId, requestIds).list();
        }
        return List.of();
    }

    /**
     * 填充id
     *
     * @param condition 查询条件
     */
    private void fillId(OtbShipmentQuery condition) {
        Stream<Long> otbPalletStream = otbPalletService.listBySsccNumList(condition.getPalletSsccNumList()).stream().map(OtbPallet::getOtbShipmentId);
        Stream<Long> otbPackageStream = otbPackageService.listBySsccNum(condition.getPackageSsccNumList()).stream().map(OtbPackage::getOtbShipmentId);
        Set<Long> list = Stream.concat(otbPalletStream, otbPackageStream).collect(Collectors.toSet());
        if (ObjectUtil.isNotEmpty(condition.getPalletSsccNumList())) {
            list.add(-1L);
        }
        if (ObjectUtil.isNotEmpty(condition.getPackageSsccNumList())) {
            list.add(-1L);
        }
        condition.setShipmentIdList(list);
        // 工单id
        condition.setOtbWorkorderIdList(otbWorkorderService.listByRefNum(condition.getOtbWorkorderRefNumList()));
        // 请求单id
        condition.setOtbRequestIdList(otbRequestService.listByRequestRefNum(condition.getOtbRequestOfRequestRefNumList()));
    }

    /**
     * 释放ReadyToGo锁和库存
     *
     * @param detailList     详情
     * @param shipDetailList 发货详情
     */
    private void releaseReadyToGoLockedAndInStock(List<OtbWorkorderDetailVO> detailList, List<OtbShipmentShipWorkOrderDetailBO> shipDetailList) {
        // 工单详情id
        List<Long> workOrderDetailIdList = shipDetailList.stream()
                .map(OtbShipmentShipWorkOrderDetailBO::getOtbWorkorderDetailVO)
                .map(BaseSuperVO::getId)
                .distinct()
                .toList();

        // 工单仓储位置
        List<OtbWorkorderBinLocation> workorderBinLocationList = otbWorkorderBinLocationService.listByOtbWorkorderDetailIdList(workOrderDetailIdList);
        List<OtbWorkorder> workorderList
                = otbWorkorderService.listByIds(StreamUtils.distinctMap(detailList, OtbWorkorderDetailVO::getOtbWorkorderId));
        // 工单映射
        Map<Long, OtbWorkorder> workorderMap = StreamUtils.toMap(workorderList, IdModel::getId);

        // 锁释放数量映射
        Map<Long, Integer> releaseDetailQtyMap = shipDetailList.stream()
                // 工单详情id分组
                .collect(Collectors.groupingBy(obj -> obj.getOtbWorkorderDetailVO().getId(),
                        // 累计释放数量
                        Collectors.summingInt(obj -> obj.getOtbWorkorderDetailVO().getShippedQty()))
                );

        // 工单详情LineNum映射
        Map<Long, OtbWorkorderDetailVO> detailMap = detailList.stream()
                .collect(Collectors.toMap(BaseSuperVO::getId, Function.identity()));

        // 释放库位库存和锁
        pickingSlipService.workorderBinLocationReleaseLockedAndReduceInStock(workorderBinLocationList,
                OtbWorkorderBinLocation::getOtbWorkorderDetailId,
                detailId -> releaseDetailQtyMap.getOrDefault(detailId, 0),
                // 仓储位日志获取函数
                (detailId, change) -> {

                    change.setChangeType(BinLocationLogEnum.SHIPMENT_SHIP.getStatus());
                    OtbWorkorderDetailVO detail = detailMap.get(detailId);
                    OtbWorkorder workorder = workorderMap.get(detail.getOtbWorkorderId());

                    // 关联信息
                    RefTableBO refTable = new RefTableBO();
                    refTable.setRefTableId(detailId);
                    refTable.setRefTableRefNum(String.valueOf(detail.getLineNum()));
                    refTable.setRefTableName(OtbWorkorderDetail.class.getSimpleName());

                    refTable.setRefTableShowRefNum(workorder.getRefNum());
                    refTable.setRefTableShowName(OtbWorkorder.class.getSimpleName());
                    change.setRefTable(refTable);
                }
        );
    }

    /**
     * 设置工单状态
     *
     * @param otbWorkorderList   工单
     * @param workorderDetailMap 工单详情
     */
    private void setWorkorderStatus(List<OtbWorkorder> otbWorkorderList, Map<Long, List<OtbWorkorderDetail>> workorderDetailMap) {
        otbWorkorderList.forEach(item -> {
            // 获取工单详情
            List<OtbWorkorderDetail> otbWorkorderDetailList = workorderDetailMap.get(item.getId());
            // 工单详情
            List<OtbWorkorderDetail> detailList = otbWorkorderDetailList
                    .stream()
                    .filter(workOrder -> ObjectUtil.notEqual(workOrder.getQty() - workOrder.getShipmentQty(), 0))
                    .toList();
            // 判空
            if (ObjectUtil.isEmpty(detailList)) {
                otbWorkorderService.checkWithSetStatus(item, OtbWorkorderEnum.WAIT_CHANNEL_CONFIRM);
            }
        });
    }

    /**
     * 填充打托单
     *
     * @param otbShipmentVO 发货单
     */
    private void fillOtbPalletList(OtbShipmentVO otbShipmentVO) {
        List<OtbPalletVO> list = otbPalletService.listByShipmentId(otbShipmentVO.getId());
        List<Long> otbPalletIdList = list.stream().map(OtbPalletVO::getId).toList();
        List<OtbPalletLabel> otbPalletLabelList = otbPalletLabelService.listByOtbPalletId(otbPalletIdList);
        // 根据打托单id映射打托单label
        Map<Long, List<OtbPalletLabel>> labelMap = ObjectUtil.toMapList(otbPalletLabelList, OtbPalletLabel::getOtbPalletId);
        // 遍历打托单集合
        list.forEach(item -> item.setLabelList(BeanUtil.copyNew(labelMap.get(item.getId()), OtbPalletLabelVO.class)));
        otbShipmentVO.setOtbPalletList(list);
    }

}
