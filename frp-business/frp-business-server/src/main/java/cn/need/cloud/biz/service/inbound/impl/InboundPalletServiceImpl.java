package cn.need.cloud.biz.service.inbound.impl;

import cn.hutool.core.collection.ListUtil;
import cn.need.cloud.biz.client.constant.enums.base.PrintStatusEnum;
import cn.need.cloud.biz.client.constant.enums.inbound.InboundPalletEnum;
import cn.need.cloud.biz.client.constant.enums.inbound.InboundPutAwayStatusEnum;
import cn.need.cloud.biz.client.constant.enums.inbound.InboundUnloadStatusEnum;
import cn.need.cloud.biz.client.constant.enums.warehouse.WarehousePrefixEnum;
import cn.need.cloud.biz.converter.inbound.InboundPalletConverter;
import cn.need.cloud.biz.mapper.inbound.InboundPalletMapper;
import cn.need.cloud.biz.model.bo.inbound.InboundPalletContextBO;
import cn.need.cloud.biz.model.entity.inbound.InboundPallet;
import cn.need.cloud.biz.model.entity.inbound.InboundPutawaySlip;
import cn.need.cloud.biz.model.entity.inbound.InboundUnload;
import cn.need.cloud.biz.model.entity.inbound.InboundWorkorder;
import cn.need.cloud.biz.model.query.inbound.InboundPalletQuery;
import cn.need.cloud.biz.model.vo.inbound.pallet.InboundPalletDetailVO;
import cn.need.cloud.biz.model.vo.inbound.pallet.InboundPalletUnloadPrintVO;
import cn.need.cloud.biz.model.vo.inbound.pallet.InboundPalletUnloadVO;
import cn.need.cloud.biz.model.vo.inbound.pallet.InboundPalletVO;
import cn.need.cloud.biz.model.vo.inbound.workorder.InBoundWorkOrderInfoVO;
import cn.need.cloud.biz.model.vo.page.InboundPalletPageVO;
import cn.need.cloud.biz.service.binlocation.BinLocationService;
import cn.need.cloud.biz.service.helper.auditshowlog.inbound.InboundPalletAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.inbound.InboundPutAwayAuditLogHelper;
import cn.need.cloud.biz.service.inbound.*;
import cn.need.cloud.biz.util.BinLocationCacheUtil;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.biz.util.ProductVersionCacheUtil;
import cn.need.cloud.biz.util.WarehouseCacheUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.cloud.upms.cache.TenantCacheService;
import cn.need.cloud.upms.cache.bean.TenantCache;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.session.Users;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.compress.utils.Lists;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Clock;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 入库单打托服务实现类
 * </p>
 * <p>
 * 该类实现了入库托盘相关的业务逻辑，包括托盘的创建、查询、打印等功能。
 * 打托（Palletization）是指将多个货物按照一定规则组合成托盘单元的过程，
 * 便于仓库管理和货物移动。本服务管理入库过程中的托盘操作。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class InboundPalletServiceImpl extends SuperServiceImpl<InboundPalletMapper, InboundPallet> implements InboundPalletService {

    /**
     * 入库托盘详情服务，用于管理托盘中的详细货品信息
     */
    @Resource
    private InboundPalletDetailService inboundPalletDetailService;

    /**
     * 入库卸货服务，使用懒加载避免循环依赖
     */
    @Resource
    @Lazy
    private InboundUnloadService inboundUnloadService;

    /**
     * 入库工单服务，用于获取和管理入库工单信息
     */
    @Resource
    private InboundWorkorderService inboundWorkorderService;

    /**
     * 库位服务，用于管理仓库中的库位信息
     */
    @Resource
    private BinLocationService binLocationService;

    /**
     * 租户缓存服务，用于获取租户相关信息
     */
    @Resource
    private TenantCacheService tenantCacheService;

    /**
     * 入库上架单服务，使用懒加载避免循环依赖
     */
    @Resource
    @Lazy
    private InboundPutawaySlipService inboundPutawaySlipService;


    /**
     * 根据查询条件获取入库托盘列表
     * <p>
     * 该方法用于按照指定的查询条件查询入库托盘列表。
     * </p>
     *
     * @param query 查询条件
     * @return 入库托盘分页视图对象列表
     */
    @Override
    public List<InboundPalletPageVO> listByQuery(InboundPalletQuery query) {
        return mapper.listByQuery(query);
    }

    /**
     * 根据查询条件分页获取入库托盘
     * <p>
     * 该方法按照指定的查询条件和分页参数查询入库托盘列表。
     * 查询结果会填充托盘详情、工单信息、箱数和产品数等计算值，以及相关的仓库、产品和库位信息。
     * </p>
     *
     * @param search 包含查询条件和分页参数的搜索对象
     * @return 包含分页信息的入库托盘分页视图对象
     * <p>
     * TODO: 代码中有一个标记为需要改进的注释（改为mapper inner join方式），建议实现此优化
     * 优化建议：使用数据库JOIN操作替代代码中的多次查询和数据组装，提高查询效率
     */
    @Override
    public PageData<InboundPalletPageVO> pageByQuery(PageSearch<InboundPalletQuery> search) {
        //获取分页对象
        Page<InboundPallet> page = Conditions.page(search, entityClass);
        //查询条件对象
        InboundPalletQuery condition = search.getCondition();
        //todo: 改为mapper inner join 方式
        //填充查询id
        fillQueryId(condition);
        //获取打托单分页列表
        List<InboundPalletPageVO> dataList = mapper.listByQuery(condition, page);
        //获取打托明细
        inboundPalletDetailService.fillPalletDetail(dataList);
        //填充工单信息
        fillInboundWorkOrderInfo(dataList);
        //计算箱数以及产品数
        dataList.forEach(item -> {
            //箱数
            item.setCartonCount(item.getCartonPerLayer() * item.getLayersCount() + item.getExtCarton());
            //产品数
            item.setPcsCount(item.getCartonCount() * item.getPcsPerCarton());
        });
        //填充仓库信息
        WarehouseCacheUtil.filledWarehouse(dataList);
        //填充产品信息
        ProductVersionCacheUtil.filledProductVersion(dataList);
        //填充库位信息
        BinLocationCacheUtil.filledBinLocation(dataList);
        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取入库托盘详情
     * <p>
     * 该方法用于获取指定ID的入库托盘详细信息，包括其关联的详情信息。
     * </p>
     *
     * @param id 入库托盘ID
     * @return 入库托盘视图对象，包含详细信息
     * @throws BusinessException 如果找不到指定ID的入库托盘，则抛出业务异常
     */
    @Override
    public InboundPalletVO detailById(Long id) {
        InboundPallet entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in InboundPallet");
        }
        return buildInboundPalletVO(entity);
    }

    /**
     * 根据参考编号获取入库托盘详情
     * <p>
     * 该方法用于获取指定参考编号的入库托盘详细信息，包括其关联的详情信息。
     * </p>
     *
     * @param refNum 入库托盘参考编号
     * @return 入库托盘视图对象，包含详细信息
     * @throws BusinessException 如果找不到指定参考编号的入库托盘，则抛出业务异常
     *                           <p>
     *                                                                                                                                                                                       TODO: 方法中调用了未定义的getByRefNum方法，需要补充实现
     *                                                                                                                                                                                       优化建议：实现getByRefNum方法或使用Lambda查询
     */
    @Override
    public InboundPalletVO detailByRefNum(String refNum) {
        InboundPallet entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("RefNum: " + refNum + " not found in InboundPallet");
        }
        return buildInboundPalletVO(entity);
    }

    /**
     * 生成入库托盘
     * <p>
     * 该方法用于根据提供的参数生成新的入库托盘记录。
     * 包括创建托盘基本信息、添加托盘详情、记录审计日志等步骤。
     * </p>
     *
     * @param inboundPalletUnloadVO 入库托盘卸货视图对象，包含托盘基本信息
     * @param inboundUnloadList     入库卸货列表，包含要加入托盘的卸货信息
     * @param inboundPutawaySlip    入库上架单，关联托盘的上架流程
     * @return 入库托盘卸货打印视图对象，用于托盘标签打印
     * <p>
     * TODO: 方法中没有对inboundPalletUnloadVO的字段进行验证
     * 优化建议：添加对必填字段的验证逻辑
     */
    @Override
    public InboundPalletUnloadPrintVO generatePallet(InboundPalletUnloadVO inboundPalletUnloadVO, List<InboundUnload> inboundUnloadList, InboundPutawaySlip inboundPutawaySlip) {
        //生成打托单
        InboundPallet inboundPallet = BeanUtil.copyNew(inboundPalletUnloadVO, InboundPallet.class);
        //填充id
        inboundPallet.setId(IdWorker.getId());
        //创建时间
        inboundPallet.setCreateTime(ZonedDateTime.now(Clock.systemUTC()).toLocalDateTime());
        //填充初始状态
        inboundPallet.setPalletStatus(InboundPalletEnum.NEW.getStatus());
        //填充仓库id
        inboundPallet.setWarehouseId(WarehouseContextHolder.getWarehouseId());
        //填充refNum
        inboundPallet.setRefNum(FormatUtil.substringAfter(RefNumTypeEnum.INBOUND_PALLET.getCode(), WarehouseContextHolder.getWarehouseId(), WarehousePrefixEnum.PREFIX.getPrefix()));
        //填充打印状态
        inboundPallet.setPrintStatus(PrintStatusEnum.NONE.getStatus());
        //填充当前用户id
        inboundPallet.setCreateBy(Objects.requireNonNull(Users.getUser()).getId());
        //持久化打托单
        super.insert(inboundPallet);
        //生成打托详情单
        List<InboundPalletDetailVO> inboundPalletDetailList = inboundPalletDetailService.generatePalletDetail(inboundUnloadList, inboundPutawaySlip.getId(), inboundPallet);
        //保存日志
        InboundPalletAuditLogHelper.recordLog(
                inboundPallet,
                StringUtil.format("Create by {}", inboundPutawaySlip.getRefNum()),
                null

        );
        //返回打印数据
        return buildInboundPalletUnloadPrintVO(inboundPallet, inboundPalletDetailList);
    }

    /**
     * 执行入库打托操作
     * <p>
     * 该方法在事务中执行，确保数据一致性。打托操作会进行以下步骤：
     * 1. 创建上下文并初始化数据
     * 2. 验证数据有效性
     * 3. 确定打托策略
     * 4. 生成托盘并创建详情记录
     * 5. 更新关联的卸货单信息
     * 6. 记录审计日志
     * 7. 根据情况更新上架单状态
     * </p>
     *
     * @param param 入库托盘卸货视图对象，包含打托所需参数
     * @return 入库托盘卸货打印视图对象，用于托盘标签打印
     * @throws BusinessException 如果验证失败或处理过程中出错，则抛出业务异常
     *                           <p>
     *                                                                                                                                                                                       TODO: 方法中的事务处理缺少回滚机制，如果部分操作成功部分失败可能导致数据不一致
     *                                                                                                                                                                                       优化建议：添加更细粒度的事务控制和异常处理，确保数据一致性
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public InboundPalletUnloadPrintVO pallet(InboundPalletUnloadVO param) {
        //创建打托上下文信息
        InboundPalletContextBO context = new InboundPalletContextBO();
        context.setParam(param);
        //初始化数据
        List<InboundUnload> inboundUnloadList = inboundUnloadService.listByPutAwaySlipId(param.getPutAwaySlipId());
        context.setInboundUnloadList(inboundUnloadList);
        Validate.notEmpty(inboundUnloadList, "unload is null");
        //数据校验
        dataValid(context);
        //判断是否为第一次打托
        boolean flag = inboundUnloadList
                .stream()
                .filter(item -> !StringUtil.equals(item.getInboundUnloadStatus(), InboundUnloadStatusEnum.CANCEL.getStatus()))
                .anyMatch(item -> ObjectUtil.notEqual(item.getPalletQty() + item.getRegularPutawayQty(), 0));
        //打托策略
        palletStrategy(context);
        //填充工单id
        Long inboundWorkorderId = inboundUnloadList
                .stream()
                .findFirst()
                .orElse(new InboundUnload())
                .getInboundWorkorderId();
        param.setInBoundWorkOrderId(inboundWorkorderId);
        //获取工单id
        InboundWorkorder inboundWorkorder = inboundWorkorderService.getById(inboundWorkorderId);
        //获取上架单
        InboundPutawaySlip inboundPutawaySlip = inboundPutawaySlipService.getById(param.getPutAwaySlipId());
        //生成打托单
        InboundPalletUnloadPrintVO inboundPalletUnloadPrintVO = generatePallet(param, context.getInboundUnloadPalletList(), inboundPutawaySlip);
        //更新卸货单
        inboundUnloadService.updateUnloadQty(inboundUnloadList);
        //上架单打托日志记录
        InboundPutAwayAuditLogHelper.recordWithPallet(param, inboundPalletUnloadPrintVO, inboundPutawaySlip, inboundWorkorder);
        if (!flag) {
            //更新上架单
            inboundPutawaySlipService.updateStatus(Collections.singleton(param.getPutAwaySlipId()), InboundPutAwayStatusEnum.PROCESSING.getStatus());
        }
        return inboundPalletUnloadPrintVO;
    }

    /**
     * 设置打托策略
     * <p>
     * 该方法用于确定具体的打托策略，根据卸货单信息和前端传入的参数来计算每个卸货单应分配的打托数量。
     * 优先分配给未到达满额的卸货单，如果一个卸货单不足以满足当前打托需求，则继续分配给下一个卸货单。
     * </p>
     *
     * @param context 打托上下文，包含打托参数和卸货单集合
     *                <p>
     *                                                                                                          TODO: 该方法逻辑较复杂，建议添加更详细的注释说明每个条件判断的业务含义
     *                                                                                                          优化建议：考虑将计算逻辑抽取为独立的方法，提高代码可读性
     */
    private void palletStrategy(InboundPalletContextBO context) {
        //卸货单集合
        List<InboundUnload> inboundUnloadList = context.getInboundUnloadList();
        //前端打托参数
        InboundPalletUnloadVO param = context.getParam();
        //过滤AllAssigned状态的卸货单
        List<InboundUnload> list = inboundUnloadList.stream()
                .filter(inboundUnload -> ObjectUtil.notEqual(inboundUnload.getInboundUnloadStatus(), InboundUnloadStatusEnum.ALL_ASSIGNED.getStatus()))
                .toList();
        //卸货单容器
        List<InboundUnload> unloadList = Lists.newArrayList();
        // 获取打托数量
        int palletQty = getPalletQty(param);
        // 填充打托数量
        for (InboundUnload inboundUnload : list) {
            InboundUnload unload = BeanUtil.copyNew(inboundUnload, InboundUnload.class);
            unloadList.add(unload);
            //当前打托数量+常规上架数量+已打托数量小于卸货数量
            if (inboundUnload.getPalletQty() + inboundUnload.getRegularPutawayQty() + palletQty <= inboundUnload.getQty()) {
                inboundUnload.setPalletQty(inboundUnload.getPalletQty() + palletQty);
                unload.setPalletQty(palletQty);
                break;
            }
            //当前打托数量+常规上架数量+已打托数量大于卸货数量
            if (inboundUnload.getPalletQty() + inboundUnload.getRegularPutawayQty() + palletQty >= inboundUnload.getQty()) {
                //该卸货单可打托数量
                int remainQty = inboundUnload.getQty() - (inboundUnload.getPalletQty() + inboundUnload.getRegularPutawayQty());
                //扣减打托数量
                palletQty -= remainQty;
                //添加已打托数量
                inboundUnload.setPalletQty(inboundUnload.getPalletQty() + remainQty);
                unload.setPalletQty(remainQty);
            }
        }
        //加载到上下文
        context.setInboundUnloadPalletList(unloadList);
    }


    /**
     * 数据校验
     * <p>
     * 该方法用于验证打托操作的数据是否有效，主要检查打托数量是否超过可用的卸货数量。
     * 如果验证失败，将抛出异常阻止打托操作继续进行。
     * </p>
     *
     * @param context 打托上下文，包含打托参数和卸货单集合
     * @throws BusinessException 如果打托数量超过可用卸货数量，则抛出业务异常
     *                           <p>
     *                                                                                                                                                                                       TODO: 当前校验比较简单，只检查了数量，可以添加更多业务规则校验
     *                                                                                                                                                                                       优化建议：增加对打托参数有效性的校验，如每层箱数、每箱产品数等
     */
    private void dataValid(InboundPalletContextBO context) {
        //前端参数
        InboundPalletUnloadVO param = context.getParam();
        //卸货单集合
        List<InboundUnload> inboundUnloadList = context.getInboundUnloadList();
        //获取可打托数量
        int sum = inboundUnloadList.stream()
                .filter(this::filterFlag)
                .mapToInt(item -> item.getQty() - item.getPalletQty() - item.getRegularPutawayQty())
                .sum();
        //打托数量
        int palletQty = getPalletQty(param);
        //校验打托数量是否超过卸货数量
        Validate.isTrue(palletQty <= sum, "pallet number more than unload number");

    }

    /**
     * 过滤已分配及完成状态卸货单
     * <p>
     * 该方法用于判断卸货单是否处于可打托状态，过滤掉已完全分配或已处理完成的卸货单。
     * </p>
     *
     * @param inboundUnload 卸货单
     * @return 如果卸货单可以用于打托操作返回true，否则返回false
     * <p>
     * TODO: 该方法的逻辑运算符使用不当，OR操作应该用||而不是|，需要修正
     * 优化建议：修正逻辑运算符，并考虑使用枚举值的equals方法代替notEqual判断
     */
    private boolean filterFlag(InboundUnload inboundUnload) {
        return ObjectUtil.notEqual(inboundUnload.getInboundUnloadStatus(), InboundUnloadStatusEnum.ALL_ASSIGNED.getStatus()) ||
                ObjectUtil.notEqual(inboundUnload.getInboundUnloadStatus(), InboundUnloadStatusEnum.PROCESSED.getStatus());
    }

    /**
     * 计算打托数量
     * <p>
     * 该方法用于根据打托参数计算总的打托数量。计算公式为：
     * 打托数量 = (层数 * 每层箱数 + 额外箱数) * 每箱产品数
     * </p>
     *
     * @param inboundPalletUnloadVO 打托信息，包含层数、每层箱数、额外箱数和每箱产品数
     * @return 计算得到的打托总数量
     * <p>
     * TODO: 方法未对参数进行空值检查，可能导致空指针异常
     * 优化建议：添加参数非空检查，使用Optional或提供默认值
     */
    private int getPalletQty(InboundPalletUnloadVO inboundPalletUnloadVO) {
        //一共多少层
        Integer layersCount = inboundPalletUnloadVO.getLayersCount();
        //每层几个箱子
        Integer cartonPerLayer = inboundPalletUnloadVO.getCartonPerLayer();
        //多了几个箱子
        Integer extCarton = inboundPalletUnloadVO.getExtCarton();
        //每箱几个产品
        Integer pcsPerCarton = inboundPalletUnloadVO.getPcsPerCarton();
        //计算打托数量
        return (layersCount * cartonPerLayer + extCarton) * pcsPerCarton;
    }

    /**
     * 构建入库单打托VO对象
     * <p>
     * 该方法用于将入库托盘实体对象转换为视图对象，并计算箱数和产品数等衍生字段。
     * 同时填充相关的产品信息，使返回的视图对象更加完整。
     * </p>
     *
     * @param entity 入库单打托对象
     * @return 返回包含详细信息的入库单打托VO对象
     * <p>
     * TODO: 方法中的计算逻辑与getPalletQty方法重复，考虑合并
     * 优化建议：提取公共计算逻辑，避免代码重复
     */
    private InboundPalletVO buildInboundPalletVO(InboundPallet entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 转成vo对象
        InboundPalletVO inboundPalletVO = Converters.get(InboundPalletConverter.class).toVO(entity);
        // 每层几个箱子
        Integer cartonPerLayer = inboundPalletVO.getCartonPerLayer();
        // 一共多少层
        Integer layersCount = inboundPalletVO.getLayersCount();
        // 一箱多少产品
        Integer pcsPerCarton = inboundPalletVO.getPcsPerCarton();
        // 多出来的箱数
        Integer extCarton = inboundPalletVO.getExtCarton();
        // 箱数
        inboundPalletVO.setCartonCount(layersCount * cartonPerLayer + extCarton);
        // 产品数
        inboundPalletVO.setPcsCount(pcsPerCarton * inboundPalletVO.getCartonCount());
        // 填充产品信息
        ProductVersionCacheUtil.filledProductVersion(inboundPalletVO);
        // 返回包含详细信息的入库单打托VO对象
        return inboundPalletVO;
    }

    /**
     * 填充查询工单id
     * <p>
     * 该方法用于填充查询条件中的工单ID列表和库位ID列表，以便进行关联查询。
     * </p>
     *
     * @param condition 查询条件对象
     *                  <p>
     *                                                                                                                        TODO: 方法没有考虑服务调用失败的情况
     *                                                                                                                        优化建议：添加异常处理和日志记录，确保调用其他服务获取ID时的稳定性
     */
    private void fillQueryId(InboundPalletQuery condition) {
        //获取工单id
        condition.setInboundWorkOrderIdList(inboundWorkorderService.getInboundWorkOrderIds(condition));
        //填充库位id
        condition.setBinLocationIdList(binLocationService.getBinLocationIds(condition));
    }

    /**
     * 填充工单信息
     * <p>
     * 该方法用于为打托列表中的每个项目填充相关的工单信息，包括工单编号、请求单编号和交易伙伴信息等。
     * 通过批量查询工单和交易伙伴信息，减少数据库访问次数，提高效率。
     * </p>
     *
     * @param dataList 打托列表
     *                 <p>
     *                                                                                                                 TODO: 当找不到对应的工单信息时，直接返回而不是设置默认值或错误标记
     *                                                                                                                 优化建议：为找不到的工单添加默认值或错误标记，提高用户体验
     */
    private void fillInboundWorkOrderInfo(List<InboundPalletPageVO> dataList) {
        //获取打托详情
        List<InboundPalletDetailVO> list = dataList
                .stream()
                .map(InboundPalletPageVO::getDetails)
                .flatMap(List::stream)
                .toList();
        //获取工单id
        Set<Long> workOrderIdList = list.stream()
                .map(InboundPalletDetailVO::getInBoundWorkOrderId)
                .collect(Collectors.toSet());
        //获取工单信息
        List<InboundWorkorder> inboundWorkOrderList = inboundWorkorderService.listByIds(workOrderIdList);
        Map<Long, InboundWorkorder> workorderMap = ObjectUtil.toMap(inboundWorkOrderList, InboundWorkorder::getId);
        //填充工单信息
        list.forEach(item -> {
            //获取入库工单信息
            InboundWorkorder inboundWorkorder = workorderMap.get(item.getInBoundWorkOrderId());
            //判空
            if (ObjectUtil.isEmpty(inboundWorkorder)) {
                return;
            }
            //转换为工单信息对象
            InBoundWorkOrderInfoVO inBoundWorkOrderInfoVO = BeanUtil.copyNew(inboundWorkorder, InBoundWorkOrderInfoVO.class);
            //填充入库请求单requestRefNum
            inBoundWorkOrderInfoVO.setRequestOfRequestRefNum(inboundWorkorder.getRequestSnapshotRequestRefNum());
            //填充入库工单refNum
            inBoundWorkOrderInfoVO.setWorkOrderRefNum(inboundWorkorder.getRefNum());
            //填充入库请求单refNum
            inBoundWorkOrderInfoVO.setRequestRefNum(inboundWorkorder.getRequestSnapshotRefNum());
            //获取合作伙伴
            TenantCache tenantCache = ObjectUtil.nullToDefault(tenantCacheService.getById(inboundWorkorder.getTransactionPartnerId()), new TenantCache());
            //填充合作伙伴名称
            inBoundWorkOrderInfoVO.setTransactionPartnerName(tenantCache.getName());
            //填充入库工单信息
            item.setInBoundWorkOrder(inBoundWorkOrderInfoVO);
        });
    }

    /**
     * 构建打托返回打印数据
     * <p>
     * 该方法用于构建用于打印的托盘信息视图对象，填充详情、仓库、库位和产品信息，
     * 使打印数据更加完整和准确。
     * </p>
     *
     * @param inboundPallet           打托对象
     * @param inboundPalletDetailList 打托详情集合
     * @return 打印打托数据
     * <p>
     * TODO: 方法没有处理相关缓存服务不可用的情况
     * 优化建议：添加缓存服务调用的异常处理，提供降级策略
     */
    private InboundPalletUnloadPrintVO buildInboundPalletUnloadPrintVO(InboundPallet inboundPallet, List<InboundPalletDetailVO> inboundPalletDetailList) {
        //转化为InboundPalletUnloadPrintVO对象
        InboundPalletUnloadPrintVO inboundPalletUnloadPrintVO = BeanUtil.copyNew(inboundPallet, InboundPalletUnloadPrintVO.class);
        //填充详情
        inboundPalletUnloadPrintVO.setDetails(inboundPalletDetailList);
        //仓库信息填充
        WarehouseCacheUtil.filledWarehouse(inboundPalletUnloadPrintVO);
        //库位信息填充
        BinLocationCacheUtil.filledBinLocation(inboundPalletUnloadPrintVO);
        //产品信息填充
        ProductVersionCacheUtil.filledProductVersion(inboundPalletUnloadPrintVO);
        //返回打托信息
        return inboundPalletUnloadPrintVO;
    }

    @Override
    public List<InboundPallet> listByWorkorderId(Long workorderId) {
        if (ObjectUtil.isEmpty(workorderId)) {
            return ListUtil.empty();
        }
        return lambdaQuery()
                .eq(InboundPallet::getInBoundWorkOrderId, workorderId)
                .list();
    }

}
