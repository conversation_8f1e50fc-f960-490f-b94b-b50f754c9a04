package cn.need.cloud.biz.model.vo.product;


import cn.need.cloud.biz.model.vo.base.BaseFullProductVO;
import cn.need.cloud.biz.model.vo.base.BaseFullProductVersionVO;
import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import cn.need.cloud.biz.model.vo.base.BaseProductVersionVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@Schema(description = "ProductTestVO")
public class ProductTestVO implements Serializable {


    @Serial
    private static final long serialVersionUID = 2690994417706828966L;


    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 产品id
     */
    @Schema(description = "产品版本id")
    private Long productId;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    private BaseProductVO baseProductVO;

    private BaseFullProductVO baseFullProductVO;

    private BaseFullProductVersionVO baseFullProductVersionVO;

    private BaseProductVersionVO productVersionVO;


}
