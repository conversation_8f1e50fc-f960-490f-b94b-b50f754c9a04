package cn.need.cloud.biz.service.otc.pickingslip.impl;

import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.ProcessType;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPrepPickingSlipStatusEnum;
import cn.need.cloud.biz.client.constant.pickingslip.PickingSlipLogConstant;
import cn.need.cloud.biz.converter.otc.OtcPrepPickingSlipConverter;
import cn.need.cloud.biz.mapper.otc.OtcPrepPickingSlipMapper;
import cn.need.cloud.biz.model.bo.common.WorkOrderMembershipBO;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPickingSlip;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPickingSlipDetail;
import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorder;
import cn.need.cloud.biz.model.query.otc.pickingslip.prep.OtcPrepPickingSlipListQuery;
import cn.need.cloud.biz.model.query.otc.pickingslip.prep.OtcPrepPickingSlipProductPickQuery;
import cn.need.cloud.biz.model.query.otc.pickingslip.prep.OtcPrepPickingSlipQuery;
import cn.need.cloud.biz.model.query.product.ProductTreeQuery;
import cn.need.cloud.biz.model.vo.base.BasePartnerVO;
import cn.need.cloud.biz.model.vo.base.BasePickVO;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.base.product.PrepFullProductVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPrepPickingSlipDetailPickVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPrepPickingSlipDetailVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPrepPickingSlipPickContextVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPrepPickingSlipVO;
import cn.need.cloud.biz.model.vo.page.OtcPrepPickingSlipPageVO;
import cn.need.cloud.biz.service.base.PickingSlipService;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcPrepPickingSlipAuditLogHelper;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipDetailService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipService;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderDetailService;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderService;
import cn.need.cloud.biz.util.DropListUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.biz.util.log.AuditLogUtil;
import cn.need.cloud.upms.cache.TenantCacheService;
import cn.need.cloud.upms.cache.bean.TenantCache;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;


/**
 * <p>
 * OTC预提货单 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtcPrepPickingSlipServiceImpl extends SuperServiceImpl<OtcPrepPickingSlipMapper, OtcPrepPickingSlip> implements OtcPrepPickingSlipService {

    @Resource
    private OtcPickingSlipService otcPickingSlipService;
    @Resource
    private OtcPrepPickingSlipDetailService otcPrepPickingSlipDetailService;
    @Resource
    @Lazy
    private OtcPrepWorkorderService otcPrepWorkorderService;
    @Resource
    private OtcPrepWorkorderDetailService otcPrepWorkorderDetailService;
    @Resource
    private PickingSlipService pickingSlipService;
    @Resource
    private TenantCacheService tenantCacheService;

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    @Override
    public PageData<OtcPrepPickingSlipPageVO> pageByQuery(PageSearch<OtcPrepPickingSlipListQuery> search) {
        Page<OtcPrepPickingSlip> page = Conditions.page(search, entityClass);
        OtcPrepPickingSlipListQuery condition = search.getCondition();
        List<OtcPrepPickingSlipPageVO> dataList = mapper.listByQuery(condition.getOtcPrepPickingSlipQuery(), condition.getOtcWorkorderQuery(), page);
        this.fillRelationVO(dataList);
        return new PageData<>(dataList, page);
    }


    @Override
    public OtcPrepPickingSlipVO detailById(Long id) {
        OtcPrepPickingSlip entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in OtcPrepPickingSlip");
        }
        return buildOtcPrepPickingSlipVO(entity);
    }

    @Override
    public OtcPrepPickingSlipVO detailByRefNum(String refNum) {
        OtcPrepPickingSlip entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("RefNum: " + refNum + " not found in OtcPrepPickingSlip");
        }
        return buildOtcPrepPickingSlipVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean pick(List<OtcPrepPickingSlipProductPickQuery> query, Long pickingSlipId) {
        OtcPrepPickingSlip slip = this.getById(pickingSlipId);
        Validate.notNull(slip, "id {} not found in Prep Picking Slip", pickingSlipId);

        // 仅支持New、InPicking状态
        Validate.isTrue(OtcPrepPickingSlipStatusEnum.canPickProcessStatus(slip.getPrepPickingSlipStatus()),
                "{} Only support New,InPicking Status", slip.refNumLog()
        );

        ProcessType.checkNormalAvailability(slip.getProcessType(), slip.refNumLog(), "pick");

        OtcPrepPickingSlipPickContextVO context = new OtcPrepPickingSlipPickContextVO();
        context.setPickList(query);
        context.setPrepPickingSlip(slip);

        // 拣货单拣货
        this.pickPickingSlip(context);

        // 更新拣货单
        this.update(slip);
        // Prep工单拣货
        otcPrepWorkorderService.pick(context);

        // 记录日志
        this.recordPickLog(context);
        return true;
    }

    @Override
    public List<DropProVO> distinctValue(OtcPrepPickingSlipQuery query) {
        return DropListUtil.dropProList(
                query.getColumnNameList(),
                OtcPrepPickingSlip.class,
                columnNameList -> mapper.dropProList(columnNameList, query)
        );
    }

    @Override
    public OtcPrepPickingSlipVO buildOtcPrepPickingSlipVO(OtcPrepPickingSlip entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的OTC预提货单VO对象
        OtcPrepPickingSlipVO vo = Converters.get(OtcPrepPickingSlipConverter.class).toVO(entity);
        // detail
        List<OtcPrepPickingSlipDetail> details = otcPrepPickingSlipDetailService.listByOtcPrepPickingSlipId(vo.getId());
        vo.setDetailList(BeanUtil.copyNew(details, OtcPrepPickingSlipDetailVO.class));
        // 拣货单
        vo.setOtcPickingSlip(otcPickingSlipService.refNumById(entity.getOtcPickingSlipId()));
        // description 获取
        vo.setDescription(pickingSlipService.getHowToDoDescription(this.getPrepProductTree(entity)));
        // 设置可上架数量
        vo.setCanPutawayQty(entity.getAllocatePutawayQty() - entity.getPutawayQty());

        // 供应商
        TenantCache tenantCache = tenantCacheService.getById(entity.getTransactionPartnerId());
        if (ObjectUtil.isNotNull(tenantCache)) {
            vo.setBasePartnerVO(BeanUtil.copyNew(tenantCache, BasePartnerVO.class));
        }

        return vo;
    }

    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 填充关联VO
     *
     * @param dataList dataList
     */
    private void fillRelationVO(List<OtcPrepPickingSlipPageVO> dataList) {
        if (ObjectUtil.isEmpty(dataList)) {
            return;
        }
        // 拣货单
        List<Long> pickingSlipIds = StreamUtils.distinctMap(dataList, OtcPrepPickingSlipPageVO::getOtcPickingSlipId);
        Map<Long, RefNumVO> pickingSlipMap = otcPickingSlipService.refNumMapByIds(pickingSlipIds);
        dataList.forEach(obj -> obj.setPickingSlip(pickingSlipMap.get(obj.getOtcPickingSlipId())));
    }

    /**
     * 拣货单拣货
     *
     * @param context 上下文
     */
    private void pickPickingSlip(OtcPrepPickingSlipPickContextVO context) {
        List<OtcPrepPickingSlipProductPickQuery> pickList = context.getPickList();
        OtcPrepPickingSlip prepPickingSlip = context.getPrepPickingSlip();
        // 拣货单拣货信息
        List<OtcPrepPickingSlipDetailPickVO> pickingSlipDetailPickList = otcPrepPickingSlipDetailService.pick(pickList, prepPickingSlip.getId());

        // 拣货的详情id
        List<Long> prepPickingSlipDetilIdList = StreamUtils.distinctMap(pickingSlipDetailPickList, BasePickVO::getId);
        // 全部分配完就拣货完成
        boolean picked = pickingSlipDetailPickList.stream().allMatch(detail -> Objects.equals(detail.getQty(), detail.getPickedQty()))
                // 数据里的也全部拣货完
                && otcPrepPickingSlipDetailService.allPickedIgnoreDetailIdList(prepPickingSlip.getId(), prepPickingSlipDetilIdList);

        // 更新计算AllocatePutAwayQty
        prepPickingSlip.setAllocatePutawayQty(this.getCanPutAwayQty(pickingSlipDetailPickList, prepPickingSlip));

        if (Objects.equals(prepPickingSlip.getPrepPickingSlipStatus(), OtcPrepPickingSlipStatusEnum.NEW.getStatus())) {
            // 拣货单 InPicking 日志
            OtcPrepPickingSlipAuditLogHelper.recordLog(prepPickingSlip, OtcPrepPickingSlipStatusEnum.IN_PICKING.getStatus(), null, null);
        }

        // 设置状态
        prepPickingSlip.setPrepPickingSlipStatus(picked
                ? OtcPrepPickingSlipStatusEnum.PICKED.getStatus()
                : OtcPrepPickingSlipStatusEnum.IN_PICKING.getStatus()
        );
        // 绑定拣货单锁信息
        pickingSlipDetailPickList.forEach(obj -> {
            obj.setRefTableShowRefNum(prepPickingSlip.getRefNum());
            obj.setRefTableShowName(OtcPrepPickingSlip.class.getSimpleName());
        });

        // 绑定拣货后的信息到上下文中
        context.setPickAfterPrepDetailList(pickingSlipDetailPickList);
    }

    /**
     * 获取可以PutAway的数量
     *
     * @param prepPickingSlip Prep拣货单
     * @return /
     */
    private int getCanPutAwayQty(List<OtcPrepPickingSlipDetailPickVO> pickList, OtcPrepPickingSlip prepPickingSlip) {
        boolean allPicked = pickList.stream().allMatch(obj -> obj.getQty() - obj.getPickedQty() == 0);
        // 全部拣货完成
        return allPicked
                ? prepPickingSlip.getQty()
                : pickingSlipService.getCanPutAwayQty(pickList, this.getPrepProductTree(prepPickingSlip));
    }


    /**
     * 获取Prep产品树结构
     *
     * @param prepPickingSlip prepPickingSlip
     * @return /
     */
    private PrepFullProductVO getPrepProductTree(OtcPrepPickingSlip prepPickingSlip) {
        Map<Long, List<OtcPrepWorkorder>> allWorkOrdertGroupByPsMap
                = otcPrepWorkorderService.groupByPrepPickingSlipIdList(Collections.singletonList(prepPickingSlip.getId()));
        if (ObjectUtil.isEmpty(allWorkOrdertGroupByPsMap)) {
            return null;
        }

        // Prep工单id
        List<Long> prepWorkOrderIdList = StreamUtils.distinctMap(allWorkOrdertGroupByPsMap.get(prepPickingSlip.getId()), IdModel::getId);
        List<WorkOrderMembershipBO> detailList = otcPrepWorkorderDetailService.groupByOtcPrepWorkOrderIdList(prepWorkOrderIdList)
                .values()
                .stream()
                .flatMap(Collection::stream)
                .map(obj -> BeanUtil.copyNew(obj, WorkOrderMembershipBO.class))
                .toList();

        // 获取Group\填充Group
        ProductTreeQuery productTreeQuery = new ProductTreeQuery()
                .setProductId(prepPickingSlip.getProductId())
                .setProductVersionInt(prepPickingSlip.getPrepPickingSlipVersionInt())
                .setPrepWorkOrderType(prepPickingSlip.getPrepPickingSlipType());
        return pickingSlipService.findAndFillWithPrepConvert(productTreeQuery, detailList);
    }

    /**
     * 记录拣货日志
     *
     * @param context 上下文
     */
    private void recordPickLog(OtcPrepPickingSlipPickContextVO context) {
        // Prep拣货单: New -> InPicking 日志
        String desc = AuditLogUtil.pickLogDesc(context.getPickAfterPrepDetailList());
        OtcPrepPickingSlip pickingSlip = context.getPrepPickingSlip();

        // 拣货单 Pick 日志
        OtcPrepPickingSlipAuditLogHelper.recordLog(pickingSlip, PickingSlipLogConstant.PICK_STATUS, desc, null, BaseTypeLogEnum.OPERATION.getType());

        // 拣货单 InPicking 日志
        OtcPrepPickingSlip prepPickingSlip = context.getPrepPickingSlip();

        // Picked 记录日志
        if (Objects.equals(prepPickingSlip.getPrepPickingSlipStatus(), OtcPrepPickingSlipStatusEnum.PICKED.getStatus())) {
            OtcPrepPickingSlipAuditLogHelper.recordLog(prepPickingSlip, PickingSlipLogConstant.PICKED_DESCRIPTION, null);
        }
    }
}
