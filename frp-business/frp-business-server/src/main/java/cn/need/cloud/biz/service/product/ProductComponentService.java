package cn.need.cloud.biz.service.product;

import cn.need.cloud.biz.model.bo.product.ProductComponentDicBO;
import cn.need.cloud.biz.model.entity.product.ProductComponent;
import cn.need.cloud.biz.model.param.product.update.ProductComponentCreateOrUpdateParam;
import cn.need.cloud.biz.model.query.product.ProductComponentQuery;
import cn.need.cloud.biz.model.vo.product.AssemblyProductListVO;
import cn.need.cloud.biz.model.vo.product.ComponentProductListVO;
import cn.need.cloud.biz.model.vo.product.ProductComponentVO;
import cn.need.cloud.biz.model.vo.product.page.ProductComponentPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 产品组装 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface ProductComponentService extends SuperService<ProductComponent> {

    /**
     * 根据参数新增或更新产品组装
     *
     * @param paramList 请求创建参数，包含需要更新的产品组装的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int createOrUpdate(List<ProductComponentCreateOrUpdateParam> paramList);


    /**
     * 根据查询条件获取产品组装列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个产品组装对象的列表(分页)
     */
    List<ProductComponentPageVO> listByQuery(ProductComponentQuery query);

    /**
     * 根据查询条件获取产品组装列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个产品组装对象的列表(分页)
     */
    PageData<ProductComponentPageVO> pageByQuery(PageSearch<ProductComponentQuery> search);

    /**
     * 根据ID获取产品组装详情
     *
     * @param id 产品组装ID
     * @return 返回产品组装VO对象
     */
    ProductComponentVO detailById(Long id);

    /**
     * 根据产品ID获取组件列表
     *
     * @param productId 产品ID
     * @return 返回组件列表
     */
    List<ComponentProductListVO> componentList(Long productId);

    /**
     * 根据产品ID获取装配列表
     *
     * @param productId 产品ID
     * @return 返回装配列表
     */
    List<AssemblyProductListVO> assemblyList(Long productId);

    /**
     * 根据组装产品ID获取产品组件列表
     *
     * @param assemblyProductId 组装产品ID
     * @return 产品组件列表
     */
    List<ProductComponent> getListByAssemblyProductId(Long assemblyProductId);

    /**
     * 根据组装产品ID获取产品组件First
     *
     * @param assemblyProductId 组装产品ID
     * @return 产品组件First
     */
    ProductComponent getFirstByAssemblyProductId(Long assemblyProductId);

    /**
     * 根据组装产品ID获取产品组件列表
     *
     * @param assemblyProductId 组装产品ID
     * @return 产品组件列表
     */
    List<ProductComponent> getListByAssemblyProductIdAndVersionInt(Long assemblyProductId, int versionInt);

    /**
     * 根据组装产品ID删除产品组件关联
     *
     * @param assemblyProductId 组装产品ID
     * @param deleteNote        删除备注
     * @return 影响的行数
     */
    Integer deleteByAssemblyProductId(Long assemblyProductId, String deleteNote);

    /**
     * 获取所有的Components 根据组合品ids
     *
     * @param assemblyProductIdList 组合品ids
     * @return 产品组装 vo对象
     */

    List<ProductComponent> getListComponentsByAssemblyProductIdList(List<Long> assemblyProductIdList);

    /**
     * 获取所有的Components 根据组合品ids
     * 如果没有返回空数组
     *
     * @param assemblyProductIdList 组合品ids
     * @return 产品组装 vo对象
     */
    List<ProductComponentDicBO> getListComponentDicByAssemblyProductIdList(List<Long> assemblyProductIdList);

    /**
     * 判断给定的productId是否为组件
     *
     * @param productId 组件或产品的ID，用于判断其是否为组件
     * @return 如果productId对应的项是组件，则返回true；否则返回false
     */
    boolean isComponent(Long productId);

    /**
     * 根据组合品id获取组件列表
     *
     * @param assemblyProductId 组合品id
     * @return 组合品列表
     */
    List<ProductComponent> list(Long assemblyProductId);
}