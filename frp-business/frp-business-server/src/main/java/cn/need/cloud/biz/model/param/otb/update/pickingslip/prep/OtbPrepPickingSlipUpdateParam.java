package cn.need.cloud.biz.model.param.otb.update.pickingslip.prep;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;


/**
 * otb预拣货单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "otb预拣货单 vo对象")
public class OtbPrepPickingSlipUpdateParam implements Serializable {


    @Serial
    private static final long serialVersionUID = 4824818541094705521L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 分配人
     */
    @Schema(description = "分配人")
    private Long assignedUserId;

    /**
     * 拣货到那里
     */
    @Schema(description = "拣货到那里")
    private String pickToStation;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * otb预拣货单状态
     */
    @Schema(description = "otb预拣货单状态")
    private String otbPrepPickingSlipStatus;

    /**
     * otb预拣货单类型
     */
    @Schema(description = "otb预拣货单类型")
    private String otbPrepPickingSlipType;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * otb拣货单id
     */
    @Schema(description = "otb拣货单id")
    private Long otbPickingSlipId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 已经分配的上架数量
     */
    @Schema(description = "已经分配的上架数量")
    private Integer allocatePutawayQty;

    /**
     * 上架数量
     */
    @Schema(description = "上架数量")
    private Integer putawayQty;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;


    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    @Size(max = 64, message = "hazmatVersionRefNum cannot exceed 64 characters")
    private String hazmatVersionRefNum;

    /**
     * 渠道要求的需要贴的产品标识码
     */
    @Schema(description = "渠道要求的需要贴的产品标识码")
    private String productBarcode;

}