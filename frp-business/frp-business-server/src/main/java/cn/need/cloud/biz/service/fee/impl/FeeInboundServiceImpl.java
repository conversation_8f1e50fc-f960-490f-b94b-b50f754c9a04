package cn.need.cloud.biz.service.fee.impl;


import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.fee.FeeInboundConverter;
import cn.need.cloud.biz.mapper.fee.FeeInboundMapper;
import cn.need.cloud.biz.model.entity.fee.FeeInbound;
import cn.need.cloud.biz.model.param.fee.create.FeeInboundCreateParam;
import cn.need.cloud.biz.model.param.fee.update.FeeInboundUpdateParam;
import cn.need.cloud.biz.model.query.fee.FeeInboundQuery;
import cn.need.cloud.biz.model.vo.fee.FeeInboundDetailVO;
import cn.need.cloud.biz.model.vo.fee.FeeInboundVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeInboundPageVO;
import cn.need.cloud.biz.service.fee.FeeInboundDetailService;
import cn.need.cloud.biz.service.fee.FeeInboundService;
import cn.need.cloud.biz.service.fee.FeeOriginalDataService;
import cn.need.cloud.biz.service.feeconfig.FeeConfigInboundService;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 费用inbound service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Service
public class FeeInboundServiceImpl extends SuperServiceImpl<FeeInboundMapper, FeeInbound> implements FeeInboundService {

    @Resource
    private FeeOriginalDataService feeOriginalDataService;

    @Resource
    private FeeConfigInboundService feeConfigInboundService;

    @Resource
    private FeeInboundDetailService feeInboundDetailService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(FeeInboundCreateParam createParam) {
        // 检查传入费用inbound参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 将费用inbound参数对象转换为实体对象并初始化
        FeeInbound entity = initFeeInbound(createParam);

        // 插入费用inbound实体对象到数据库
        super.insert(entity);

        // 返回费用inboundID
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(FeeInboundUpdateParam updateParam) {
        // 检查传入费用inbound参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 将费用inbound参数对象转换为实体对象
        FeeInbound entity = initFeeInbound(updateParam);

        // 执行更新费用inbound操作
        return super.update(entity);

    }

    @Override
    public List<FeeInboundPageVO> listByQuery(FeeInboundQuery query) {
        if (ObjectUtil.isEmpty(query)) {
            return Collections.emptyList();
        }
        List<FeeInboundPageVO> feeInboundPageVOList = mapper.listByQuery(query);

        fillFod(feeInboundPageVOList);

        return feeInboundPageVOList;
    }

    @Override
    public PageData<FeeInboundPageVO> pageByQuery(PageSearch<FeeInboundQuery> search) {
        Page<FeeInbound> page = Conditions.page(search, entityClass);
        List<FeeInboundPageVO> dataList = mapper.listByQuery(search.getCondition(), page);

        fillFod(dataList);

        return new PageData<>(dataList, page);
    }

    @Override
    public FeeInboundVO detailById(Long id) {
        FeeInbound entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "FeeInbound", id));
        }
        FeeInboundVO feeInboundVO = buildFeeInboundVO(entity);

        fillFod(feeInboundVO);

        List<FeeInboundDetailVO> detailList = feeInboundDetailService.listByFeeInboundId(feeInboundVO.getId());


        feeInboundVO.setDetailList(detailList);

        return feeInboundVO;
    }

    @Override
    public FeeInboundVO detailByRefNum(String refNum) {
        FeeInbound entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "FeeInbound", "refNum", refNum));
        }
        return buildFeeInboundVO(entity);
    }

    /**
     * 初始化费用inbound对象
     * 此方法用于设置费用inbound对象的必要参数，确保其处于有效状态
     *
     * @param createParam 费用inbound 新增对象，不应为空
     * @return 返回初始化后的费用inbound
     * @throws BusinessException 如果传入的费用inbound为空，则抛出此异常
     */
    private FeeInbound initFeeInbound(FeeInboundCreateParam createParam) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "FeeInboundCreateParam"));
        }

        // 获取费用inbound转换器实例，用于将费用inbound参数对象转换为实体对象
        FeeInboundConverter converter = Converters.get(FeeInboundConverter.class);

        // 将费用inbound参数对象转换为实体对象并初始化
        FeeInbound entity = converter.toEntity(createParam);


        entity.setId(IdWorker.getId());
        entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.FEE_INBOUND));

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 初始化费用inbound对象
     * 此方法用于设置费用inbound对象的必要参数，确保其处于有效状态
     *
     * @param updateParam 费用inbound 修改对象，不应为空
     * @return 返回初始化后的费用inbound
     * @throws BusinessException 如果传入的费用inbound为空，则抛出此异常
     */
    private FeeInbound initFeeInbound(FeeInboundUpdateParam updateParam) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(updateParam)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "FeeInboundUpdateParam"));
        }

        // 获取费用inbound转换器实例，用于将费用inbound参数对象转换为实体对象
        FeeInboundConverter converter = Converters.get(FeeInboundConverter.class);

        // 将费用inbound参数对象转换为实体对象并初始化
        FeeInbound entity = converter.toEntity(updateParam);

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 构建费用inboundVO对象
     *
     * @param entity 费用inbound对象
     * @return 返回包含详细信息的费用inboundVO对象
     */
    private FeeInboundVO buildFeeInboundVO(FeeInbound entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的费用inboundVO对象
        return Converters.get(FeeInboundConverter.class).toVO(entity);
    }

}
