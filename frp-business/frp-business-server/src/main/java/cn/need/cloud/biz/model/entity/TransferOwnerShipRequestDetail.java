package cn.need.cloud.biz.model.entity;

import cn.need.framework.common.mybatis.model.TenantModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * 货权转移详情
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName
public class TransferOwnerShipRequestDetail extends TenantModel {

    @TableField
    private Long headerId;
    /**
     * 删除原因
     */
    @TableField
    private String deletedNote;

    /**
     * 源产品Id
     */
    @TableField
    private Long fromProductId;

    /**
     * 序号
     */
    @TableField
    private Integer lineNum;

    /**
     * 备注
     */
    @TableField
    private String note;

    /**
     * 目标产品Id
     */
    @TableField
    private Long toProductId;

    /**
     * 转移数量
     */
    @TableField
    private Integer transferQty;

    /**
     * 仓库id
     */
    @TableField
    private Long warehouseId;

}
