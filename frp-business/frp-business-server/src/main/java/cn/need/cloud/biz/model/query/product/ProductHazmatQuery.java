package cn.need.cloud.biz.model.query.product;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * Query对象
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = " Query对象")
public class ProductHazmatQuery extends SuperQuery {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    // region packageInstruction

    /**
     * packageInstruction
     */
    @Schema(description = "packageInstruction")
    private String packageInstruction;

    /**
     * packageInstruction
     */
    @Schema(description = "packageInstruction 集合")
    @Condition(value = Keyword.IN, fields = {"packageInstruction"})
    private List<String> packageInstructionList;

    /**
     * packageInstruction
     */
    @Schema(description = "packageInstruction 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"packageInstruction"})
    private List<String> packageInstructionNiList;

    /**
     * packageInstruction
     */
    @Schema(description = "packageInstruction值类型集合")
    private List<String> packageInstructionValueTypeList;

    /**
     * packageInstruction
     */
    @Schema(description = "packageInstruction 大于")
    @Condition(value = Keyword.GT, fields = {"packageInstruction"})
    private String packageInstructionGt;

    /**
     * packageInstruction
     */
    @Schema(description = "packageInstruction 大于等于")
    @Condition(value = Keyword.GE, fields = {"packageInstruction"})
    private String packageInstructionGe;

    /**
     * packageInstruction
     */
    @Schema(description = "packageInstruction 小于")
    @Condition(value = Keyword.LT, fields = {"packageInstruction"})
    private String packageInstructionLt;

    /**
     * packageInstruction
     */
    @Schema(description = "packageInstruction 小于等于")
    @Condition(value = Keyword.LE, fields = {"packageInstruction"})
    private String packageInstructionLe;

    /**
     * packageInstruction
     */
    @Schema(description = "packageInstruction 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"packageInstruction"})
    private String packageInstructionLike;

    /**
     * packageInstruction
     */
    @Schema(description = "packageInstruction 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"packageInstruction"})
    private String packageInstructionLikeLeft;

    /**
     * packageInstruction
     */
    @Schema(description = "packageInstruction 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"packageInstruction"})
    private String packageInstructionLikeRight;

    // endregion packageInstruction

    // region productId

    /**
     * productId
     */
    @Schema(description = "productId")
    private Long productId;

    /**
     * productId
     */
    @Schema(description = "productId 集合")
    @Condition(value = Keyword.IN, fields = {"productId"})
    private List<Long> productIdList;

    /**
     * productId
     */
    @Schema(description = "productId 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"productId"})
    private List<Long> productIdNiList;

    /**
     * productId
     */
    @Schema(description = "productId值类型集合")
    private List<String> productIdValueTypeList;

    /**
     * productId
     */
    @Schema(description = "productId 大于")
    @Condition(value = Keyword.GT, fields = {"productId"})
    private Long productIdGt;

    /**
     * productId
     */
    @Schema(description = "productId 大于等于")
    @Condition(value = Keyword.GE, fields = {"productId"})
    private Long productIdGe;

    /**
     * productId
     */
    @Schema(description = "productId 小于")
    @Condition(value = Keyword.LT, fields = {"productId"})
    private Long productIdLt;

    /**
     * productId
     */
    @Schema(description = "productId 小于等于")
    @Condition(value = Keyword.LE, fields = {"productId"})
    private Long productIdLe;

    /**
     * productId
     */
    @Schema(description = "productId 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"productId"})
    private Long productIdLike;

    /**
     * productId
     */
    @Schema(description = "productId 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"productId"})
    private Long productIdLikeLeft;

    /**
     * productId
     */
    @Schema(description = "productId 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"productId"})
    private Long productIdLikeRight;

    // endregion productId

    // region transportationRegulatoryClass

    /**
     * transportationRegulatoryClass
     */
    @Schema(description = "transportationRegulatoryClass")
    private String transportationRegulatoryClass;

    /**
     * transportationRegulatoryClass
     */
    @Schema(description = "transportationRegulatoryClass 集合")
    @Condition(value = Keyword.IN, fields = {"transportationRegulatoryClass"})
    private List<String> transportationRegulatoryClassList;

    /**
     * transportationRegulatoryClass
     */
    @Schema(description = "transportationRegulatoryClass 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"transportationRegulatoryClass"})
    private List<String> transportationRegulatoryClassNiList;

    /**
     * transportationRegulatoryClass
     */
    @Schema(description = "transportationRegulatoryClass值类型集合")
    private List<String> transportationRegulatoryClassValueTypeList;

    /**
     * transportationRegulatoryClass
     */
    @Schema(description = "transportationRegulatoryClass 大于")
    @Condition(value = Keyword.GT, fields = {"transportationRegulatoryClass"})
    private String transportationRegulatoryClassGt;

    /**
     * transportationRegulatoryClass
     */
    @Schema(description = "transportationRegulatoryClass 大于等于")
    @Condition(value = Keyword.GE, fields = {"transportationRegulatoryClass"})
    private String transportationRegulatoryClassGe;

    /**
     * transportationRegulatoryClass
     */
    @Schema(description = "transportationRegulatoryClass 小于")
    @Condition(value = Keyword.LT, fields = {"transportationRegulatoryClass"})
    private String transportationRegulatoryClassLt;

    /**
     * transportationRegulatoryClass
     */
    @Schema(description = "transportationRegulatoryClass 小于等于")
    @Condition(value = Keyword.LE, fields = {"transportationRegulatoryClass"})
    private String transportationRegulatoryClassLe;

    /**
     * transportationRegulatoryClass
     */
    @Schema(description = "transportationRegulatoryClass 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"transportationRegulatoryClass"})
    private String transportationRegulatoryClassLike;

    /**
     * transportationRegulatoryClass
     */
    @Schema(description = "transportationRegulatoryClass 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"transportationRegulatoryClass"})
    private String transportationRegulatoryClassLikeLeft;

    /**
     * transportationRegulatoryClass
     */
    @Schema(description = "transportationRegulatoryClass 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"transportationRegulatoryClass"})
    private String transportationRegulatoryClassLikeRight;

    // endregion transportationRegulatoryClass

    // region unRegulatoryId

    /**
     * unRegulatoryId
     */
    @Schema(description = "unRegulatoryId")
    private String unRegulatoryId;

    /**
     * unRegulatoryId
     */
    @Schema(description = "unRegulatoryId 集合")
    @Condition(value = Keyword.IN, fields = {"unRegulatoryId"})
    private List<String> unRegulatoryIdList;

    /**
     * unRegulatoryId
     */
    @Schema(description = "unRegulatoryId 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"unRegulatoryId"})
    private List<String> unRegulatoryIdNiList;

    /**
     * unRegulatoryId
     */
    @Schema(description = "unRegulatoryId值类型集合")
    private List<String> unRegulatoryIdValueTypeList;

    /**
     * unRegulatoryId
     */
    @Schema(description = "unRegulatoryId 大于")
    @Condition(value = Keyword.GT, fields = {"unRegulatoryId"})
    private String unRegulatoryIdGt;

    /**
     * unRegulatoryId
     */
    @Schema(description = "unRegulatoryId 大于等于")
    @Condition(value = Keyword.GE, fields = {"unRegulatoryId"})
    private String unRegulatoryIdGe;

    /**
     * unRegulatoryId
     */
    @Schema(description = "unRegulatoryId 小于")
    @Condition(value = Keyword.LT, fields = {"unRegulatoryId"})
    private String unRegulatoryIdLt;

    /**
     * unRegulatoryId
     */
    @Schema(description = "unRegulatoryId 小于等于")
    @Condition(value = Keyword.LE, fields = {"unRegulatoryId"})
    private String unRegulatoryIdLe;

    /**
     * unRegulatoryId
     */
    @Schema(description = "unRegulatoryId 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"unRegulatoryId"})
    private String unRegulatoryIdLike;

    /**
     * unRegulatoryId
     */
    @Schema(description = "unRegulatoryId 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"unRegulatoryId"})
    private String unRegulatoryIdLikeLeft;

    /**
     * unRegulatoryId
     */
    @Schema(description = "unRegulatoryId 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"unRegulatoryId"})
    private String unRegulatoryIdLikeRight;

    // endregion unRegulatoryId

    // region versionRefNum

    /**
     * versionRefNum
     */
    @Schema(description = "versionRefNum")
    private String versionRefNum;

    /**
     * versionRefNum
     */
    @Schema(description = "versionRefNum 集合")
    @Condition(value = Keyword.IN, fields = {"versionRefNum"})
    private List<String> versionRefNumList;

    /**
     * versionRefNum
     */
    @Schema(description = "versionRefNum 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"versionRefNum"})
    private List<String> versionRefNumNiList;

    /**
     * versionRefNum
     */
    @Schema(description = "versionRefNum值类型集合")
    private List<String> versionRefNumValueTypeList;

    /**
     * versionRefNum
     */
    @Schema(description = "versionRefNum 大于")
    @Condition(value = Keyword.GT, fields = {"versionRefNum"})
    private String versionRefNumGt;

    /**
     * versionRefNum
     */
    @Schema(description = "versionRefNum 大于等于")
    @Condition(value = Keyword.GE, fields = {"versionRefNum"})
    private String versionRefNumGe;

    /**
     * versionRefNum
     */
    @Schema(description = "versionRefNum 小于")
    @Condition(value = Keyword.LT, fields = {"versionRefNum"})
    private String versionRefNumLt;

    /**
     * versionRefNum
     */
    @Schema(description = "versionRefNum 小于等于")
    @Condition(value = Keyword.LE, fields = {"versionRefNum"})
    private String versionRefNumLe;

    /**
     * versionRefNum
     */
    @Schema(description = "versionRefNum 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"versionRefNum"})
    private String versionRefNumLike;

    /**
     * versionRefNum
     */
    @Schema(description = "versionRefNum 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"versionRefNum"})
    private String versionRefNumLikeLeft;

    /**
     * versionRefNum
     */
    @Schema(description = "versionRefNum 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"versionRefNum"})
    private String versionRefNumLikeRight;

    // endregion versionRefNum


}