package cn.need.cloud.biz.mapper.feeconfig;


import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigOtc;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigOtcQuery;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigOtcPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 仓库报价费用配置otc Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Mapper
public interface FeeConfigOtcMapper extends SuperMapper<FeeConfigOtc> {

    /**
     * 根据条件获取仓库报价费用配置otc列表
     *
     * @param query 查询条件
     * @return 仓库报价费用配置otc集合
     */
    default List<FeeConfigOtcPageVO> listByQuery(@Param("qofco") FeeConfigOtcQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取仓库报价费用配置otc分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 仓库报价费用配置otc集合
     */
    List<FeeConfigOtcPageVO> listByQuery(@Param("qofco") FeeConfigOtcQuery query, @Param("page") Page<?> page);
}