package cn.need.cloud.biz.mapper.warehouse;

import cn.need.cloud.biz.model.entity.warehouse.WarehouseProduct;
import cn.need.cloud.biz.model.query.warehouse.WarehouseProductQuery;
import cn.need.cloud.biz.model.vo.page.WarehouseProductPageVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseProductListVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 产品即发货 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface WarehouseProductMapper extends SuperMapper<WarehouseProduct> {

    /**
     * 根据条件获取产品即发货列表
     *
     * @param query 查询条件
     * @return 产品即发货集合
     */
    default List<WarehouseProductPageVO> listByQuery(WarehouseProductQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取产品即发货分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 产品即发货集合
     */
    List<WarehouseProductPageVO> listByQuery(@Param("qo") WarehouseProductQuery query, @Param("page") Page<?> page);

    /**
     * 根据productId产品即发货列表
     *
     * @param productId 查询条件
     * @return 产品即发货集合
     */
    List<WarehouseProductListVO> listByProductId(@Param("productId") Long productId);
}