package cn.need.cloud.biz.model.vo.base.aware;

import cn.need.cloud.biz.model.vo.base.BasePartnerVO;
import cn.need.framework.common.core.lang.ObjectUtil;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;


/**
 * 超级仓库交易伙伴展示对象
 * <p>
 * 子类中不能包含 transactionPartnerId
 * </p>
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
public class BaseSuperWarehouseTransactionPartnerShowVO extends BaseSuperWarehouseShowVO implements BaseTransactionPartnerAware {
    @Serial
    private static final long serialVersionUID = 4791553385772429198L;
    /**
     * 交易伙伴id
     */
    @Getter
    @Setter
    private Long transactionPartnerId;

    /**
     * 交易伙伴
     */
    @Setter(lombok.AccessLevel.PRIVATE)
    private BasePartnerVO transactionPartnerVO;

    @Override
    public BasePartnerVO getTransactionPartnerVO() {
        if (ObjectUtil.isEmpty(transactionPartnerId)) {
            return null;
        }
        if (ObjectUtil.isNotEmpty(transactionPartnerVO)) {
            return transactionPartnerVO;
        }
        // Retrieve from cache once and store the result
        transactionPartnerVO = BaseTransactionPartnerAware.super.getTransactionPartnerVO();
        return transactionPartnerVO;
    }
}
