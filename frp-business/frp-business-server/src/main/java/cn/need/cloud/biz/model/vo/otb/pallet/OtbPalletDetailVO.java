package cn.need.cloud.biz.model.vo.otb.pallet;

import cn.need.cloud.biz.model.vo.base.BaseFullProductVO;
import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * OTB托盘详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTB托盘详情 vo对象")
public class OtbPalletDetailVO extends BaseSuperVO {


    @Serial
    private static final long serialVersionUID = 4084027692530544847L;
    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 渠道要求的需要贴的产品标识码
     */
    @Schema(description = "渠道要求的需要贴的产品标识码")
    private String productBarcode;

    @Schema(description = "渠道要求的需要贴的产品sku")
    private String productChannelSku;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    private String hazmatVersionRefNum;

    @Schema(description = "产品详情")
    private BaseFullProductVO baseFullProductVO;

    /**
     * otb托盘id
     */
    @Schema(description = "otb托盘id")
    private Long otbPalletId;

}