package cn.need.cloud.biz.model.vo.base.putawayslip;

import cn.need.cloud.biz.model.vo.base.RefNumVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTC上架单 VO对象
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PrepPutawaySlipVO<T extends PutawaySlipDetailVO> extends PutawaySlipVO<T> {

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id")
    private Long prepPickingSlipId;

    /**
     * 工单id
     */
    @Schema(description = "拣货单")
    private RefNumVO prepPickingSlip;

    /**
     * 工单id
     */
    @Schema(description = "工单id")
    private Long prepWorkorderId;

    /**
     * 工单id
     */
    @Schema(description = "工单")
    private RefNumVO prepWorkorder;

}