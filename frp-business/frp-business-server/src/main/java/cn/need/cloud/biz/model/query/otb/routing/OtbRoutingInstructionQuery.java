package cn.need.cloud.biz.model.query.otb.routing;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;


/**
 * otb发货指南 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "otb发货指南 query对象")
public class OtbRoutingInstructionQuery extends SuperQuery {

    /**
     * 承运人联系人姓名
     */
    @Schema(description = "承运人联系人姓名")
    private String carrierContactName;

    /**
     * 承运人联系人电话
     */
    @Schema(description = "承运人联系人电话")
    private String carrierContactPhone;

    /**
     * 承运人联系人邮箱
     */
    @Schema(description = "承运人联系人邮箱")
    private String carrierContactEmail;

    /**
     * 收货人联系人姓名
     */
    @Schema(description = "收货人联系人姓名")
    private String receivingContactName;

    /**
     * 收货人联系人电话
     */
    @Schema(description = "收货人联系人电话")
    private String receivingContactPhone;

    /**
     * 收货人联系人邮箱
     */
    @Schema(description = "收货人联系人邮箱")
    private String receivingContactEmail;

    /**
     * 发货人联系人姓名
     */
    @Schema(description = "发货人联系人姓名")
    private String shipperContactName;

    /**
     * 发货人联系人电话
     */
    @Schema(description = "发货人联系人电话")
    private String shipperContactPhone;

    /**
     * 发货人联系人邮箱
     */
    @Schema(description = "发货人联系人邮箱")
    private String shipperContactEmail;

    /**
     * 发货地址名称
     */
    @Schema(description = "发货地址名称")
    private String shipFromAddressName;

    /**
     * 发货地址公司
     */
    @Schema(description = "发货地址公司")
    private String shipFromAddressCompany;

    /**
     * 发货地址国家
     */
    @Schema(description = "发货地址国家")
    private String shipFromAddressCountry;

    /**
     * 发货地址州/省
     */
    @Schema(description = "发货地址州/省")
    private String shipFromAddressState;

    /**
     * 发货地址城市
     */
    @Schema(description = "发货地址城市")
    private String shipFromAddressCity;

    /**
     * 发货地址邮编
     */
    @Schema(description = "发货地址邮编")
    private String shipFromAddressZipCode;

    /**
     * 发货地址1
     */
    @Schema(description = "发货地址1")
    private String shipFromAddressAddr1;

    /**
     * 发货地址2
     */
    @Schema(description = "发货地址2")
    private String shipFromAddressAddr2;

    /**
     * 发货地址3
     */
    @Schema(description = "发货地址3")
    private String shipFromAddressAddr3;

    /**
     * 发货地址邮箱
     */
    @Schema(description = "发货地址邮箱")
    private String shipFromAddressEmail;

    /**
     * 发货地址电话
     */
    @Schema(description = "发货地址电话")
    private String shipFromAddressPhone;

    /**
     * 发货地址备注
     */
    @Schema(description = "发货地址备注")
    private String shipFromAddressNote;

    /**
     * 发货地址是否为住宅地址
     */
    @Schema(description = "发货地址是否为住宅地址")
    private Boolean shipFromAddressIsResidential;

    /**
     * 收货地址名称
     */
    @Schema(description = "收货地址名称")
    private String shipToAddressName;

    /**
     * 收货地址公司
     */
    @Schema(description = "收货地址公司")
    private String shipToAddressCompany;

    /**
     * 收货地址国家
     */
    @Schema(description = "收货地址国家")
    private String shipToAddressCountry;

    /**
     * 收货地址州/省
     */
    @Schema(description = "收货地址州/省")
    private String shipToAddressState;

    /**
     * 收货地址城市
     */
    @Schema(description = "收货地址城市")
    private String shipToAddressCity;

    /**
     * 收货地址邮编
     */
    @Schema(description = "收货地址邮编")
    private String shipToAddressZipCode;

    /**
     * 收货地址1
     */
    @Schema(description = "收货地址1")
    private String shipToAddressAddr1;

    /**
     * 收货地址2
     */
    @Schema(description = "收货地址2")
    private String shipToAddressAddr2;

    /**
     * 收货地址3
     */
    @Schema(description = "收货地址3")
    private String shipToAddressAddr3;

    /**
     * 收货地址邮箱
     */
    @Schema(description = "收货地址邮箱")
    private String shipToAddressEmail;

    /**
     * 收货地址电话
     */
    @Schema(description = "收货地址电话")
    private String shipToAddressPhone;

    /**
     * 收货地址备注
     */
    @Schema(description = "收货地址备注")
    private String shipToAddressNote;

    /**
     * 收货地址是否为住宅
     */
    @Schema(description = "收货地址是否为住宅")
    private Boolean shipToAddressIsResidential;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * BOL文件数据
     */
    @Schema(description = "BOL文件数据")
    private String bolFileFileData;

    /**
     * BOL文件扩展名
     */
    @Schema(description = "BOL文件扩展名")
    private String bolFileFileExtension;

    /**
     * BOL文件类型
     */
    @Schema(description = "BOL文件类型")
    private String bolFileFileType;

    /**
     * BOL文件类型
     */
    @Schema(description = "BOL文件类型集合")
    @Condition(value = Keyword.IN, fields = {"bolFileFileType"})
    private List<String> bolFileFileTypeList;

    /**
     * 承运人代码
     */
    @Schema(description = "承运人代码")
    private String carrierCode;

    /**
     * 承运人名称
     */
    @Schema(description = "承运人名称")
    private String carrierName;

    /**
     * Carton数量
     */
    @Schema(description = "Carton数量")
    private Integer cartonQty;

    /**
     * 确认的运输类型
     */
    @Schema(description = "确认的运输类型")
    private String confirmedShipmentType;

    /**
     * 确认的运输类型
     */
    @Schema(description = "确认的运输类型集合")
    @Condition(value = Keyword.IN, fields = {"confirmedShipmentType"})
    private List<String> confirmedShipmentTypeList;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 托盘数量
     */
    @Schema(description = "托盘数量")
    private Integer palletQty;

    /**
     * otb请求id
     */
    @Schema(description = "otb请求id")
    private Long otbRequestId;

    /**
     * 请求提货日期
     */
    @Schema(description = "请求提货日期")
    private LocalDateTime requestPickupDate;
    /**
     * 请求提货日期开始
     */
    @Schema(description = "请求提货日期开始")
    @Condition(Keyword.GE)
    private LocalDateTime requestPickupDateStart;
    /**
     * 请求提货日期结束
     */
    @Schema(description = "请求提货日期结束")
    @Condition(Keyword.LE)
    private LocalDateTime requestPickupDateEnd;

    /**
     * 计划提货日期
     */
    @Schema(description = "计划提货日期")
    private LocalDateTime schedulePickupDate;
    /**
     * 计划提货日期开始
     */
    @Schema(description = "计划提货日期开始")
    @Condition(Keyword.GE)
    private LocalDateTime schedulePickupDateStart;
    /**
     * 计划提货日期结束
     */
    @Schema(description = "计划提货日期结束")
    @Condition(Keyword.LE)
    private LocalDateTime schedulePickupDateEnd;

    /**
     * otb装运id
     */
    @Schema(description = "otb装运id")
    private Long otbShipmentId;

    /**
     * 运输参考编号
     */
    @Schema(description = "运输参考编号")
    private String otbShipmentRefNum;

    /**
     * 运输参考编号
     */
    @Schema(description = "运输参考编号集合")
    @Condition(value = Keyword.IN, fields = {"otbShipmentRefNum"})
    private List<String> otbShipmentRefNumList;

    /**
     * 可堆叠托盘数量
     */
    @Schema(description = "可堆叠托盘数量")
    private Integer stackedPalletQty;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private Set<String> refNumList;

    /**
     * BOL编号
     */
    @Schema(description = "BOL编号")
    private String bolNum;

    /**
     * BOL编号
     */
    @Schema(description = "BOL编号集合")
    @Condition(value = Keyword.IN, fields = {"bolNum"})
    private List<String> bolNumList;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司集合")
    @Condition(value = Keyword.IN, fields = {"shipCarrier"})
    private List<String> shipCarrierList;

    /**
     * 运输方式
     */
    @Schema(description = "运输方式")
    private String shipMethod;

    /**
     * 运输方式
     */
    @Schema(description = "运输方式集合")
    @Condition(value = Keyword.IN, fields = {"shipMethod"})
    private List<String> shipMethodList;

    /**
     * 快递Api配置RefNum
     */
    @Schema(description = "快递Api配置RefNum")
    private String shipApiProfileRefNum;

    /**
     * 快递Api配置RefNum
     */
    @Schema(description = "快递Api配置RefNum集合")
    @Condition(value = Keyword.IN, fields = {"shipApiProfileRefNum"})
    private List<String> shipApiProfileRefNumList;

    /**
     * 路由指令状态
     */
    @Schema(description = "路由指令状态")
    private String otbRoutingInstructionStatus;

    /**
     * 路由指令状态
     */
    @Schema(description = "路由指令状态集合")
    @Condition(value = Keyword.IN, fields = {"otbRoutingInstructionStatus"})
    private List<String> otbRoutingInstructionStatusList;

    /**
     * 托盘文件数据
     */
    @Schema(description = "托盘文件数据")
    private String otbPalletFileFileData;

    /**
     * 托盘文件扩展名
     */
    @Schema(description = "托盘文件扩展名")
    private String otbPalletFileFileExtension;

    /**
     * 托盘文件类型
     */
    @Schema(description = "托盘文件类型")
    private String otbPalletFileFileType;

    /**
     * 托盘文件类型
     */
    @Schema(description = "托盘文件类型集合")
    @Condition(value = Keyword.IN, fields = {"otbPalletFileFileType"})
    private List<String> otbPalletFileFileTypeList;

    /**
     * BOL文件纸张类型
     */
    @Schema(description = "BOL文件纸张类型")
    private String otbBolFilePaperType;

    /**
     * BOL文件纸张类型
     */
    @Schema(description = "BOL文件纸张类型集合")
    @Condition(value = Keyword.IN, fields = {"otbBolFilePaperType"})
    private List<String> otbBolFilePaperTypeList;

    /**
     * 托盘文件纸张类型
     */
    @Schema(description = "托盘文件纸张类型")
    private String otbPalletFilePaperType;

    /**
     * 托盘文件纸张类型
     */
    @Schema(description = "托盘文件纸张类型集合")
    @Condition(value = Keyword.IN, fields = {"otbPalletFilePaperType"})
    private List<String> otbPalletFilePaperTypeList;


}