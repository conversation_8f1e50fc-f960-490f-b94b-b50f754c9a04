package cn.need.cloud.biz.model.vo.otb.page;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * OTB请求详情 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTB请求详情 vo对象")
public class OtbRequestDetailPageVO implements Serializable {


    @Serial
    private static final long serialVersionUID = -575320335334805319L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * otb请求id
     */
    @Schema(description = "otb请求id")
    private Long otbRequestId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    private String hazmatVersionRefNum;

    /**
     * 渠道要求的需要贴的产品标识码
     */
    @Schema(description = "渠道要求的需要贴的产品标识码")
    private String productBarcode;

}