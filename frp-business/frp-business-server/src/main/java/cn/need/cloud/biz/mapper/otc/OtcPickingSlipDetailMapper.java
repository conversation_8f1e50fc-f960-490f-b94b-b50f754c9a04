package cn.need.cloud.biz.mapper.otc;

import cn.need.cloud.biz.model.entity.otc.OtcPickingSlipDetail;
import cn.need.cloud.biz.model.query.otc.pickingslip.OtcPickingSlipDetailQuery;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPickingSlipDetailPickVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPickingSlipNoSummaryVO;
import cn.need.cloud.biz.model.vo.page.OtcPickingSlipDetailPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * OTC预拣货单详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtcPickingSlipDetailMapper extends SuperMapper<OtcPickingSlipDetail> {

    /**
     * 根据条件获取OTC预拣货单详情列表
     *
     * @param query 查询条件
     * @return OTC预拣货单详情集合
     */
    default List<OtcPickingSlipDetailPageVO> listByQuery(OtcPickingSlipDetailQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取OTC预拣货单详情分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return OTC预拣货单详情集合
     */
    List<OtcPickingSlipDetailPageVO> listByQuery(@Param("qo") OtcPickingSlipDetailQuery query, @Param("page") Page<?> page);

    /**
     * summaryPrint 拣货单详情汇总
     *
     * @param pickingSlipIds 拣货单id
     * @return /
     */
    List<OtcPickingSlipNoSummaryVO> summaryPrint(@Param("qo") List<Long> pickingSlipIds);

    /**
     * 拣货合并更新
     *
     * @param beforeMergeData 更新merge的数据集合
     * @return 成功
     */
    boolean pickUpdate(@Param("qo") List<OtcPickingSlipDetailPickVO> beforeMergeData);
}