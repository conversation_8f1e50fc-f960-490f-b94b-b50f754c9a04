package cn.need.cloud.biz.model.vo.base.putawayslip;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTC上架单 分页列表VO对象
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PutawaySlipPageVO extends BaseSuperVO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String putawaySlipStatus;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * 类型
     */
    @Schema(description = "类型")
    private String putawaySlipType;

    /**
     * 编码
     */
    @Schema(description = "编码")
    private String refNum;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

}