package cn.need.cloud.biz.model.query.otc.workorder.prep;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;


/**
 * OTC预提工单 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTC预提工单 query对象")
public class OtcPrepWorkorderQuery extends SuperQuery {

    /**
     * 主键id
     */
    @Schema(description = "主键id集合")
    private List<Long> idList;

    /**
     * Prep拣货单id
     */
    @Schema(description = "Prep拣货单id")
    private Long otcPrepPickingSlipId;

    /**
     * 预工单状态
     */
    @Schema(description = "预工单状态")
    private String prepWorkorderStatus;

    /**
     * 预工单状态
     */
    @Schema(description = "预工单状态集合")
    private List<String> prepWorkorderStatusList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private List<String> refNumList;

    /**
     * 请求单唯一编码集合
     */
    @Schema(description = "请求单唯一标识码集合")
    private List<String> requestRefNumList;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 工单产品类型
     */
    @Schema(description = "工单产品类型")
    private List<String> workOrderProductTypeList;

    /**
     * 预工单类型
     */
    @Schema(description = "预工单类型集合")
    @Condition(value = Keyword.IN, fields = {"prepWorkorderType"})
    private List<String> prepWorkorderTypeList;

    /**
     * 订单类型
     */
    @Schema(description = "订单类型集合")
    private List<String> orderTypeList;

    /**
     * 渠道集合
     */
    @Schema(description = "渠道集合")
    private List<String> channelList;

    /**
     * 拣货到那里
     */
    @Schema(description = "拣货到那里集合")
    @Condition(value = Keyword.IN, fields = {"pickToStation"})
    private List<String> pickToStationList;

    /**
     * 发货承运方
     */
    @Schema(description = "发货承运方")
    private List<String> shipCarrierList;

    /**
     * 发货方式
     */
    @Schema(description = "发货方式")
    private List<String> shipMethodList;

    /**
     * 最晚发货时间起
     */
    @Schema(description = "最晚发货时间起")
    private LocalDateTime lastShipDateStart;

    /**
     * 最晚发货时间至
     */
    @Schema(description = "最晚发货时间起")
    private LocalDateTime lastShipDateEnd;

    /**
     * 预发货到c端工单id
     */
    @Schema(description = "预发货到c端工单id")
    private Long otcPrepWorkorderId;

    /**
     * 预发货到c端工单id集合
     */
    @Schema(description = "预发货到c端工单id集合")
    private List<Long> otcPrepWorkorderIdList;

    /**
     * Prep工单产品类型
     */
    @Schema(description = "Prep工单产品类型集合")
    @Condition(value = Keyword.IN, fields = {"prepWorkorderProductType"})
    private List<String> prepWorkorderProductTypeList;

    @Schema(description = "处理类型")
    private String processType;

}