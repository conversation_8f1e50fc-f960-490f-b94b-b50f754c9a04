package cn.need.cloud.biz.model.vo.otc.pickingslip;

import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.otc.*;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReleaseLockedParam;
import cn.need.cloud.biz.model.query.otc.pickingslip.prep.OtcPrepPickingSlipPutAwayQuery;
import cn.need.cloud.biz.model.vo.otb.workorder.OtcPrepWorkorderPutAwayVO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/***
 * 上架上下文信息
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
@Data
public class OtcPrepPickingSlipPutAwayContextVO {

    /**
     * 上架参数信息
     */
    private OtcPrepPickingSlipPutAwayQuery query;

    /**
     * 上架的Prep拣货单
     */
    private OtcPrepPickingSlip prepPickingSlip;

    /**
     * 上架的Prep工单
     */
    private List<OtcPrepWorkorder> putAwayPrepWorkOrderList = new ArrayList<>();

    /**
     * 该拣货单下的Prep工单
     */
    private List<OtcPrepWorkorder> prepWorkOrderList = new ArrayList<>();

    /**
     * 库存预留释放库存参数
     */
    private List<InventoryReleaseLockedParam> reserveInventoryReleaseList = new ArrayList<>();

    /**
     * 工单信息
     */
    private List<OtcWorkorder> workorderList = new ArrayList<>();

    /**
     * 上架库位信息
     */
    private BinLocationDetail putAwayBinLocationDetail;

    /**
     * 拣货单详情
     */
    private List<OtcPrepPickingSlipDetail> prepPickingSlipDetailList;

    /**
     * 更新的工单详情
     */
    private List<OtcPrepWorkorderDetail> putAwayPrepWorkorderDetailList;


    /**
     * 所有Prep工单详情
     */
    private Map<Long, List<OtcPrepWorkorderDetail>> prepDetailGroupByWkMap;

    /**
     * 上架Prep工单的信息
     */
    private List<OtcPrepWorkorderPutAwayVO> prepWorkorderPutawayList = new ArrayList<>();
}
