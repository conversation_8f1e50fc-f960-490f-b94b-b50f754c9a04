package cn.need.cloud.biz.service.otb.pickingslip.impl;

import cn.need.cloud.biz.cache.bean.ProductCache;
import cn.need.cloud.biz.client.constant.enums.base.ReLabelStatus;
import cn.need.cloud.biz.converter.otb.OtbPickingSlipDetailConverter;
import cn.need.cloud.biz.mapper.otb.OtbPickingSlipDetailMapper;
import cn.need.cloud.biz.model.entity.binlocation.BinLocation;
import cn.need.cloud.biz.model.entity.otb.OtbPickingSlipDetail;
import cn.need.cloud.biz.model.query.otb.pickingslip.OtbPickingSlipMarkReLabelPrintByBarcodeQuery;
import cn.need.cloud.biz.model.query.otb.pickingslip.OtbPickingSlipProductPickQuery;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipDetailPickVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipDetailVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipSummaryVO;
import cn.need.cloud.biz.service.binlocation.BinLocationService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPickingSlipDetailService;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.support.convert.Converters;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * otb拣货单详情 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtbPickingSlipDetailServiceImpl extends SuperServiceImpl<OtbPickingSlipDetailMapper, OtbPickingSlipDetail> implements OtbPickingSlipDetailService {

    @Resource
    private BinLocationService binLocationService;

    @Override
    public List<OtbPickingSlipDetailVO> listByOtbPickingSlipId(Long otbPickingSlipId) {
        List<OtbPickingSlipDetail> list = lambdaQuery().eq(OtbPickingSlipDetail::getOtbPickingSlipId, otbPickingSlipId).list();
        return Converters.get(OtbPickingSlipDetailConverter.class).toVO(list);
    }

    @Override
    public List<OtbPickingSlipDetailVO> listByPickSlipId(Long id) {
        // 根据拣货单获取拣货单详情集合
        List<OtbPickingSlipDetail> list = lambdaQuery()
                .eq(OtbPickingSlipDetail::getOtbPickingSlipId, id)
                .list();
        // 转换为vo对象
        // 返回拣货单详情vo对象
        return Converters.get(OtbPickingSlipDetailConverter.class).toVO(list);
    }

    @Override
    public List<OtbPickingSlipSummaryVO> summary(List<Long> pickingSlipIdList) {
        return ObjectUtil.isEmpty(pickingSlipIdList)
                ? Collections.emptyList()
                : mapper.summary(pickingSlipIdList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<OtbPickingSlipDetailPickVO> pick(List<OtbPickingSlipProductPickQuery> pickList) {
        if (ObjectUtil.isEmpty(pickList)) {
            return Collections.emptyList();
        }
        // 每个详情拣货数量
        Map<Long, Integer> detailPickQtyMap = pickList.stream()
                .collect(Collectors.groupingBy(OtbPickingSlipProductPickQuery::getOtbPickingSlipDetailId,
                        Collectors.summingInt(OtbPickingSlipProductPickQuery::getQty))
                );

        List<Long> psDetailIdList = StreamUtils.distinctMap(pickList, OtbPickingSlipProductPickQuery::getOtbPickingSlipDetailId);
        // 获取拣货单详情
        List<OtbPickingSlipDetail> detailList = this.listByIds(psDetailIdList);
        Validate.notEmpty(detailList, "Pick details cannot found");

        // 设置ReLabel
        this.setReLabelStatus(detailList);
        // 校验拣货数量
        List<OtbPickingSlipDetailPickVO> pickDetailList = detailList.stream()
                .peek(obj -> {
                    int pickQty = detailPickQtyMap.getOrDefault(obj.getId(), 0);
                    int canPickQty = obj.getQty() - obj.getPickedQty();
                    // 拣货数量不能大于拣货数量
                    Validate.isTrue(canPickQty >= pickQty,
                            "{} The number of items to be picked cannot be greater than the number of items that can be picked",
                            obj.getId()
                    );
                })
                .map(obj -> {
                    OtbPickingSlipDetailPickVO pick = BeanUtil.copyNew(obj, OtbPickingSlipDetailPickVO.class);
                    // 设置拣货前的数量
                    pick.setPickedBeforeQty(pick.getPickedQty());
                    // 拣货后
                    pick.setPickedQty(pick.getPickedQty() + detailPickQtyMap.getOrDefault(obj.getId(), 0));

                    // 绑定锁信息
                    pick.setRefTableId(obj.getId());
                    pick.setRefTableRefNum(String.valueOf(obj.getLineNum()));
                    pick.setRefTableName(OtbPickingSlipDetail.class.getSimpleName());
                    return pick;
                })
                .toList();

        // 更新拣货数量
        this.pickUpdate(pickDetailList);

        // 返回拣货信息
        return pickDetailList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean pickUpdate(List<OtbPickingSlipDetailPickVO> pickDetails) {
        if (ObjectUtil.isEmpty(pickDetails)) {
            return false;
        }

        List<OtbPickingSlipDetail> pickedList = pickDetails.stream()
                .filter(obj -> obj.getChangePickQty() > 0)
                .map(obj -> BeanUtil.copyNew(obj, OtbPickingSlipDetail.class))
                .toList();

        Validate.isTrue(this.updateBatch(pickedList) == pickedList.size(), "Update otb picking slip detail PickQty failed");
        return true;
    }

    @Override
    public boolean allPickedIgnoreDetailIdList(Long pickingSlipId, List<Long> pickingSlipDetailIdList) {
        List<OtbPickingSlipDetail> detailList = lambdaQuery()
                .eq(OtbPickingSlipDetail::getOtbPickingSlipId, pickingSlipId)
                .notIn(ObjectUtil.isNotEmpty(pickingSlipDetailIdList), IdModel::getId, pickingSlipDetailIdList)
                .list();
        return ObjectUtil.isEmpty(detailList)
                || detailList
                .stream()
                .allMatch(obj -> Objects.equals(obj.getPickedQty(), obj.getQty()));
    }

    @Override
    public List<OtbPickingSlipDetail> findMarkReLabelDetails(OtbPickingSlipMarkReLabelPrintByBarcodeQuery query) {
        return lambdaQuery()
                .eq(OtbPickingSlipDetail::getOtbPickingSlipId, query.getOtbPickingSlipId())
                .eq(OtbPickingSlipDetail::getProductBarcode, query.getProductBarcode())
                .eq(OtbPickingSlipDetail::getProductId, query.getProductId())
                .eq(OtbPickingSlipDetail::getProductChannelSku, query.getProductChannelSku())
                .list();
    }

    @Override
    public List<OtbPickingSlipDetail> listByPickingSlipId(Long pickingSlipId) {
        if (ObjectUtil.isNotEmpty(pickingSlipId)) {
            return lambdaQuery()
                    .eq(OtbPickingSlipDetail::getOtbPickingSlipId, pickingSlipId)
                    .list();
        }
        return List.of();
    }

    @Override
    public List<OtbPickingSlipDetail> listByPickingSlipIds(List<Long> pickingSlipIdList) {
        if (ObjectUtil.isEmpty(pickingSlipIdList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(OtbPickingSlipDetail::getOtbPickingSlipId, pickingSlipIdList)
                .list();
    }

    /**
     * 设置ReLabel状态
     *
     * @param detailList 详情集合
     */
    private void setReLabelStatus(List<OtbPickingSlipDetail> detailList) {
        if (ObjectUtil.isEmpty(detailList)) {
            return;
        }

        // 虚拟库位
        Map<Long, BinLocation> virtualBinLocationMap = binLocationService.allVirtualBinLocationList();

        // 产品
        List<ProductCache> productCacheList = ProductCacheUtil.listByIds(StreamUtils.distinctMap(detailList, OtbPickingSlipDetail::getProductId));
        Map<Long, ProductCache> productCacheMap = StreamUtils.toMap(productCacheList, ProductCache::getId);

        // 设置ReLabel
        detailList.forEach(obj -> {
            ProductCache product = productCacheMap.get(obj.getProductId());
            boolean isSame = Arrays.asList(product.getSupplierSku(), product.getUpc()).contains(obj.getProductBarcode());
            // 如果是在 Prep 库位，则不用重新打标签
            boolean isVirtual = virtualBinLocationMap.containsKey(obj.getBinLocationId());
            // 设置状态
            obj.setRelabelStatus(isSame || isVirtual
                    ? ReLabelStatus.NONE.getStatus()
                    : ReLabelStatus.NEW.getStatus());
        });
    }

}
