package cn.need.cloud.biz.model.entity.otb;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * <p>
 * OTB包裹详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otb_package_detail")
public class OtbPackageDetail extends SuperModel {


    @Serial
    private static final long serialVersionUID = 1715347684062334334L;
    /**
     * 行序号
     */
    @TableField("line_num")
    private Integer lineNum;

    /**
     * 数量
     */
    @TableField("qty")
    private Integer qty;


    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @TableField("hazmat_version_ref_num")
    private String hazmatVersionRefNum;

    /**
     * otb包裹id
     */
    @TableField("otb_package_id")
    private Long otbPackageId;

    /**
     * 渠道要求的需要贴的产品标识码
     */
    @TableField("product_barcode")
    private String productBarcode;

    /**
     * 渠道sku
     */
    @TableField("product_channel_sku")
    private String productChannelSku;
}
