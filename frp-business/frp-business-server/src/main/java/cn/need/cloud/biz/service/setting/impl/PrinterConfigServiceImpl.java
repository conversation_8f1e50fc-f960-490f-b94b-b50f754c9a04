package cn.need.cloud.biz.service.setting.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.setting.PrinterConfigConverter;
import cn.need.cloud.biz.mapper.setting.PrinterConfigMapper;
import cn.need.cloud.biz.model.entity.setting.PrinterConfig;
import cn.need.cloud.biz.model.param.setting.create.PrinterConfigCreateParam;
import cn.need.cloud.biz.model.param.setting.update.PrinterConfigUpdateParam;
import cn.need.cloud.biz.model.query.setting.PrinterConfigQuery;
import cn.need.cloud.biz.model.vo.page.PrinterConfigPageVO;
import cn.need.cloud.biz.model.vo.setting.PrinterConfigVO;
import cn.need.cloud.biz.service.setting.PrinterConfigService;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 打印配置 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class PrinterConfigServiceImpl extends SuperServiceImpl<PrinterConfigMapper, PrinterConfig> implements PrinterConfigService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(PrinterConfigCreateParam createParam) {
        // 检查传入打印配置参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取打印配置转换器实例，用于将打印配置参数对象转换为实体对象
        PrinterConfigConverter converter = Converters.get(PrinterConfigConverter.class);

        // 将打印配置参数对象转换为实体对象并初始化
        PrinterConfig entity = initPrinterConfig(converter.toEntity(createParam));

        // 插入打印配置实体对象到数据库
        super.insert(entity);

        // 返回打印配置ID
        return entity.getId();
    }


    /**
     * 初始化打印配置对象
     * 此方法用于设置打印配置对象的必要参数，确保其处于有效状态
     *
     * @param entity 打印配置对象，不应为空
     * @return 返回初始化后的打印配置
     * @throws BusinessException 如果传入的打印配置为空，则抛出此异常
     */
    private PrinterConfig initPrinterConfig(PrinterConfig entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("PrinterConfig cannot be empty");
        }


        // 返回初始化后的配置对象
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(PrinterConfigUpdateParam updateParam) {
        // 检查传入打印配置参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取打印配置转换器实例，用于将打印配置参数对象转换为实体对象
        PrinterConfigConverter converter = Converters.get(PrinterConfigConverter.class);

        // 将打印配置参数对象转换为实体对象
        PrinterConfig entity = converter.toEntity(updateParam);

        // 执行更新打印配置操作
        return super.update(entity);

    }

    @Override
    public List<PrinterConfigPageVO> listByQuery(PrinterConfigQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<PrinterConfigPageVO> pageByQuery(PageSearch<PrinterConfigQuery> search) {
        Page<PrinterConfig> page = Conditions.page(search, entityClass);
        page.addOrder(new OrderItem());
        List<PrinterConfigPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public PrinterConfigVO detailById(Long id) {
        PrinterConfig entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in PrinterConfig");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "PrinterConfig", id));
        }
        return buildPrinterConfigVO(entity);
    }


    /**
     * 构建打印配置VO对象
     *
     * @param entity 打印配置对象
     * @return 返回包含详细信息的打印配置VO对象
     */
    private PrinterConfigVO buildPrinterConfigVO(PrinterConfig entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的打印配置VO对象
        return Converters.get(PrinterConfigConverter.class).toVO(entity);
    }

}
