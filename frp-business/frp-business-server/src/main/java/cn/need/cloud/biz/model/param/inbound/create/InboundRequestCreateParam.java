package cn.need.cloud.biz.model.param.inbound.create;

import cn.need.cloud.biz.client.constant.RegexConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 入库请求 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "入库请求 vo对象")
public class InboundRequestCreateParam implements Serializable {

    /**
     * 运输方式类型
     */
    @Schema(description = "运输方式类型")
    private String transportMethodType;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码")
    @Pattern(regexp = RegexConstant.CHARACTER_REGEX, message = RegexConstant.CHARACTER_REGEX_MESSAGE)
    private String requestRefNum;

    /**
     * 预计到达日期
     */
    @Schema(description = "预计到达日期")
    private LocalDateTime estimateArrivalDate;

    /**
     * 快递号
     */
    @Schema(description = "快递号")
    private String trackingNum;

    /**
     * 发件人姓名
     */
    @Schema(description = "发件人姓名")
    private String fromAddressName;

    /**
     * 发件人公司
     */
    @Schema(description = "发件人公司")
    private String fromAddressCompany;

    /**
     * 发件人国家
     */
    @Schema(description = "发件人国家")
    private String fromAddressCountry;

    /**
     * 发件人州
     */
    @Schema(description = "发件人州")
    private String fromAddressState;

    /**
     * 发件人城市
     */
    @Schema(description = "发件人城市")
    private String fromAddressCity;

    /**
     * 发件人邮编
     */
    @Schema(description = "发件人邮编")
    private String fromAddressZipCode;

    /**
     * 发件人地址1
     */
    @Schema(description = "发件人地址1")
    private String fromAddressAddr1;

    /**
     * 发件人地址2
     */
    @Schema(description = "发件人地址2")
    private String fromAddressAddr2;

    /**
     * 发件人地址3
     */
    @Schema(description = "发件人地址3")
    private String fromAddressAddr3;

    /**
     * 发件人邮箱
     */
    @Schema(description = "发件人邮箱")
    private String fromAddressEmail;

    /**
     * 发件人电话
     */
    @Schema(description = "发件人电话")
    private String fromAddressPhone;

    /**
     * 发件人备注
     */
    @Schema(description = "发件人备注")
    private String fromAddressNote;

    /**
     * 发件人地址是否为住宅
     */
    @Schema(description = "发件人地址是否为住宅")
    private Boolean fromAddressIsResidential;


    /**
     * 交易伙伴ID
     */
    @Schema(description = "交易伙伴ID")
    private Long transactionPartnerId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 容器类型
     */
    @Schema(description = "容器类型")
    private String containerType;

    /**
     * 入库请求单详情
     */
    @Schema(description = "入库请求单详情")
    private List<InboundRequestDetailCreateParam> details;

}