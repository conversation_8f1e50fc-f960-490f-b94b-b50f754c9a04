package cn.need.cloud.biz.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.core.json.PackageVersion;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.TimeZone;


/***
 * Json 工具类
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public class JsonUtil {


    public static final String PATTERN_DATETIME = "yyyy-MM-dd HH:mm:ss";
    public static final String PATTERN_DATE = "yyyy-MM-dd";
    public static final String PATTERN_TIME = "HH:mm:ss";
    private static final ObjectMapper INSTANCE = new ObjectMapper();

    static {
        // 去掉默认的时间戳格式
        INSTANCE.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        // 设置为中国上海时区
        INSTANCE.setTimeZone(TimeZone.getTimeZone(ZoneId.systemDefault()));
        // 序列化时，日期的统一格式
        INSTANCE.setDateFormat(new SimpleDateFormat(PATTERN_DATETIME, Locale.CHINA));
        // 单引号
        INSTANCE.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        // 允许JSON字符串包含非引号控制字符（值小于32的ASCII字符，包含制表符和换行符）
        INSTANCE.configure(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true);
        INSTANCE.configure(JsonReadFeature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER.mappedFeature(), true);
        // 失败处理
        INSTANCE.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        INSTANCE.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 单引号处理
        INSTANCE.configure(JsonReadFeature.ALLOW_SINGLE_QUOTES.mappedFeature(), true);
        // 日期格式化
        INSTANCE.registerModule(JavaTimeModule.INSTANCE);
        INSTANCE.findAndRegisterModules();
    }

    private JsonUtil() {
    }

    /**
     * json反序列化成对象
     *
     * @param json  json字符串
     * @param clazz 对象class
     * @return /
     */
    public static <T> T toObject(String json, Class<T> clazz) {
        try {
            return getInstance().readValue(json, clazz);
        } catch (Exception e) {
            throw new RuntimeException("Error converting JSON to object", e);
        }
    }


    /**
     * 将对象序列化成json字符串
     *
     * @param value javaBean
     * @return jsonString json字符串
     */
    public static <T> String toJson(T value) {
        try {
            return getInstance().writeValueAsString(value);
        } catch (Exception e) {
            throw new RuntimeException("Error converting Object to JSON", e);
        }
    }

    /**
     * 实现JSON序列化的 jackson 单例对象
     *
     * @return /
     */
    private static ObjectMapper getInstance() {
        return INSTANCE;
    }

    /***
     * Java8时间类型序列化器
     *
     * <AUTHOR>
     * @since 2024-12-25
     */
    public static class JavaTimeModule extends SimpleModule {

        public static final JavaTimeModule INSTANCE = new JavaTimeModule();
        public static final DateTimeFormatter DATETIME_FORMAT = DateTimeFormatter.ofPattern(PATTERN_DATETIME);
        public static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern(PATTERN_DATE);
        public static final DateTimeFormatter TIME_FORMAT = DateTimeFormatter.ofPattern(PATTERN_TIME);

        public JavaTimeModule() {
            super(PackageVersion.VERSION);
            this.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DATETIME_FORMAT));
            this.addDeserializer(LocalDate.class, new LocalDateDeserializer(DATE_FORMAT));
            this.addDeserializer(LocalTime.class, new LocalTimeDeserializer(TIME_FORMAT));

            this.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DATETIME_FORMAT));
            this.addSerializer(LocalDate.class, new LocalDateSerializer(DATE_FORMAT));
            this.addSerializer(LocalTime.class, new LocalTimeSerializer(TIME_FORMAT));
        }
    }
}
