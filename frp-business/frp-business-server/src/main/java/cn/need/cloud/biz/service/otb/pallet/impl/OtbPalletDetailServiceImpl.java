package cn.need.cloud.biz.service.otb.pallet.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.otb.OtbPalletDetailConverter;
import cn.need.cloud.biz.mapper.otb.OtbPalletDetailMapper;
import cn.need.cloud.biz.model.bo.otb.OtbBuildPalletContextBo;
import cn.need.cloud.biz.model.entity.otb.OtbPackageDetail;
import cn.need.cloud.biz.model.entity.otb.OtbPallet;
import cn.need.cloud.biz.model.entity.otb.OtbPalletDetail;
import cn.need.cloud.biz.model.query.otb.pallet.OtbPalletDetailQuery;
import cn.need.cloud.biz.model.vo.otb.pallet.OtbPalletDetailVO;
import cn.need.cloud.biz.model.vo.page.OtbPalletDetailPageVO;
import cn.need.cloud.biz.service.otb.pallet.OtbPalletDetailService;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * OTB托盘详情服务实现类
 * </p>
 * <p>
 * 该类实现了OTB托盘详情（Pallet Detail）的核心业务逻辑，负责托盘详情的创建、查询和管理。
 * 主要功能包括：
 * 1. 托盘详情的创建，包括从包装详情中生成托盘详情
 * 2. 托盘详情的查询功能，支持根据ID、托盘ID等多种方式查询
 * 3. 托盘详情的分页查询和详情展示
 * </p>
 * <p>
 * 托盘详情记录了托盘中包含的各个产品的信息，包括产品ID、条码、渠道SKU和数量等。
 * 它是托盘的重要组成部分，用于记录托盘中的产品明细。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtbPalletDetailServiceImpl extends SuperServiceImpl<OtbPalletDetailMapper, OtbPalletDetail> implements OtbPalletDetailService {


    /**
     * 根据查询条件获取托盘详情列表
     * <p>
     * 该方法根据指定的查询条件获取托盘详情列表，不带分页。
     * 查询条件可以包括托盘ID、产品ID、产品条码等多种条件。
     * </p>
     *
     * @param query 托盘详情查询条件对象
     * @return 托盘详情列表，包含托盘详情基本信息和关联的产品信息
     */
    @Override
    public List<OtbPalletDetailPageVO> listByQuery(OtbPalletDetailQuery query) {
        return mapper.listByQuery(query);
    }

    /**
     * 根据查询条件分页获取托盘详情列表
     * <p>
     * 该方法根据指定的查询条件和分页参数获取托盘详情列表。
     * 主要流程包括：
     * 1. 根据分页参数创建分页对象
     * 2. 执行分页查询并获取数据列表
     * 3. 返回带分页信息的数据列表
     * </p>
     *
     * @param search 包含查询条件和分页参数的对象
     * @return 带分页信息的托盘详情列表，包含托盘详情基本信息和关联的产品信息
     */
    @Override
    public PageData<OtbPalletDetailPageVO> pageByQuery(PageSearch<OtbPalletDetailQuery> search) {
        Page<OtbPalletDetail> page = Conditions.page(search, entityClass);
        List<OtbPalletDetailPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取托盘详情
     * <p>
     * 该方法根据托盘详情ID获取托盘详情的详细信息。
     * 如果指定ID的托盘详情不存在，则抛出业务异常。
     * </p>
     *
     * @param id 托盘详情ID
     * @return 托盘详情VO对象，包含托盘详情基本信息和关联的产品信息
     * @throws BusinessException 如果托盘详情不存在，则抛出业务异常
     */
    @Override
    public OtbPalletDetailVO detailById(Long id) {
        OtbPalletDetail entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in OtbPalletDetail");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtbPalletDetail", id));
        }
        return buildOtbPalletDetailVO(entity);
    }


    /**
     * 根据托盘ID获取托盘详情列表
     * <p>
     * 该方法根据托盘ID获取该托盘的所有详情记录。
     * 返回的是托盘详情的VO对象列表，包含产品信息和数量等。
     * </p>
     *
     * @param otbPalletId 托盘ID
     * @return 托盘详情VO对象列表，包含托盘详情基本信息和关联的产品信息
     */
    @Override
    public List<OtbPalletDetailVO> listByOtbPalletId(Long otbPalletId) {
        List<OtbPalletDetail> list = lambdaQuery().eq(OtbPalletDetail::getOtbPalletId, otbPalletId).list();
        return Converters.get(OtbPalletDetailConverter.class).toVO(list);
    }

    /**
     * 生成托盘详情
     * <p>
     * 该方法用于根据包装详情生成托盘详情。
     * 主要流程包括：
     * 1. 从上下文中获取托盘实体
     * 2. 将包装详情按产品ID、条码和渠道SKU进行分组
     * 3. 对每个分组构建一个托盘详情实体，并计算总数量
     * 4. 批量插入托盘详情实体
     * 5. 将生成的托盘详情列表设置到上下文中
     * </p>
     * <p>
     * 该方法将包装详情中的产品信息转换为托盘详情，并将相同产品的数量进行汇总。
     * </p>
     *
     * @param contextBo 托盘构建上下文对象，包含托盘实体和包装详情列表
     *                  <p>
     *                                                                                                                        TODO: 考虑使用批量插入优化性能，减少数据库访问
     */
    @Override
    public void generateDetail(OtbBuildPalletContextBo contextBo) {
        //获取打托单
        OtbPallet otbPallet = contextBo.getOtbPallet();
        //构建打托单详情集合
        List<OtbPalletDetail> list = contextBo.getOtbPackageDetailList().stream()
                .collect(Collectors.groupingBy(item -> StringUtil.format("{}/{}/{}", item.getProductId(), item.getProductBarcode(), item.getProductChannelSku())))
                .values()
                .stream()
                .map(item -> buildOtbPalletDetail(item, otbPallet))
                .toList();
        //持久化
        super.insertBatch(list);
        //添加打托详情
        contextBo.setOtbPalletDetailList(list);
    }

    /**
     * 根据托盘ID列表获取托盘详情列表
     * <p>
     * 该方法根据托盘ID列表获取这些托盘的所有详情记录。
     * 如果托盘ID列表为空，则抛出异常。
     * </p>
     *
     * @param otbPalletIdList 托盘ID列表
     * @return 托盘详情实体列表
     * @throws IllegalArgumentException 如果托盘ID列表为空，则抛出异常
     *                                  <p>
     *                                                                                                                                                                                                                                        TODO: 考虑当托盘ID列表过大时进行分批查询，避免数据库压力
     */
    @Override
    public List<OtbPalletDetail> listByPalletIdList(List<Long> otbPalletIdList) {
        Validate.notEmpty(otbPalletIdList, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "otbPalletIdList"));
        return lambdaQuery()
                .in(OtbPalletDetail::getOtbPalletId, otbPalletIdList)
                .list();
    }

    /**
     * 构建托盘详情实体
     * <p>
     * 该方法用于根据包装详情列表和托盘实体构建托盘详情实体。
     * 主要流程包括：
     * 1. 从包装详情列表中获取第一个包装详情，用于提取产品信息
     * 2. 计算包装详情列表中所有包装详情的数量之和
     * 3. 创建新的托盘详情实体，并设置托盘ID、数量、行号和产品信息
     * </p>
     * <p>
     * 该方法将相同产品的包装详情汇总为一个托盘详情，便于后续的托盘管理和打印。
     * </p>
     *
     * @param item      包装详情列表，包含相同产品的多个包装详情
     * @param otbPallet 托盘实体
     * @return 托盘详情实体
     * <p>
     * TODO: 考虑使用对象池或工厂模式创建托盘详情实体，减少对象创建开销
     */
    @NotNull
    private OtbPalletDetail buildOtbPalletDetail(List<OtbPackageDetail> item, OtbPallet otbPallet) {
        OtbPackageDetail otbPackageDetail = item.stream().findFirst().orElse(new OtbPackageDetail());
        int sum = item.stream().mapToInt(OtbPackageDetail::getQty).sum();
        OtbPalletDetail otbPalletDetail = new OtbPalletDetail();
        otbPalletDetail.setOtbPalletId(otbPallet.getId());
        otbPalletDetail.setQty(sum);
        otbPalletDetail.setLineNum(0);
        otbPalletDetail.setProductId(otbPackageDetail.getProductId());
        otbPalletDetail.setProductBarcode(otbPackageDetail.getProductBarcode());
        otbPalletDetail.setProductChannelSku(otbPackageDetail.getProductChannelSku());
        return otbPalletDetail;
    }

    /**
     * 构建OTB托盘详情VO对象
     * <p>
     * 该方法用于将托盘详情实体转换为托盘详情VO对象。
     * 如果输入的实体为空，则返回null。
     * </p>
     * <p>
     * 该方法使用转换器将实体对象转换为VO对象，便于向前端展示托盘详情信息。
     * </p>
     *
     * @param entity OTB托盘详情实体对象
     * @return 托盘详情VO对象，如果实体为空则返回null
     */
    private OtbPalletDetailVO buildOtbPalletDetailVO(OtbPalletDetail entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的OTB托盘详情VO对象
        return Converters.get(OtbPalletDetailConverter.class).toVO(entity);
    }

}
