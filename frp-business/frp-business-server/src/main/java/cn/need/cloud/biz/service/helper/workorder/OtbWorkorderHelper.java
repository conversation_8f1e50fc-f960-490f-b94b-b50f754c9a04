package cn.need.cloud.biz.service.helper.workorder;

import cn.need.cloud.biz.client.constant.enums.otb.OtbPrepWorkOrderEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcWorkorderStatusEnum;
import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorder;
import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorderDetail;
import cn.need.cloud.biz.model.entity.otb.OtbWorkorder;
import cn.need.cloud.biz.model.entity.otb.OtbWorkorderDetail;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbPrepWorkorderAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbWorkorderAuditLogHelper;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.model.IdModel;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 工单辅助类
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
public class OtbWorkorderHelper {

    /**
     * 重新刷新工单状态
     *
     * @param workorderList 工单
     * @param details       详情
     */
    public static void refreshStatus(List<OtbWorkorder> workorderList, List<OtbWorkorderDetail> details) {
        if (ObjectUtil.isEmpty(details)) {
            return;
        }
        var refreshMap = StreamUtils.toMap(workorderList, IdModel::getId);
        details.stream()
                .collect(Collectors.groupingBy(OtbWorkorderDetail::getOtbWorkorderId))
                .forEach((key, currentWkDetails) -> {
                    var workorder = refreshMap.get(key);

                    var oldStatus = workorder.getOtbWorkorderStatus();

                    // 全部上架 Shipped
                    var allShipped = currentWkDetails.stream().allMatch(obj -> Objects.equals(obj.getQty(), obj.getFinishQty()));
                    // 全部上架 ReadyToShip
                    var allReadyToShip = currentWkDetails.stream().allMatch(obj -> Objects.equals(obj.getQty(), obj.getShippedQty()));
                    // 全部拣货 Picked
                    var allPicked = currentWkDetails.stream().allMatch(obj -> Objects.equals(obj.getQty(), obj.getPickedQty()));
                    // 拣货 InPicking
                    var hasPick = details.stream().anyMatch(obj -> obj.getPickedQty() > 0);
                    workorder.setOtbWorkorderStatus(allShipped ? OtcWorkorderStatusEnum.SHIPPED.getStatus()
                            : allReadyToShip ? OtcWorkorderStatusEnum.READY_TO_SHIP.getStatus()
                            : allPicked ? OtcWorkorderStatusEnum.PICKED.getStatus()
                            : hasPick ? OtcWorkorderStatusEnum.IN_PICKING.getStatus()
                            : OtcWorkorderStatusEnum.BEGIN.getStatus()
                    );

                    // 记录日志
                    if (!Objects.equals(oldStatus, workorder.getOtbWorkorderStatus())) {
                        OtbWorkorderAuditLogHelper.recordLog(workorder);
                    }

                });
    }

    /**
     * 重新刷新工单状态
     *
     * @param prepWorkorderList 工单
     * @param prepDetails       详情
     */
    public static void refreshPrepStatus(List<OtbPrepWorkorder> prepWorkorderList, List<OtbPrepWorkorderDetail> prepDetails) {
        var prepWorkorderMap = StreamUtils.toMap(prepWorkorderList, IdModel::getId);
        var detailsGroupMap = StreamUtils.groupBy(prepDetails, OtbPrepWorkorderDetail::getOtbPrepWorkorderId);
        detailsGroupMap.forEach((prepWorkorderId, details) -> {
            var prepWorkorder = prepWorkorderMap.get(prepWorkorderId);

            // 全部上架 Putaway
            var allPutaway = details.stream().allMatch(obj -> Objects.equals(obj.getPutawayQty(), obj.getQty()));
            // 全部拣货 Picked
            var allPicked = details.stream().allMatch(obj -> Objects.equals(obj.getPickedQty(), obj.getQty()));
            // 拣货 InPicking
            var hasPick = details.stream().anyMatch(obj -> obj.getPickedQty() != 0);

            var oldStatus = prepWorkorder.getOtbPrepWorkorderStatus();
            prepWorkorder.setOtbPrepWorkorderStatus(allPutaway ? OtbPrepWorkOrderEnum.PROCESSED.getStatus()
                    : allPicked ? OtbPrepWorkOrderEnum.PICKED.getStatus()
                    : hasPick ? OtbPrepWorkOrderEnum.IN_PICKING.getStatus()
                    : OtbPrepWorkOrderEnum.NEW.getStatus()
            );

            // 记录日志
            if (!Objects.equals(oldStatus, prepWorkorder.getOtbPrepWorkorderStatus())) {
                OtbPrepWorkorderAuditLogHelper.recordLog(prepWorkorder);
            }
        });
    }
}
