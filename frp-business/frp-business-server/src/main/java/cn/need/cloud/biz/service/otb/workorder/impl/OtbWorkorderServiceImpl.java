package cn.need.cloud.biz.service.otb.workorder.impl;

import cn.hutool.core.collection.CollUtil;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.RequestStatusEnum;
import cn.need.cloud.biz.client.constant.enums.inventory.WorkOrderPrepStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbRequestShipmentStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbWorkorderEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbWorkorderTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otb.WorkorderProductTypeEnum;
import cn.need.cloud.biz.client.constant.workorder.WorkorderLogConstant;
import cn.need.cloud.biz.converter.otb.OtbWorkorderConverter;
import cn.need.cloud.biz.mapper.otb.OtbWorkorderMapper;
import cn.need.cloud.biz.model.bo.common.RefTableBO;
import cn.need.cloud.biz.model.entity.base.RefNumModel;
import cn.need.cloud.biz.model.entity.otb.*;
import cn.need.cloud.biz.model.query.base.BaseBinLocationQuery;
import cn.need.cloud.biz.model.query.otb.pickingslip.OtbPickingSlipFilterBuildQuery;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkOrderListQuery;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkorderAdjustShipQtyDetailQuery;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkorderAdjustShipQtyQuery;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkorderQuery;
import cn.need.cloud.biz.model.vo.base.BasePickVO;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipDetailPickVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipPickContextVO;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestDetailVO;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestVO;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbWorkorderDetailPickVO;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbWorkorderDetailVO;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbWorkorderVO;
import cn.need.cloud.biz.model.vo.page.OtbWorkorderPageVO;
import cn.need.cloud.biz.model.vo.product.ProductVersionVO;
import cn.need.cloud.biz.service.base.PickingSlipService;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbRequestAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbWorkorderAuditLogHelper;
import cn.need.cloud.biz.service.otb.impl.logutil.OtbWorkorderLogUtil;
import cn.need.cloud.biz.service.otb.request.OtbRequestService;
import cn.need.cloud.biz.service.otb.workorder.OtbPrepWorkorderService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderBinLocationService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderDetailService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderService;
import cn.need.cloud.biz.service.product.ProductVersionService;
import cn.need.cloud.biz.util.*;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * OTB工单 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtbWorkorderServiceImpl extends SuperServiceImpl<OtbWorkorderMapper, OtbWorkorder> implements OtbWorkorderService {

    @Resource
    private OtbWorkorderDetailService otbWorkorderDetailService;
    @Resource
    private OtbPrepWorkorderService otbPrepWorkorderService;
    @Resource
    @Lazy
    private OtbRequestService otbRequestService;
    @Resource
    private OtbWorkorderBinLocationService otbWorkorderBinLocationService;
    @Resource
    private PickingSlipService pickingSlipService;
    @Resource
    private ProductVersionService productVersionService;

    /**
     * 构建工单拣货信息
     *
     * @param pickingSlipPickList       拣货单拣货信息
     * @param workOrderMap              工单
     * @param detailGroupByProductIdMap 工单详情产品分组
     * @return /
     */
    @NotNull
    private static List<OtbWorkorderDetailPickVO> buildPickList(List<OtbPickingSlipDetailPickVO> pickingSlipPickList,
                                                                Map<Long, OtbWorkorder> workOrderMap,
                                                                Map<Long, List<OtbWorkorderDetail>> detailGroupByProductIdMap) {
        // 参数校验
        if (ObjectUtil.isEmpty(pickingSlipPickList)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "pickingSlipPickList"));
        }
        if (ObjectUtil.isEmpty(workOrderMap)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "workOrderMap"));
        }
        if (ObjectUtil.isEmpty(detailGroupByProductIdMap)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "detailGroupByProductIdMap"));
        }

        // ProductId ProductCode ProductChannelSku分组分配
        final String groupFormat = "%s:%s:%s";
        Map<String, List<OtbWorkorderDetail>> allocationDetailGroupMap = detailGroupByProductIdMap.values()
                .stream()
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(obj -> String.format(groupFormat,
                        // ProductId ProductCode ProductChannelSku
                        obj.getProductId(), obj.getDetailSnapshotProductBarcode(), obj.getDetailSnapshotProductChannelSku()))
                );

        // 根据工单RefNum排序 分配产品
        allocationDetailGroupMap.forEach((key, detailList) -> detailList.sort(Comparator.comparing(o -> {
            OtbWorkorder workOrder = workOrderMap.get(o.getOtbWorkorderId());
            return (workOrder != null) ? workOrder.getRefNum() : null;
        })));

        return AllocationUtil.checkAndAllocationPickQty(
                pickingSlipPickList,
                allocationDetailGroupMap,
                pick -> String.format(groupFormat, pick.getProductId(), pick.getProductBarcode(), pick.getProductChannelSku()),
                (detail, pick) -> buildWorkorderDetailPick(workOrderMap, pick, detail)
        );
    }

    /**
     * 构建工单详情拣货信息
     *
     * @param workOrderMap Prep工单
     * @param pick         拣货单拣货信息
     * @param detail       Prep工单详情
     * @return /
     */
    private static OtbWorkorderDetailPickVO buildWorkorderDetailPick(Map<Long, OtbWorkorder> workOrderMap,
                                                                     OtbPickingSlipDetailPickVO pick,
                                                                     OtbWorkorderDetail detail) {
        // 参数校验
        if (ObjectUtil.isEmpty(workOrderMap)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_REQUIRED, "workOrderMap"));
        }
        if (ObjectUtil.isEmpty(pick)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_REQUIRED, "pick"));
        }
        if (ObjectUtil.isEmpty(detail)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_REQUIRED, "detail"));
        }

        OtbWorkorderDetailPickVO workorderPick = BeanUtil.copyNew(detail, OtbWorkorderDetailPickVO.class);
        workorderPick.setProductId(pick.getProductId());
        workorderPick.setProductVersionId(pick.getProductVersionId());

        // 设置库位相关信息
        workorderPick.setBinLocationId(pick.getBinLocationId());
        workorderPick.setBinLocationDetailId(pick.getBinLocationDetailId());
        workorderPick.setBinLocationDetailLockedId(pick.getBinLocationDetailLockedId());

        // 锁相关信息
        workorderPick.setRefTableId(workorderPick.getId());
        workorderPick.setRefTableRefNum(String.valueOf(workorderPick.getLineNum()));
        workorderPick.setRefTableName(OtbWorkorderDetail.class.getSimpleName());
        workorderPick.setRefTableShowName(OtbWorkorder.class.getSimpleName());
        workorderPick.setRefTableShowRefNum(Optional.ofNullable(workOrderMap.get(detail.getOtbWorkorderId()))
                .map(OtbWorkorder::getRefNum)
                .orElse(String.valueOf(workorderPick.getOtbWorkorderId()))
        );

        // 设置拣货单信息
        workorderPick.setOtbPickingSlipDetailId(pick.getId());
        workorderPick.setOtbPickingSlipId(pick.getOtbPickingSlipId());

        // 库位日志信息
        RefTableBO logInfo = new RefTableBO();
        logInfo.setRefTableId(pick.getRefTableId());
        logInfo.setRefTableRefNum(pick.getRefTableRefNum());
        logInfo.setRefTableName(pick.getRefTableName());
        logInfo.setRefTableShowName(pick.getRefTableShowName());
        logInfo.setRefTableShowRefNum(pick.getRefTableShowRefNum());
        workorderPick.setPickLogInfo(logInfo);

        workorderPick.setPickedBeforeQty(detail.getPickedQty());
        return workorderPick;
    }

    /**
     * 修复库位查询条件为空对象，影响SQL语句拼接
     *
     * @param query 工单列表查询条件
     * @return /
     */
    private static boolean checkAndFixBinLocationQuery(OtbWorkOrderListQuery query) {
        // 参数校验
        if (ObjectUtil.isEmpty(query)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_REQUIRED, "query"));
        }

        OtbWorkorderQuery otcWorkorderQuery = query.getOtbWorkorderQuery();
        BaseBinLocationQuery binLocationQuery = query.getBinLocationQuery();

        // 业务校验
        if (ObjectUtil.isEmpty(otcWorkorderQuery)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_REQUIRED, "otbWorkorderQuery"));
        }

        boolean canBinLocationQuery = ObjectUtil.isNotNull(otcWorkorderQuery)
                // 仅Begin状态才开启
                && Objects.equals(otcWorkorderQuery.getOtbWorkorderStatus(), OtbWorkorderEnum.BEGIN.getStatus())
                // 添加库位查询条件
                && ObjectUtil.isNotNull(binLocationQuery)
                // 至少存在一个条件
                && binLocationQuery.enable();

        if (!canBinLocationQuery) {
            // 未开启库位查询条件 库位查询条件设置null，空对象影响查询SQL
            query.setBinLocationQuery(null);
        }
        return canBinLocationQuery;
    }

    /**
     * FilterBuild条件校验
     *
     * @param workOrderListQuery FilterBuild工单条件
     */
    private static void checkFilterBuildWorkOrder(OtbWorkOrderListQuery workOrderListQuery) {
        OtbWorkorderQuery otbWorkorderQuery = workOrderListQuery.getOtbWorkorderQuery();

        // 仅仅Begin状态Filter Build Picking Slip
        boolean notBeginWorkOrderStatus = !OtbWorkorderEnum.BEGIN.getStatus().equals(otbWorkorderQuery.getOtbWorkorderStatus());
        if (notBeginWorkOrderStatus) {
            // throw new BusinessException("Only support Begin Work Order status");
            throw new BusinessException(String.format(ErrorMessages.STATUS_REQUIRED, "OtbWorkorder", "BEGIN", otbWorkorderQuery.getOtbWorkorderStatus()));
        }

        // 支持None/Processed
        List<String> prepWorkOderStatusSupportList = Arrays.asList(
                WorkOrderPrepStatusEnum.NONE.getStatus(),
                WorkOrderPrepStatusEnum.PROCESSED.getStatus()
        );
        List<String> prepTypeList = otbWorkorderQuery.getWorkorderPrepStatusList();
        if (ObjectUtil.isEmpty(prepTypeList)) {
            // throw new BusinessException("Please select WorkorderPrepStatus");
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "workorderPrepStatusList"));
        }
        if (!prepWorkOderStatusSupportList.containsAll(prepTypeList)) {
            // throw new BusinessException("Only prepWorkOrderStatus such as None/Processed can be built");
            throw new BusinessException(String.format(ErrorMessages.STATUS_REQUIRED, "prepWorkOrder", "NONE or PROCESSED", String.join(", ", prepTypeList)));
        }
    }

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    @Override
    public PageData<OtbWorkorderPageVO> pageByQuery(PageSearch<OtbWorkOrderListQuery> search) {
        Page<OtbWorkorder> page = Conditions.page(search, entityClass);
        OtbWorkOrderListQuery condition = search.getCondition();
        // 存在库位条件的处理
        if (this.fixQueryByBinLocation(condition)) {
            return new PageData<>(Collections.emptyList(), page);
        }
        List<OtbWorkorderPageVO> dataList = mapper.listByQuery(condition.getOtbWorkorderQuery(), condition.getBinLocationQuery(), condition.getOtbPickingSlipQuery(), page);
        // 返回分页列表
        return new PageData<>(dataList, page);
    }

    @Override
    public OtbWorkorderVO detailById(Long id) {
        OtbWorkorder entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in OtbWorkorder");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtbWorkorder", id));
        }
        return buildOtbWorkorderVO(entity);
    }

    @Override
    public OtbWorkorderVO detailByRefNum(String refNum) {
        OtbWorkorder entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("RefNum: " + refNum + " not found in OtbWorkorder");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtbWorkorder", "refNum", refNum));
        }
        return buildOtbWorkorderVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void begin(Set<Long> ids) {
        // 获取工单状态
        List<OtbWorkorder> workorderList = listByIds(ids);
        // 状态校验
        List<String> list = workorderList
                .stream()
                .filter(item -> ObjectUtil.notEqual(item.getOtbWorkorderStatus(), OtbWorkorderEnum.NEW.getStatus()))
                .map(OtbWorkorder::getRefNum)
                .toList();
        // 判断是否包含非New状态工单数据
        Validate.isTrue(ObjectUtil.isEmpty(list), StringUtils.join(list, StringPool.COMMA).concat(" status is not New"));

        // 更新工单状态
        workorderList.forEach(item -> item.setOtbWorkorderStatus(OtbWorkorderEnum.BEGIN.getStatus()));

        Validate.isTrue(this.updateBatch(workorderList) == workorderList.size(), "Failed to update OtbWorkOrder Begin status");

        // Prep Begin逻辑
        List<OtbPrepWorkorder> prepWorkorderList = otbPrepWorkorderService.workOrderBegin(ids);

        // 更新 Request Processing
        List<OtbRequest> requestList = this.buildUpdateRequestList(workorderList);
        Validate.isTrue(otbRequestService.updateBatch(requestList) == requestList.size(),
                "Failed to update Request Processing status"
        );

        Map<Long, List<OtbPrepWorkorder>> groupByWkMap = StreamUtils.groupBy(prepWorkorderList, OtbPrepWorkorder::getOtbWorkorderId);
        for (OtbWorkorder workorder : workorderList) {
            String desc = groupByWkMap.getOrDefault(workorder.getId(), Collections.emptyList())
                    .stream()
                    .map(RefNumModel::refNumLog)
                    .collect(Collectors.joining(StringPool.NEWLINE));
            // 工单 Begin 日志
            OtbWorkorderAuditLogHelper.recordLog(workorder, desc, null);
        }
    }

    @Override
    public OtbWorkorder getByPickSlipId(Long pickingSlipId) {
        return lambdaQuery()
                .eq(OtbWorkorder::getOtbPickingSlipId, pickingSlipId)
                .one();
    }

    @Override
    public Integer filterBuildPickingSlipCount(OtbWorkOrderListQuery query) {
        // 校验
        checkFilterBuildWorkOrder(query);
        // 存在库位条件的处理
        if (this.fixQueryByBinLocation(query)) {
            return 0;
        }
        // 返回列表条数
        return mapper.filterBuildPickingSlipCount(query.getOtbWorkorderQuery(), query.getOtbPickingSlipQuery(), query.getBinLocationQuery());
    }

    @Override
    public Map<Long, String> getRefNum(Collection<Long> workOrderIdList) {
        if (ObjectUtil.isEmpty(workOrderIdList)) {
            return Collections.emptyMap();
        }
        // 获取工单集合
        List<OtbWorkorder> list = lambdaQuery()
                .in(OtbWorkorder::getId, workOrderIdList)
                .select(OtbWorkorder::getRefNum, OtbWorkorder::getId)
                .list();
        // 根据工单id映射工单refNum
        return ObjectUtil.toMap(list, OtbWorkorder::getId, OtbWorkorder::getRefNum);
    }

    @Override
    public String getRefNum(Long workOrderId) {
        return lambdaQuery().eq(OtbWorkorder::getId, workOrderId)
                .oneOpt()
                .map(OtbWorkorder::getRefNum)
                .orElse(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean adjustShipQty(OtbWorkorderAdjustShipQtyQuery query) {
        OtbWorkorder workorder = this.getById(query.getId());
        Validate.notNull(workorder, "id: " + query.getId() + " not found in OtbWorkorder");
        // 获取详情
        List<Long> detailIdList = StreamUtils.distinctMap(query.getDetailList(), OtbWorkorderAdjustShipQtyDetailQuery::getOtbWorkorderDetailId);
        List<OtbWorkorderDetail> detailList = otbWorkorderDetailService.listByIds(detailIdList);
        Validate.notEmpty(detailList, "detailIdList: " + detailIdList + " not found in OtbWorkorderDetail");

        Map<Long, Integer> adjustShipQtyMap = query.getDetailList()
                .stream()
                .collect(Collectors.toMap(OtbWorkorderAdjustShipQtyDetailQuery::getOtbWorkorderDetailId, OtbWorkorderAdjustShipQtyDetailQuery::getCanProcessQty));
        // TODO 逻辑后续补充
        detailList.stream()
                .filter(obj -> adjustShipQtyMap.containsKey(obj.getId()))
                .forEach(obj -> {
                    int adjustShipQty = adjustShipQtyMap.get(obj.getId());
                    // 发货数量不能大于总数量
                    int qty = obj.getQty();
                    Validate.isTrue(qty >= adjustShipQty,
                            "WorkOrderDetailId {} CanProcessQty {} can not greater than Qty {}",
                            obj.getId(), qty, adjustShipQty
                    );
                    // 实际Ship数量要大于Picked数量
                    int pickedQty = obj.getPickedQty();
                    Validate.isTrue(pickedQty <= adjustShipQty,
                            "PickedQty {} can not greater than CanProcessQty {}",
                            pickedQty, adjustShipQty
                    );

                    int reserveQty = obj.getReserveQty();
                    int noReserveQty = qty - reserveQty;
                    int pickedReserveQty = pickedQty - noReserveQty;
                    // 代表还没Pick 都是 Reserve
                    boolean noPick = pickedReserveQty == 0 && noReserveQty == 0;
                    boolean noPickReserve = pickedReserveQty > 0;

                    Validate.isTrue(noPick || noPickReserve, "has ReserveQty,Please recordLog from PrepWorkOrder");

                    obj.setQty(adjustShipQty);
                    obj.setNote(query.getNote());
                });
        Validate.isTrue(otbWorkorderDetailService.updateBatch(detailList) == detailList.size(), "Failed to Adjust Ship Qty");
        return true;
    }

    @Override
    public List<OtbWorkorder> filterBuildByQuery(OtbPickingSlipFilterBuildQuery query) {
        OtbWorkOrderListQuery workOrderListQuery = query.getFilter();
        // 校验
        checkFilterBuildWorkOrder(workOrderListQuery);
        // 修复库位查询条件
        checkAndFixBinLocationQuery(workOrderListQuery);
        // 这里查询没有校验库存
        return mapper.filterBuild(workOrderListQuery.getOtbWorkorderQuery(), workOrderListQuery.getOtbPickingSlipQuery(), workOrderListQuery.getBinLocationQuery());
    }

    // todo: 根据 Id,获取RefNum 也要统一
    // 根据RefNum 获取 Id

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pick(OtbPickingSlipPickContextVO context) {
        OtbPickingSlip slip = context.getPickingSlip();
        // 工单
        Map<Long, OtbWorkorder> workOrderMap = this.findCanPickListByPickingSlip(slip);

        // 可能取消了工单
        Validate.notEmpty(workOrderMap,
                "{}, Pick Workorder is not found, Please check workorder status",
                slip.refNumLog()
        );

        List<Long> workOrderIdList = workOrderMap.keySet().stream().toList();

        // 产品工单详情分组
        Map<Long, List<OtbWorkorderDetail>> detailGroupByProductIdMap = otbWorkorderDetailService.groupByOtbWorkOrderIdList(workOrderIdList)
                .values()
                .stream()
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(OtbWorkorderDetail::getProductId));

        // 构建工单拣货详情信息
        List<OtbWorkorderDetailPickVO> workOrderDetailPickUpdateList = buildPickList(context.getPickAfterDetailList(), workOrderMap, detailGroupByProductIdMap);

        // 释放库位锁、库存移到ReadyToGo
        pickingSlipService.otbMoveBinLocationInventoryToReadyToGo(workOrderDetailPickUpdateList);

        this.updatePickedQtyAndPicked(workOrderMap, detailGroupByProductIdMap, workOrderDetailPickUpdateList);

        // 绑定工单拣货信息至上下文
        context.setWorkOrderPickAfterDetailList(workOrderDetailPickUpdateList);
    }

    @Override
    public List<DropProVO> distinctValuePro(OtbWorkorderQuery query) {
        return DropListUtil.dropProList(
                query.getColumnNameList(),
                OtbWorkorder.class,
                columnInfos -> mapper.dropProList(columnInfos, query)
        );
    }

    @Override
    public List<DropProVO> countPreDay(OtbWorkorderQuery query) {
        return this.distinctValuePro(query);
    }

    @Override
    public Set<Long> getOtcWorkOrderId(Set<String> otbWorkorderRefNumList) {
        if (ObjectUtil.isEmpty(otbWorkorderRefNumList)) {
            return CollUtil.newHashSet();
        }
        // 获取工单id
        Set<Long> list = lambdaQuery()
                .in(OtbWorkorder::getRefNum, otbWorkorderRefNumList)
                .list()
                .stream()
                .map(OtbWorkorder::getId)
                .collect(Collectors.toSet());
        // 返回工单id
        return ObjectUtil.emptyToDefault(list, CollUtil.newHashSet(-1L));
    }

    /// ///////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    // todo: 这个也是归属于 RefNum & Id 的
    @Override
    public Set<Long> listByRefNum(Set<String> workOrderRefNumList) {
        if (ObjectUtil.isEmpty(workOrderRefNumList)) {
            return CollUtil.newHashSet();
        }
        // 工单id
        return lambdaQuery()
                .in(OtbWorkorder::getRefNum, workOrderRefNumList)
                .select(OtbWorkorder::getId)
                .list()
                .stream()
                .map(OtbWorkorder::getId)
                .collect(Collectors.toSet());
    }

    @Override
    public Boolean existUnfinishedOrder(Long binLocationId) {
        // 工单结束状态
        List<String> list = Lists.arrayList(OtbWorkorderEnum.SHIPED.getStatus());
        Long count = mapper.existUnfinishedOrder(binLocationId, list);
        return count > 0;
    }

    /// ///////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    @Override
    public void checkWithSetStatus(OtbWorkorder item, OtbWorkorderEnum otbWorkOrderEnum) {
        Validate.isTrue(StringUtil.equals(item.getOtbWorkorderStatus(), OtbWorkorderEnum.PACKED.getStatus()),
                StringUtil.format("current workorder refNum:{} ,current status:{} ,after status:{}", item.getRefNum(), item.getOtbWorkorderStatus(), otbWorkOrderEnum.getStatus()));
        item.setOtbWorkorderStatus(otbWorkOrderEnum.getStatus());
        AuditLogHolder.record(OtbWorkorderLogUtil.getAuditShowLog(item));
    }

    @Override
    public void updateChannelConfirmed(OtbWorkorder otbWorkorder, OtbWorkorderEnum otbWorkOrderEnum) {
        // 获取工单详情
        List<OtbWorkorderDetailVO> otbWorkorderDetailList = otbWorkorderDetailService.listByOtbWorkorderId(otbWorkorder.getId());
        // 计算NeedAllocateShipmentQty
        List<OtbWorkorderDetailVO> list = otbWorkorderDetailList
                .stream()
                .filter(item -> ObjectUtil.notEqual(item.getQty() - item.getShipmentQty(), 0))
                .toList();
        // 校验需要分配做成Shipment数量
        if (ObjectUtil.isEmpty(list)) {
            // 更新工单状态
            otbWorkorder.setOtbWorkorderStatus(otbWorkOrderEnum.getStatus());
            super.update(otbWorkorder);
            AuditLogHolder.record(OtbWorkorderLogUtil.getAuditShowLog(otbWorkorder));
        }
    }

    @Override
    public List<OtbWorkorder> listByPickingSlipIds(List<Long> pickingSlipIdList) {
        if (ObjectUtil.isNotEmpty(pickingSlipIdList)) {
            return lambdaQuery()
                    .in(OtbWorkorder::getOtbPickingSlipId, pickingSlipIdList)
                    .list();
        }
        return List.of();
    }

    @Override
    public List<OtbWorkorder> listByRequestIds(List<Long> requestIdList) {
        if (ObjectUtil.isNotEmpty(requestIdList)) {
            return lambdaQuery()
                    .in(OtbWorkorder::getOtbRequestId, requestIdList)
                    .list();
        }
        return List.of();
    }

    /**
     * 构造 OTB 工单主表（含计算总重量判断类型等）
     */
    @Override
    public OtbWorkorder buildOtbWorkorder(
            final String note,
            final OtbRequestVO request,
            final List<OtbRequestDetailVO> detailList
    ) {
        OtbWorkorder workorder = new OtbWorkorder();
        workorder.setId(IdWorker.getId());
        workorder.setOtbRequestId(request.getId());
        workorder.setOtbWorkorderStatus(OtbWorkorderEnum.NEW.getStatus());
        workorder.setOtbRequestShipmentStatus(OtbRequestShipmentStatusEnum.NONE.getStatus());
        workorder.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTB_WORK_ORDER));
        workorder.setWorkorderProductType(WorkorderProductTypeEnum.NORMAL.getStatus());
        workorder.setNote(note);
        // 从 request 中摘取必要的字段做快照
        workorder.setRequestSnapshotRefNum(request.getRefNum());
        workorder.setRequestSnapshotNote(request.getNote());
        workorder.setRequestSnapshotChannel(request.getChannel());
        workorder.setRequestSnapshotTransactionPartnerId(request.getTransactionPartnerId());
        workorder.setRequestSnapshotRequestRefNum(request.getRequestRefNum());
        workorder.setRequestSnapshotShipWindowStart(request.getShipWindowStart());
        workorder.setRequestSnapshotOrderNum(request.getOrderNum());
        workorder.setRequestSnapshotShipWindowEnd(request.getShipWindowEnd());

        // 根据明细计算总重量，决定是否为NORMAL/HEAVY(仅示例)
        workorder.setOtbWorkorderType(
                OtbWorkorderTypeEnum.toWorkorderTypeEnum(getTotalWeight(detailList)).getStatus()
        );

        workorder.setWorkorderPrepStatus(WorkOrderPrepStatusEnum.NONE.getStatus());

        return workorder;
    }

    /**
     * 计算所有明细的总重量
     */
    private BigDecimal getTotalWeight(List<OtbRequestDetailVO> detailList) {
        if (detailList.isEmpty()) {
            return BigDecimal.ZERO;
        }
        // 获取最新版本的产品重量
        final Map<Long, ProductVersionVO> productVersionMap = productVersionService
                .getLatestProductVersionByProductIdList(
                        detailList.stream().map(OtbRequestDetailVO::getProductId).toList()
                );

        BigDecimal totalWeight = BigDecimal.ZERO;
        for (OtbRequestDetailVO detail : detailList) {
            final ProductVersionVO productVersionVO = productVersionMap.get(detail.getProductId());
            if (productVersionVO == null || productVersionVO.getNetWeight() == null) {
                // 若找不到重量信息，可根据业务需要处理，这里直接忽略或抛异常
                continue;
            }
            final BigDecimal netWeight = productVersionVO.getNetWeight();
            final BigDecimal sumWeight = netWeight.multiply(new BigDecimal(detail.getQty()));
            totalWeight = totalWeight.add(sumWeight);
        }
        return totalWeight;
    }

    /**
     * 根据库位查询条件，修正查询条件
     *
     * @param query 列表查询条件
     */
    private boolean fixQueryByBinLocation(OtbWorkOrderListQuery query) {
        boolean canBinLocationQuery = checkAndFixBinLocationQuery(query);
        // 未开启直接返回
        if (!canBinLocationQuery) {
            return false;
        }
        // 开启工单库存校验
        List<OtbWorkorder> dataList = mapper.filterBuild(query.getOtbWorkorderQuery(), query.getOtbPickingSlipQuery(), query.getBinLocationQuery());
        // 校验库存
        List<Long> hasStockWorkOrderIdList = this.listCheckWorkOrderInStock(dataList, query.getBinLocationQuery());
        if (ObjectUtil.isEmpty(hasStockWorkOrderIdList)) {
            return true;
        }
        // 清空查询条件 使用工单id查询
        query.setBinLocationQuery(null);
        query.setOtbWorkorderQuery(null);
        OtbWorkorderQuery fixWorkOrderQuery = new OtbWorkorderQuery();
        fixWorkOrderQuery.setIdList(hasStockWorkOrderIdList);
        query.setOtbWorkorderQuery(fixWorkOrderQuery);
        return false;
    }

    /**
     * 校验工单库存
     *
     * @param dataList 工单集合
     * @return 有库存的工单
     */
    private List<Long> listCheckWorkOrderInStock(List<OtbWorkorder> dataList, BaseBinLocationQuery binLocationQuery) {
        // 校验库存
        List<Long> workOrderIdList = StreamUtils.distinctMap(dataList, OtbWorkorder::getId);
        // 详情按工单分组
        Map<Long, List<OtbWorkorderDetail>> detailGroupByWorkOrderMap = otbWorkorderDetailService.groupByOtbWorkOrderIdList(workOrderIdList);
        return pickingSlipService.filterHasInStockWorkOrder(dataList, detailGroupByWorkOrderMap, binLocationQuery);
    }

    /**
     * 构建OTB工单VO对象
     *
     * @param entity OTB工单对象
     * @return 返回包含详细信息的OTB工单VO对象
     */
    private OtbWorkorderVO buildOtbWorkorderVO(OtbWorkorder entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 转换vo对象
        OtbWorkorderVO otbWorkorderVO = Converters.get(OtbWorkorderConverter.class).toVO(entity);
        // 填充仓库
        WarehouseCacheUtil.filledWarehouse(otbWorkorderVO);
        // 获取工单详情
        List<OtbWorkorderDetailVO> otbWorkorderDetailList = otbWorkorderDetailService.listByOtbWorkorderId(otbWorkorderVO.getId());
        // 计算qty
        computeQty(otbWorkorderDetailList);
        // 填充产品信息
        ProductCacheUtil.filledProduct(otbWorkorderDetailList);
        otbWorkorderVO.setDetailList(otbWorkorderDetailList);
        // 返回包含详细信息的OTB工单VO对象
        return otbWorkorderVO;
    }

    /**
     * 计算数量
     *
     * @param otbWorkorderDetailList 出库工单详情
     */
    private void computeQty(List<OtbWorkorderDetailVO> otbWorkorderDetailList) {

        // todo:这个放在实体里面就可以了
        // 遍历工单详情
        otbWorkorderDetailList.forEach(item -> {
            // 填充NeedReserveQty
            item.setNeedReserveQty(item.getReserveQty() - item.getFinishReserveQty());
            // 待拣货数量
            item.setNeedPackQty(item.getPickedQty() - item.getPackedQty());
            // 待拣货数量
            item.setNeedPickQty(Math.max(item.getQty() - item.getPickedQty(), 0));
            // 能够 Shipment 的 数量
            item.setCanShipmentQty(item.getPackedQty() - item.getShipmentQty());
            //
            item.setNeedAllocateShipmentQty(item.getQty() - item.getShipmentQty());
            // 能够 生成 拣货单 数量
            item.setCanBuildPickingSlipQty(Math.max(item.getQty() - item.getReserveQty() + item.getFinishReserveQty() - item.getPickedQty(), 0));
        });
    }

    /**
     * 获取可拣货的工单
     *
     * @param slip 拣货单
     * @return /
     */
    @NotNull
    private Map<Long, OtbWorkorder> findCanPickListByPickingSlip(OtbPickingSlip slip) {
        return lambdaQuery()
                .eq(OtbWorkorder::getOtbWorkorderStatus, OtbWorkorderEnum.IN_PICKING.getStatus())
                .eq(OtbWorkorder::getOtbPickingSlipId, slip.getId())
                .list()
                .stream()
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));
    }

    /**
     * 获取需要更新单请求单，设置状态Processing
     *
     * @param workOrderList 上下文
     * @return /
     */
    private List<OtbRequest> buildUpdateRequestList(List<OtbWorkorder> workOrderList) {
        // 工单集合
        List<Long> requestIdList = StreamUtils.distinctMap(workOrderList, OtbWorkorder::getOtbRequestId);
        List<OtbRequest> requestList = otbRequestService.listByIds(requestIdList);
        // 设置处理中
        requestList.forEach(obj -> {
            obj.setOtbRequestStatus(RequestStatusEnum.PROCESSING.getStatus());
            obj.setProcessStartTime(TimeUtils.now());
        });

        // 请求单: Processing 日志
        OtbRequestAuditLogHelper.recordLog(requestList);
        return requestList;
    }

    /**
     * 更新拣货数量以及Picked状态
     *
     * @param workOrderMap                  工单
     * @param detailGroupByProductIdMap     工单详情
     * @param workOrderDetailPickUpdateList 工单详情拣货信息
     */
    private void updatePickedQtyAndPicked(Map<Long, OtbWorkorder> workOrderMap, Map<Long, List<OtbWorkorderDetail>> detailGroupByProductIdMap, List<OtbWorkorderDetailPickVO> workOrderDetailPickUpdateList) {
        // 增加otb_workorder_bin_location、lock_id
        List<OtbWorkorderBinLocation> workOrderBinLocationList = workOrderDetailPickUpdateList.stream()
                .map(obj -> {
                    OtbWorkorderBinLocation wkBinLocation = BeanUtil.copyNew(obj, OtbWorkorderBinLocation.class);
                    wkBinLocation.setId(IdWorker.getId());
                    wkBinLocation.setOtbWorkorderDetailId(obj.getId());
                    obj.setOtbWorkorderBinLocationId(wkBinLocation.getId());
                    // 分配至库位的数量
                    wkBinLocation.setQty(obj.getChangePickQty());
                    // readyToGo lock_id
                    wkBinLocation.setBinLocationDetailLockedId(obj.getReadyToGoLocked().getId());
                    return wkBinLocation;
                })
                .toList();
        otbWorkorderBinLocationService.insertBatch(workOrderBinLocationList);

        // 工单详情映射
        Map<Long, Integer> detailPickChangeMap = workOrderDetailPickUpdateList.stream()
                .collect(Collectors.groupingBy(OtbWorkorderDetailPickVO::getId,
                        Collectors.mapping(BasePickVO::getChangePickQty, Collectors.summingInt(Integer::intValue)))
                );
        List<OtbWorkorderDetail> pickDetailUpdateList = detailGroupByProductIdMap.values()
                .stream()
                .flatMap(Collection::stream)
                .filter(obj -> detailPickChangeMap.containsKey(obj.getId()))
                .toList();

        // 更新工单详情
        int wkDetailUpdateCount = otbWorkorderDetailService.updateBatch(pickDetailUpdateList);
        Validate.isTrue(wkDetailUpdateCount == pickDetailUpdateList.size(), "Failed to update work order detail");

        // 全拣货逻辑处理
        pickingSlipService.dealWithAllPicked(workOrderDetailPickUpdateList,
                OtbWorkorderDetail::getOtbWorkorderId,
                detailGroupByProductIdMap,
                (workId, allPicked) -> {
                    OtbWorkorder workorder = workOrderMap.get(workId);
                    // 更新状态
                    workorder.setOtbWorkorderStatus(allPicked
                            ? OtbWorkorderEnum.PICKED.getStatus()
                            : workorder.getOtbWorkorderStatus()
                    );
                }
        );

        // 更新工单状态
        List<OtbWorkorder> pickedWorkorderList = workOrderMap.values().stream()
                .filter(obj -> Objects.equals(obj.getOtbWorkorderStatus(), OtbWorkorderEnum.PICKED.getStatus()))
                .toList();

        Validate.isTrue(super.updateBatch(pickedWorkorderList) == pickedWorkorderList.size(),
                "Failed to update Workorder FinishPickingSlip status"
        );

        // 工单: InPicking -> Picked 日志
        OtbWorkorderAuditLogHelper.recordLog(pickedWorkorderList, WorkorderLogConstant.PICKED_DESCRIPTION, null);
    }
}
