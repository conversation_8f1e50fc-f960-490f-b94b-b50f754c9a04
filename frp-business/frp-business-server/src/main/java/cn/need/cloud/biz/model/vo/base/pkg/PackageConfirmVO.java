package cn.need.cloud.biz.model.vo.base.pkg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 包裹确认页对象
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@Schema(description = "包裹确认页对象")
public class PackageConfirmVO implements Serializable {

    @Schema(description = "包裹id")
    private Long id;

    @Schema(description = "包裹状态")
    private String packageStatus;

    @Schema(description = "refNum")
    private String refNum;

    @Schema(description = "ReadyToShip时间")
    private LocalDateTime readyToShipTime;

    @Schema(description = "shippedTime")
    private LocalDateTime shippedTime;

    @Schema(description = "快递单号")
    private String trackingNum;

}
