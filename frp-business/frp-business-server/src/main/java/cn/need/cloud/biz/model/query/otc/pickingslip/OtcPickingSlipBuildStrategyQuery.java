package cn.need.cloud.biz.model.query.otc.pickingslip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * OTC工单 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC工单 FilterBuildPickingSlip 构建查询Query - strategy query对象")
public class OtcPickingSlipBuildStrategyQuery {

    /**
     * 构建拣货单策略
     */
    @Schema(description = "构建拣货单策略")
    @NotBlank
    private String buildPickingSlipStrategy;

    /**
     * 最大SOSP
     */
    @Schema(description = "最大SOSP")
    @Min(value = 1, message = "MaxSOSP must be greater than or equal to 1")
    @NotNull(message = "MaxSOSP must is not null")
    private Integer maxSosp;

    /**
     * 最大SOSP库位
     */
    @Schema(description = "最大SOSP库位")
    @Min(value = 1, message = "MaxSOSPBinLocation must be greater than or equal to 1")
    @NotNull(message = "MaxSOSPBinLocation must is not null")
    private Integer maxSospBinLocation;

    /**
     * 最大SlapAndGo
     */
    @Schema(description = "最大SlapAndGo")
    @Min(value = 1, message = "MaxSlapAndGo must be greater than or equal to 1")
    @NotNull(message = "MaxSlapAndGo must is not null")
    private Integer maxSlapAndGo;

    /**
     * 最大SlapAndGo库位
     */
    @Schema(description = "最大SlapAndGo库位")
    @Min(value = 1, message = "MaxSlapAndGoBinLocation must be greater than or equal to 1")
    @NotNull(message = "MaxSlapAndGoBinLocation must is not null")
    private Integer maxSlapAndGoBinLocation;

    /**
     * 最大工单数量
     */
    @Schema(description = "最大工单数量")
    @Min(value = 1, message = "MaxCanBuildSum must be greater than or equal to 1")
    @NotNull(message = "MaxCanBuildSum must is not null")
    private Integer maxCanBuildSum;
}