package cn.need.cloud.biz.service.inbound;

import cn.need.cloud.biz.model.entity.inbound.InboundPallet;
import cn.need.cloud.biz.model.entity.inbound.InboundPutawaySlip;
import cn.need.cloud.biz.model.entity.inbound.InboundUnload;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.inbound.InboundPalletQuery;
import cn.need.cloud.biz.model.vo.inbound.pallet.InboundPalletUnloadPrintVO;
import cn.need.cloud.biz.model.vo.inbound.pallet.InboundPalletUnloadVO;
import cn.need.cloud.biz.model.vo.inbound.pallet.InboundPalletVO;
import cn.need.cloud.biz.model.vo.page.InboundPalletPageVO;
import cn.need.cloud.biz.service.base.HeaderPrintedService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 入库单打托 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface InboundPalletService extends SuperService<InboundPallet>, HeaderPrintedService<InboundPallet, InboundPalletService, PrintQuery> {

    /**
     * 根据查询条件获取入库单打托列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个入库单打托对象的列表(分页)
     */
    List<InboundPalletPageVO> listByQuery(InboundPalletQuery query);

    /**
     * 根据查询条件获取入库单打托列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个入库单打托对象的列表(分页)
     */
    PageData<InboundPalletPageVO> pageByQuery(PageSearch<InboundPalletQuery> search);

    /**
     * 根据ID获取入库单打托
     *
     * @param id 入库单打托ID
     * @return 返回入库单打托VO对象
     */
    InboundPalletVO detailById(Long id);

    /**
     * 根据入库单打托唯一编码获取入库单打托
     *
     * @param refNum 入库单打托唯一编码
     * @return 返回入库单打托VO对象
     */
    InboundPalletVO detailByRefNum(String refNum);

    /**
     * 生成打托单并
     *
     * @param inboundPalletUnloadVO 打托卸货信息
     * @param inboundUnloadList     卸货单集合
     * @param inboundPutawaySlip    上架单id
     */
    InboundPalletUnloadPrintVO generatePallet(InboundPalletUnloadVO inboundPalletUnloadVO, List<InboundUnload> inboundUnloadList, InboundPutawaySlip inboundPutawaySlip);

    /**
     * 上架单打托
     *
     * @param inboundPalletUnloadVO 打托卸货
     * @return 打托单打印信息
     */
    InboundPalletUnloadPrintVO pallet(InboundPalletUnloadVO inboundPalletUnloadVO);

    /**
     * 根据工单ID获取入库托盘列表
     *
     * @param workorderId 工单ID
     * @return 入库托盘列表
     */
    List<InboundPallet> listByWorkorderId(Long workorderId);
}
