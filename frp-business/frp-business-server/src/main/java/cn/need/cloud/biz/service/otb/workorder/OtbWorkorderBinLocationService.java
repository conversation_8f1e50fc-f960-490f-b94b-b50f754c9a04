package cn.need.cloud.biz.service.otb.workorder;

import cn.need.cloud.biz.model.entity.otb.OtbWorkorderBinLocation;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkOrderBinLocationQuery;
import cn.need.cloud.biz.model.vo.otb.page.OtbWorkorderBinLocationPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * OTC工单仓储位置 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbWorkorderBinLocationService extends SuperService<OtbWorkorderBinLocation> {

    /**
     * 根据查询条件获取OTC工单仓储位置列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTC工单仓储位置对象的列表(分页)
     */
    PageData<OtbWorkorderBinLocationPageVO> pageByQuery(PageSearch<OtbWorkOrderBinLocationQuery> search);


    /**
     * 根据工单详情ID列表获取OTC工单仓储位置列表
     *
     * @param workOrderDetailIdList 工单详情ID列表
     * @return 返回一个OTC工单仓储位置对象的列表
     */
    List<OtbWorkorderBinLocation> listByOtbWorkorderDetailIdList(List<Long> workOrderDetailIdList);

    /**
     * 根据工单ID列表获取OTC工单仓储位置列表
     *
     * @param workorderIds 工单ID列表
     * @return 返回一个OTC工单仓储位置对象的列表
     */
    List<OtbWorkorderBinLocation> listByWorkorderIdList(List<Long> workorderIds);
}