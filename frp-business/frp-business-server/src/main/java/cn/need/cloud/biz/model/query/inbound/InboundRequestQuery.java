package cn.need.cloud.biz.model.query.inbound;

import cn.need.cloud.biz.service.base.RequestQuery;
import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;


/**
 * 入库请求 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "入库请求 query对象")
public class InboundRequestQuery extends SuperQuery implements RequestQuery {

    /**
     * 运输方式类型
     */
    @Schema(description = "运输方式类型")
    private String transportMethodType;

    /**
     * 运输方式类型
     */
    @Schema(description = "运输方式类型集合")
    @Condition(value = Keyword.IN, fields = {"transportMethodType"})
    private List<String> transportMethodTypeList;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码")
    private String requestRefNum;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"requestRefNum"})
    private Set<String> requestRefNumList;

    /**
     * 预计到达日期
     */
    @Schema(description = "预计到达日期")
    private LocalDateTime estimateArrivalDate;
    /**
     * 预计到达日期开始
     */
    @Schema(description = "预计到达日期开始")
    @Condition(Keyword.GE)
    private LocalDateTime estimateArrivalDateStart;
    /**
     * 预计到达日期结束
     */
    @Schema(description = "预计到达日期结束")
    @Condition(Keyword.LE)
    private LocalDateTime estimateArrivalDateEnd;

    /**
     * 实际到达日期
     */
    @Schema(description = "实际到达日期")
    private LocalDateTime actualArrivalDate;
    /**
     * 实际到达日期开始
     */
    @Schema(description = "实际到达日期开始")
    @Condition(Keyword.GE)
    private LocalDateTime actualArrivalDateStart;
    /**
     * 实际到达日期结束
     */
    @Schema(description = "实际到达日期结束")
    @Condition(Keyword.LE)
    private LocalDateTime actualArrivalDateEnd;

    /**
     * 快递号
     */
    @Schema(description = "快递号")
    private String trackingNum;

    /**
     * 快递号
     */
    @Schema(description = "快递号集合")
    @Condition(value = Keyword.IN, fields = {"trackingNum"})
    private List<String> trackingNumList;

    /**
     * 发件人姓名
     */
    @Schema(description = "发件人姓名")
    private String fromAddressName;

    /**
     * 发件人公司
     */
    @Schema(description = "发件人公司")
    private String fromAddressCompany;

    /**
     * 发件人国家
     */
    @Schema(description = "发件人国家")
    private String fromAddressCountry;

    /**
     * 发件人州
     */
    @Schema(description = "发件人州")
    private String fromAddressState;

    /**
     * 发件人城市
     */
    @Schema(description = "发件人城市")
    private String fromAddressCity;

    /**
     * 发件人邮编
     */
    @Schema(description = "发件人邮编")
    private String fromAddressZipCode;

    /**
     * 发件人地址1
     */
    @Schema(description = "发件人地址1")
    private String fromAddressAddr1;

    /**
     * 发件人地址2
     */
    @Schema(description = "发件人地址2")
    private String fromAddressAddr2;

    /**
     * 发件人地址3
     */
    @Schema(description = "发件人地址3")
    private String fromAddressAddr3;

    /**
     * 发件人邮箱
     */
    @Schema(description = "发件人邮箱")
    private String fromAddressEmail;

    /**
     * 发件人电话
     */
    @Schema(description = "发件人电话")
    private String fromAddressPhone;

    /**
     * 发件人备注
     */
    @Schema(description = "发件人备注")
    private String fromAddressNote;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private List<Long> warehouseIdList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private Set<String> refNumList;

    /**
     * 交易伙伴ID
     */
    @Schema(description = "交易伙伴ID")
    private Long transactionPartnerId;

    /**
     * 交易伙伴ID
     */
    @Schema(description = "交易伙伴ID集合")
    @Condition(value = Keyword.IN, fields = {"transactionPartnerId"})
    private Set<Long> transactionPartnerIdList;

    /**
     * 请求状态
     */
    @Schema(description = "请求状态")
    private String inboundRequestStatus;

    /**
     * 请求状态
     */
    @Schema(description = "请求状态集合")
    @Condition(value = Keyword.IN, fields = {"inboundRequestStatus"})
    private List<String> inboundRequestStatusList;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 容器类型
     */
    @Schema(description = "容器类型")
    private String containerType;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String productType;

    /**
     * 容器类型
     */
    @Schema(description = "容器类型集合")
    @Condition(value = Keyword.IN, fields = {"containerType"})
    private List<String> containerTypeList;

    /**
     * 发件人地址是否为住宅
     */
    @Schema(description = "发件人地址是否为住宅")
    private Boolean fromAddressIsResidential;

    /**
     * 计费状态
     */
    @Schema(description = "计费状态")
    private String feeStatus;

    /**
     * 计费状态
     */
    @Schema(description = "计费状态")
    @Condition(value = Keyword.IN, fields = {"feeStatus"})
    private List<String> feeStatusList;
}