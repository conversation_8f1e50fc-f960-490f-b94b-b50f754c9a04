package cn.need.cloud.biz.service.otb.request;

import cn.need.cloud.biz.model.entity.otb.OtbRequest;
import cn.need.cloud.biz.model.param.otb.create.request.OtbRequestCreateParam;
import cn.need.cloud.biz.model.param.otb.update.request.OtbRequestAuditParam;
import cn.need.cloud.biz.model.param.otb.update.request.OtbRequestUpdateParam;
import cn.need.cloud.biz.model.query.otb.request.OtbRequestQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.otb.page.OtbRequestPageVO;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestVO;
import cn.need.cloud.biz.service.base.RequestService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * OTB请求 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbRequestService extends SuperService<OtbRequest>,RequestService<OtbRequest, OtbRequestService> {

    /**
     * 根据参数新增OTB请求
     *
     * @param createParam 请求创建参数，包含需要插入的OTB请求的相关信息
     * @return OTB请求实体对象
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    OtbRequest insertByParam(OtbRequestCreateParam createParam);

    /**
     * 根据参数更新OTB请求
     *
     * @param updateParam 请求创建参数，包含需要更新的OTB请求的相关信息
     * @return 返回更新后的OTB请求实体对象
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    OtbRequest updateByParam(OtbRequestUpdateParam updateParam);

    /**
     * 根据查询条件获取OTB请求列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTB请求对象的列表(分页)
     */
    List<OtbRequestPageVO> listByQuery(OtbRequestQuery query);

    /**
     * 根据查询条件获取OTB请求列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTB请求对象的列表(分页)
     */
    PageData<OtbRequestPageVO> pageByQuery(PageSearch<OtbRequestQuery> search);

    /**
     * 根据ID获取OTB请求
     *
     * @param id OTB请求ID
     * @return 返回OTB请求VO对象
     */
    OtbRequestVO detailById(Long id);

    /**
     * 根据OTB请求唯一编码获取OTB请求
     *
     * @param refNum OTB请求唯一编码
     * @return 返回OTB请求VO对象
     */
    OtbRequestVO detailByRefNum(String refNum);

    /**
     * 审核请求
     * 此方法用于处理审核请求的操作，例如审核一篇笔记或请求是否符合规定等具体业务逻辑需根据实际情况定义
     *
     * @param param 包含审核请求所需参数的对象，如请求ID、审核结果、审核人等
     */
    void audit(OtbRequestAuditParam param);

    /**
     * 根据请求单id映射请求对象
     *
     * @param requestIdList otb请求id集合
     * @return 求单id映射请求对象关系
     */
    Map<Long, OtbRequest> getRefNum(Collection<Long> requestIdList);

    /**
     * 获取请求单id
     *
     * @param otbRequestRefNumList 请求单refNum
     * @return 请求单id
     */
    Set<Long> getRequestId(Set<String> otbRequestRefNumList);

    /**
     * 下拉列表pro 统计
     *
     * @param query 查询条件
     * @return /
     */
    List<DropProVO> countPreDay(OtbRequestQuery query);

    /**
     * 根据refNum获取请求单id
     *
     * @param requestOfRequestRefNumList 请求单请求refNum
     * @return 请求单id
     */
    Set<Long> listByRequestRefNum(Set<String> requestOfRequestRefNumList);

    /**
     * 根据请求单id获取请求单refNum
     *
     * @param otbRequestId 请求单id
     * @return 请求单refNum
     */
    OtbRequest getRefNum(Long otbRequestId);

    /**
     * 审核失败
     *
     * @param otbRequest otb请求对象
     * @param message    错误信息
     * @param note       错误备注
     */
    void updateByApproveFail(OtbRequest otbRequest, String message, String note);

    /**
     * 根据仓库id获取未完成的请求单
     *
     * @param warehouseId 仓库id
     * @return 是否存在未完成的请求单
     */
    Boolean existUnfinishedOrder(Long warehouseId);
}