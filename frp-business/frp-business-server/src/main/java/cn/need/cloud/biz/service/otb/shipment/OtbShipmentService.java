package cn.need.cloud.biz.service.otb.shipment;

import cn.need.cloud.biz.model.bo.otb.OtbBuildShipmentContextBO;
import cn.need.cloud.biz.model.entity.otb.OtbShipment;
import cn.need.cloud.biz.model.entity.otb.OtbWorkorder;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.otb.shipment.OtbShipmentQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.otb.shipment.OtbShipmentVO;
import cn.need.cloud.biz.model.vo.page.OtbShipmentPageVO;
import cn.need.cloud.biz.service.base.MarkPrintedService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * OTB装运 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbShipmentService extends SuperService<OtbShipment>, MarkPrintedService<OtbShipment, OtbShipmentService, PrintQuery> {

    /**
     * 根据查询条件获取OTB装运列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTB装运对象的列表(分页)
     */
    List<OtbShipmentPageVO> listByQuery(OtbShipmentQuery query);

    /**
     * 根据查询条件获取OTB装运列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTB装运对象的列表(分页)
     */
    PageData<OtbShipmentPageVO> pageByQuery(PageSearch<OtbShipmentQuery> search);

    /**
     * 根据ID获取OTB装运
     *
     * @param id OTB装运ID
     * @return 返回OTB装运VO对象
     */
    OtbShipmentVO detailById(Long id);

    /**
     * 根据OTB装运唯一编码获取OTB装运
     *
     * @param refNum OTB装运唯一编码
     * @return 返回OTB装运VO对象
     */
    OtbShipmentVO detailByRefNum(String refNum);

    /**
     * 下拉列表pro 统计
     *
     * @param query 查询条件
     * @return /
     */
    List<DropProVO> countPreDay(OtbShipmentQuery query);

    /**
     * 下拉列表pro 带Group的
     *
     * @param query 查询条件
     * @return /
     */
    List<DropProVO> distinctValuePro(OtbShipmentQuery query);


    /**
     * 上下文
     *
     * @param context 上下文
     */
    void loadContext(OtbBuildShipmentContextBO context);

    /**
     * 生成发货单
     *
     * @param context 上下文
     */
    void generateShipment(OtbBuildShipmentContextBO context);

    /**
     * 生成小件发货单
     *
     * @param context 上下文
     */
    void generateSmallParcel(OtbBuildShipmentContextBO context);

    /**
     * 渠道确认
     *
     * @param idList 发货单ID
     */
    void waitChannelConfirm(List<Long> idList);

    /**
     * 标记处理中
     *
     * @param otbShipment 发货单
     */
    void markProcessing(OtbShipment otbShipment);

    /**
     * 标记处理中或准备发货
     *
     * @param otbShipmentId 发货单ID
     * @param otbPackageId  日志ID
     */
    void markProcessingOrReadyToShip(Long otbShipmentId, Long otbPackageId);


    /**
     * 标记完成
     *
     * @param otbShipment 发货单
     */
    void markProcessed(OtbShipment otbShipment);

    /**
     * 请求单下是否存在发货单状态不为Processed
     *
     * @param otbRequestId      请求单id
     * @param id                发货单id
     * @param otbShipmentStatus 发货单状态
     */
    boolean existShipment(Long otbRequestId, Long id, String otbShipmentStatus);

    /**
     * 释放ReadyToGo库位库存
     *
     * @param otbShipment  发货单
     * @param otbWorkorder 工单
     */
    void releaseReadyToGoBinLocations(OtbShipment otbShipment, OtbWorkorder otbWorkorder);

    /**
     * 判断是否有未完成的
     *
     * @param requestId  请求单ID
     * @param shipmentId 发货单ID
     * @param status     状态
     * @return /
     */
    Boolean isAnyNotStatus(Long requestId, Long shipmentId, List<String> status);

    /**
     * 打印发货单
     *
     * @param context 上下文
     * @return 打印信息
     */
    OtbShipmentVO buildShipmentPrintInfo(OtbBuildShipmentContextBO context);

    /**
     * 根据工单ID列表获取OTB装运列表
     *
     * @param workorderIds 工单ID列表
     * @return 返回一个OTB装运对象的列表
     */
    List<OtbShipment> listByWorkorderIds(List<Long> workorderIds);

    /**
     * 根据请求单ID列表获取OTB装运列表
     *
     * @param requestIds 请求单ID列表
     * @return 返回一个OTB装运对象的列表
     */
    List<OtbShipment> listByRequestIds(List<Long> requestIds);
}