package cn.need.cloud.biz.model.param.otc.update.pickingslip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * OTC拣货单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC拣货单 vo对象")
public class OtcPickingSlipUpdateParam implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 分配人
     */
    @Schema(description = "分配人")
    private Long assignedUserId;

    /**
     * 拣货到那里
     */
    @Schema(description = "拣货到那里")
    private String pickToStation;

    /**
     * 拣货状态
     */
    @Schema(description = "拣货状态")
    private String pickingSlipStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * 订单类型
     */
    @Schema(description = "订单类型")
    private String orderType;

    /**
     * 是否有特定运输要求
     */
    @Schema(description = "是否有特定运输要求")
    private Boolean hasCusShipRequire;

    /**
     * 现场包装标志
     */
    @Schema(description = "现场包装标志")
    private Boolean onSitePackFlag;


    /**
     * 谁构建拣货单
     */
    @Schema(description = "谁构建拣货单")
    private String buildFromType;

    /**
     * 锁定前
     */
    @Schema(description = "锁定前")
    private String lockedBefore;

}