package cn.need.cloud.biz.model.param.otb.update.pickingslip.prep;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;


/**
 * otb预拣货单详情 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "otb预拣货单详情 vo对象")
public class OtbPrepPickingSlipDetailUpdateParam implements Serializable {


    @Serial
    private static final long serialVersionUID = -8561610084101661592L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 已经分配的数量
     */
    @Schema(description = "已经分配的数量")
    private Integer allocateQty;

    /**
     * 拣货数量
     */
    @Schema(description = "拣货数量")
    private Integer pickedQty;

    /**
     * 库位详情锁id
     */
    @Schema(description = "库位详情锁id")
    private Long binLocationDetailLockedId;

    /**
     * 库位详情id
     */
    @Schema(description = "库位详情id")
    private Long binLocationDetailId;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * otb预拣货单id
     */
    @Schema(description = "otb预拣货单id")
    private Long otbPrepPickingSlipId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    @Size(max = 64, message = "hazmatVersionRefNum cannot exceed 64 characters")
    private String hazmatVersionRefNum;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    /**
     * 上架数量
     */
    @Schema(description = "上架数量")
    private Integer putawayQty;

}