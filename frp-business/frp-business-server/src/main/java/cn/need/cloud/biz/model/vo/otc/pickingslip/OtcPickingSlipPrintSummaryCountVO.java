package cn.need.cloud.biz.model.vo.otc.pickingslip;

import cn.need.cloud.biz.model.vo.base.RefNumVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


/**
 * OTC拣货单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC拣货单 PrintSummary 统计结果 vo对象")
public class OtcPickingSlipPrintSummaryCountVO {


    /**
     * 打印数量
     */
    @Schema(description = "打印数量")
    private Integer count;

    /**
     * 未汇总数量
     */
    @Schema(description = "未汇总数量")
    private Integer noSummaryCount;

    /**
     * 已汇总数量
     */
    @Schema(description = "已汇总数量")
    private Integer summaryCount;


    /**
     * 未汇总列表
     */
    @Schema(description = "未汇总列表")
    private List<OtcPickingSlipSummaryVO> summaryList;
    /**
     * 未汇总列表
     */
    @Schema(description = "汇总列表")
    private List<RefNumVO> noSummaryList;

}