package cn.need.cloud.biz.model.vo.page;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.otc.pkg.OtcPackagePageWorkorderVO;
import cn.need.cloud.biz.model.vo.otc.pkg.OtcPackageDetailSimpleVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * OTC包裹 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTC包裹 vo对象")
public class OtcPackagePageVO extends BaseSuperVO {

    @Serial
    private static final long serialVersionUID = 6084224071764980487L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String detailProductType;

    /**
     * 快递号
     */
    @Schema(description = "快递号")
    private String trackingNum;

    /**
     * c端拣货id
     */
    @Schema(description = "c端拣货id")
    private Long otcPickingSlipId;

    /**
     * c端工单id
     */
    @Schema(description = "c端工单id")
    private Long otcWorkorderId;

    /**
     * c端工单id
     */
    @Schema(description = "c端工单")
    private OtcPackagePageWorkorderVO otcWorkorder;

    /**
     * c端拣货
     */
    @Schema(description = "c端拣货")
    private RefNumVO pickingSlip;

    /**
     * 包裹状态
     */
    @Schema(description = "包裹状态")
    private String packageStatus;

    /**
     * 是否为快递运输
     */
    @Schema(description = "是否为快递运输")
    private Boolean shipExpressFlag;

    /**
     * 运输方式
     */
    @Schema(description = "运输方式")
    private String shipMethod;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    /**
     * 准备运输时间
     */
    @Schema(description = "准备运输时间")
    private LocalDateTime readyToShipTime;

    /**
     * 已运输时间
     */
    @Schema(description = "已运输时间")
    private LocalDateTime shippedTime;

    /**
     * 运输箱子-长
     */
    @Schema(description = "运输箱子-长")
    private BigDecimal shipSizeLength;

    /**
     * 运输箱子-宽
     */
    @Schema(description = "运输箱子-宽")
    private BigDecimal shipSizeWidth;

    /**
     * 运输箱子-高
     */
    @Schema(description = "运输箱子-高")
    private BigDecimal shipSizeHeight;

    /**
     * 运输箱子-重量
     */
    @Schema(description = "运输箱子-重量")
    private BigDecimal shipSizeWeight;

    /**
     * 运输箱子-长度单位
     */
    @Schema(description = "运输箱子-长度单位")
    private String shipSizeDimensionUnit;

    /**
     * 运输箱子-重量单位
     */
    @Schema(description = "运输箱子-重量单位")
    private String shipSizeWeightUnit;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id", hidden = true)
    @JsonIgnore
    private Long warehouseId;

    /**
     * 多盒包裹行号
     */
    @JsonIgnore
    private Integer packageMultiboxLineNum;

    /**
     * 多盒包裹产品版本id
     */
    @JsonIgnore
    private Long packageMultiboxProductId;

    /**
     * 多盒包裹版本
     */
    @JsonIgnore
    private Integer packageMultiboxVersionInt;

    /**
     * 流程类型
     */
    @Schema(description = "流程类型")
    private String processType;

    /**
     * 包裹详情列表
     */
    @Schema(description = "包裹详情列表")
    private List<OtcPackageDetailSimpleVO> details;

}