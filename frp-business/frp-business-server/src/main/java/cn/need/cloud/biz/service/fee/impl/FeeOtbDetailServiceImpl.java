package cn.need.cloud.biz.service.fee.impl;


import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.fee.FeeOtbDetailConverter;
import cn.need.cloud.biz.mapper.fee.FeeOtbDetailMapper;
import cn.need.cloud.biz.model.entity.fee.FeeOtbDetail;
import cn.need.cloud.biz.model.param.fee.create.FeeOtbDetailCreateParam;
import cn.need.cloud.biz.model.param.fee.update.FeeOtbDetailUpdateParam;
import cn.need.cloud.biz.model.query.fee.FeeOtbDetailQuery;
import cn.need.cloud.biz.model.vo.fee.FeeOtbDetailVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeOtbDetailPageVO;
import cn.need.cloud.biz.service.fee.FeeOtbDetailService;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 费用详情otb service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Service
public class FeeOtbDetailServiceImpl extends SuperServiceImpl<FeeOtbDetailMapper, FeeOtbDetail> implements FeeOtbDetailService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(FeeOtbDetailCreateParam createParam) {
        // 检查传入费用详情otb参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 将费用详情otb参数对象转换为实体对象并初始化
        FeeOtbDetail entity = initFeeOtbDetail(createParam);

        // 插入费用详情otb实体对象到数据库
        super.insert(entity);

        // 返回费用详情otbID
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(FeeOtbDetailUpdateParam updateParam) {
        // 检查传入费用详情otb参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 将费用详情otb参数对象转换为实体对象
        FeeOtbDetail entity = initFeeOtbDetail(updateParam);

        // 执行更新费用详情otb操作
        return super.update(entity);

    }

    @Override
    public List<FeeOtbDetailPageVO> listByQuery(FeeOtbDetailQuery query) {
        if (ObjectUtil.isEmpty(query)) {
            return Collections.emptyList();
        }
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<FeeOtbDetailPageVO> pageByQuery(PageSearch<FeeOtbDetailQuery> search) {
        Page<FeeOtbDetail> page = Conditions.page(search, entityClass);
        List<FeeOtbDetailPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public FeeOtbDetailVO detailById(Long id) {
        FeeOtbDetail entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "FeeOtbDetail", id));
        }
        return buildFeeOtbDetailVO(entity);
    }

    @Override
    public FeeOtbDetailVO detailByRefNum(String refNum) {
        FeeOtbDetail entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "FeeOtbDetail", "refNum", refNum));
        }
        return buildFeeOtbDetailVO(entity);
    }

    @Override
    public List<FeeOtbDetailVO> listByFeeOtbId(Long feeOtbId) {
        List<FeeOtbDetail> list = lambdaQuery().eq(FeeOtbDetail::getHeaderId, feeOtbId).list();
        return Converters.get(FeeOtbDetailConverter.class).toVO(list);
    }

    /**
     * 初始化费用详情otb对象
     * 此方法用于设置费用详情otb对象的必要参数，确保其处于有效状态
     *
     * @param createParam 费用详情otb 新增对象，不应为空
     * @return 返回初始化后的费用详情otb
     * @throws BusinessException 如果传入的费用详情otb为空，则抛出此异常
     */
    private FeeOtbDetail initFeeOtbDetail(FeeOtbDetailCreateParam createParam) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "FeeOtbDetailCreateParam"));
        }

        // 获取费用详情otb转换器实例，用于将费用详情otb参数对象转换为实体对象
        FeeOtbDetailConverter converter = Converters.get(FeeOtbDetailConverter.class);

        // 将费用详情otb参数对象转换为实体对象并初始化
        FeeOtbDetail entity = converter.toEntity(createParam);

        entity.setId(IdWorker.getId());
        entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.FEE_OTB_DETAIL));

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 初始化费用详情otb对象
     * 此方法用于设置费用详情otb对象的必要参数，确保其处于有效状态
     *
     * @param updateParam 费用详情otb 修改对象，不应为空
     * @return 返回初始化后的费用详情otb
     * @throws BusinessException 如果传入的费用详情otb为空，则抛出此异常
     */
    private FeeOtbDetail initFeeOtbDetail(FeeOtbDetailUpdateParam updateParam) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(updateParam)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "FeeOtbDetailUpdateParam"));
        }

        // 获取费用详情otb转换器实例，用于将费用详情otb参数对象转换为实体对象
        FeeOtbDetailConverter converter = Converters.get(FeeOtbDetailConverter.class);

        // 将费用详情otb参数对象转换为实体对象并初始化
        FeeOtbDetail entity = converter.toEntity(updateParam);


        // 返回初始化后的配置对象
        return entity;
    }


    /**
     * 构建费用详情otbVO对象
     *
     * @param entity 费用详情otb对象
     * @return 返回包含详细信息的费用详情otbVO对象
     */
    private FeeOtbDetailVO buildFeeOtbDetailVO(FeeOtbDetail entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的费用详情otbVO对象
        return Converters.get(FeeOtbDetailConverter.class).toVO(entity);
    }

}
