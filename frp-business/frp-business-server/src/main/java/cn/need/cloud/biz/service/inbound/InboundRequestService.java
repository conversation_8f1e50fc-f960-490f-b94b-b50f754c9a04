package cn.need.cloud.biz.service.inbound;

import cn.need.cloud.biz.model.bo.inbound.InboundRequestAuditContextBO;
import cn.need.cloud.biz.model.entity.inbound.InboundRequest;
import cn.need.cloud.biz.model.param.inbound.create.InboundRequestCreateParam;
import cn.need.cloud.biz.model.param.inbound.update.InboundRequestUpdateParam;
import cn.need.cloud.biz.model.query.inbound.InboundRequestQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.inbound.request.InboundRequestVO;
import cn.need.cloud.biz.model.vo.page.InboundRequestPageVO;
import cn.need.cloud.biz.service.base.RequestService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.NoteParam;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 入库请求 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface InboundRequestService extends SuperService<InboundRequest>, RequestService<InboundRequest,InboundRequestService> {

    /**
     * 根据参数新增入库请求
     *
     * @param createParam 请求创建参数，包含需要插入的入库请求的相关信息
     * @return 返回创建的入库请求实体对象
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    InboundRequest insertByParam(InboundRequestCreateParam createParam);


    /**
     * 根据参数更新入库请求
     *
     * @param updateParam 请求创建参数，包含需要更新的入库请求的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    InboundRequest updateByParam(InboundRequestUpdateParam updateParam);

    /**
     * 根据查询条件获取入库请求列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个入库请求对象的列表(分页)
     */
    List<InboundRequestPageVO> listByQuery(InboundRequestQuery query);

    /**
     * 根据查询条件获取入库请求列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个入库请求对象的列表(分页)
     */
    PageData<InboundRequestPageVO> pageByQuery(PageSearch<InboundRequestQuery> search);

    /**
     * 根据ID获取入库请求
     *
     * @param id 入库请求ID
     * @return 返回入库请求VO对象
     */
    InboundRequestVO detailById(Long id);

    /**
     * 根据入库请求唯一编码获取入库请求
     *
     * @param refNum 入库请求唯一编码
     * @return 返回入库请求VO对象
     */
    InboundRequestVO detailByRefNum(String refNum);

    /**
     * 入库请求字段去重下拉
     */
    List<DropProVO> distinctValue(InboundRequestQuery query);

    /**
     * 取消请求单状态
     *
     * @param cancelParam 取消信息
     * @param status      状态信息
     * @return 影响行数
     */
    Integer updateStatus(NoteParam cancelParam, String status);

    /**
     * 更新入库请求单状态
     *
     * @param inboundRequestId 入库请求单id
     * @param status           更新状态
     * @param arrivedTime      实际到达时间
     */
    void updateRequestStatus(Long inboundRequestId, String status, LocalDateTime arrivedTime);

    /**
     * 审批入库请求单
     *
     * @param context 审批上下文信息
     */
    void updateRequestStatus(InboundRequestAuditContextBO context);

    /**
     * 判断是否有未完成的入库请求单
     *
     * @param warehouseId 仓库id
     * @return 是否有未完成的入库请求单
     */
    Boolean existUnfinishedOrder(Long warehouseId);
}