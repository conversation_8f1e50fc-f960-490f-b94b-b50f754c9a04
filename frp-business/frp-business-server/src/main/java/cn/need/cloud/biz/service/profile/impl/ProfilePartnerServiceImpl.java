package cn.need.cloud.biz.service.profile.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.profile.ProfilePartnerConverter;
import cn.need.cloud.biz.mapper.setting.ProfilePartnerMapper;
import cn.need.cloud.biz.model.entity.setting.ProfilePartner;
import cn.need.cloud.biz.model.param.profile.create.ProfilePartnerCreateParam;
import cn.need.cloud.biz.model.param.profile.update.ProfilePartnerUpdateParam;
import cn.need.cloud.biz.model.query.profile.ProfilePartnerQuery;
import cn.need.cloud.biz.model.vo.profile.ProfilePartnerPageVO;
import cn.need.cloud.biz.model.vo.profile.ProfilePartnerVO;
import cn.need.cloud.biz.service.profile.ProfilePartnerService;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 企业伙伴档案 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@Service
public class ProfilePartnerServiceImpl extends SuperServiceImpl<ProfilePartnerMapper, ProfilePartner> implements ProfilePartnerService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(ProfilePartnerCreateParam createParam) {
        // 检查传入企业伙伴档案参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取企业伙伴档案转换器实例，用于将企业伙伴档案参数对象转换为实体对象
        ProfilePartnerConverter converter = Converters.get(ProfilePartnerConverter.class);

        // 将企业伙伴档案参数对象转换为实体对象并初始化
        ProfilePartner entity = initProfilePartner(converter.toEntity(createParam));

        // 插入企业伙伴档案实体对象到数据库
        super.insert(entity);

        // 返回企业伙伴档案ID
        return entity.getId();
    }


    /**
     * 初始化企业伙伴档案对象
     * 此方法用于设置企业伙伴档案对象的必要参数，确保其处于有效状态
     *
     * @param entity 企业伙伴档案对象，不应为空
     * @return 返回初始化后的企业伙伴档案
     * @throws BusinessException 如果传入的企业伙伴档案为空，则抛出此异常
     */
    private ProfilePartner initProfilePartner(ProfilePartner entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("ProfilePartner cannot be empty");
        }
        entity.setTenantId(TenantContextHolder.getTenantId());

        // 返回初始化后的配置对象
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(ProfilePartnerUpdateParam updateParam) {
        // 检查传入企业伙伴档案参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取企业伙伴档案转换器实例，用于将企业伙伴档案参数对象转换为实体对象
        ProfilePartnerConverter converter = Converters.get(ProfilePartnerConverter.class);

        // 将企业伙伴档案参数对象转换为实体对象
        ProfilePartner entity = converter.toEntity(updateParam);

        // 执行更新企业伙伴档案操作
        int update = super.update(initProfilePartner(entity));
        Validate.isTrue(update == 1, "Update failed");
        return update;

    }

    @Override
    public List<ProfilePartnerPageVO> listByQuery(ProfilePartnerQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<ProfilePartnerPageVO> pageByQuery(PageSearch<ProfilePartnerQuery> search) {
        Page<ProfilePartner> page = Conditions.page(search, entityClass);
        List<ProfilePartnerPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public ProfilePartnerVO detailById(Long id) {
        ProfilePartner entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in ProfilePartner");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "ProfilePartner", id));
        }
        return buildProfilePartnerVO(entity);
    }


    /**
     * 构建企业伙伴档案VO对象
     *
     * @param entity 企业伙伴档案对象
     * @return 返回包含详细信息的企业伙伴档案VO对象
     */
    private ProfilePartnerVO buildProfilePartnerVO(ProfilePartner entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的企业伙伴档案VO对象
        return Converters.get(ProfilePartnerConverter.class).toVO(entity);
    }

}
