package cn.need.cloud.biz.model.entity.otc;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * <p>
 * OTC请求详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otc_request_detail")
public class OtcRequestDetail extends SuperModel {


    @Serial
    private static final long serialVersionUID = 6547763060939452827L;
    /**
     * OTC请求ID
     */
    @TableField("otc_request_id")
    private Long otcRequestId;

    /**
     * 数量
     */
    @TableField("qty")
    private Integer qty;

    @TableField("finish_qty")
    private Integer finishQty;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 行序号
     */
    @TableField("line_num")
    private Integer lineNum;


    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @TableField("product_id")
    private Long productId;
    
}
