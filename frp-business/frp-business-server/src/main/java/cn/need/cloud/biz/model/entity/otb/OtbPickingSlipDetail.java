package cn.need.cloud.biz.model.entity.otb;

import cn.need.cloud.biz.model.entity.base.pickingslip.BasePickingSlipDetailModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * <p>
 * otb拣货单详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otb_picking_slip_detail")
public class OtbPickingSlipDetail extends BasePickingSlipDetailModel {


    @Serial
    private static final long serialVersionUID = -1012510432039019856L;

    /**
     * otb拣货单id
     */
    @TableField("otb_picking_slip_id")
    private Long otbPickingSlipId;


    /**
     * 危险品版本号
     */
    @TableField("hazmat_version_ref_num")
    private String hazmatVersionRefNum;


    /**
     * 打包为Package 数量
     */
    @TableField("packed_qty")
    private Integer packedQty;

    /**
     * 渠道要求的需要贴的产品标识码
     */
    @TableField("product_barcode")
    private String productBarcode;

    /**
     * 渠道sku
     */
    @TableField("product_channel_sku")
    private String productChannelSku;

    /**
     * relabel状态
     */
    @TableField("relabel_status")
    private String relabelStatus;


    public String toLog() {
        return String.format("LineNum: %d ProductBarcode: %s Qty: %s", getLineNum(), getProductBarcode(), getQty());
    }
}
