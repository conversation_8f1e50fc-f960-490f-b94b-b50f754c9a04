package cn.need.cloud.biz.mapper.otc;

import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorderBinLocation;
import cn.need.cloud.biz.model.query.otc.workorder.prep.OtcPrepWorkorderBinLocationQuery;
import cn.need.cloud.biz.model.vo.page.OtcPrepWorkorderBinLocationPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * OTC预提工单仓储位置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtcPrepWorkorderBinLocationMapper extends SuperMapper<OtcPrepWorkorderBinLocation> {

    /**
     * 根据条件获取OTC预提工单仓储位置列表
     *
     * @param query 查询条件
     * @return OTC预提工单仓储位置集合
     */
    default List<OtcPrepWorkorderBinLocationPageVO> listByQuery(OtcPrepWorkorderBinLocationQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取OTC预提工单仓储位置分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return OTC预提工单仓储位置集合
     */
    List<OtcPrepWorkorderBinLocationPageVO> listByQuery(@Param("qo") OtcPrepWorkorderBinLocationQuery query, @Param("page") Page<?> page);
}