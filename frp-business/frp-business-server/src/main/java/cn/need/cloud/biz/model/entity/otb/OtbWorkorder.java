package cn.need.cloud.biz.model.entity.otb;

import cn.need.cloud.biz.client.constant.enums.base.ShipTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otb.PaperTypeEnum;
import cn.need.cloud.biz.model.entity.base.WorkorderModel;
import cn.need.cloud.dfs.client.constant.enums.FileDataTypeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;


/**
 * <p>
 * OTB工单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otb_workorder")
public class OtbWorkorder extends WorkorderModel {


    @Serial
    private static final long serialVersionUID = 6276706864734174195L;
    /**
     * request快照RefNum
     */
    @TableField("request_snapshot_ref_num")
    private String requestSnapshotRefNum;

    /**
     * request快照备注
     */
    @TableField("request_snapshot_note")
    private String requestSnapshotNote;

    /**
     * request快照渠道
     */
    @TableField("request_snapshot_channel")
    private String requestSnapshotChannel;

    /**
     * request快照transactionPartnerId
     */
    @TableField("request_snapshot_transaction_partner_id")
    private Long requestSnapshotTransactionPartnerId;

    /**
     * 请求快照RequestRefnum
     */
    @TableField("request_snapshot_request_ref_num")
    private String requestSnapshotRequestRefNum;

    /**
     * otb 工单状态
     */
    @TableField("otb_workorder_status")
    private String otbWorkorderStatus;

    /**
     * otb请求id
     */
    @TableField("otb_request_id")
    private Long otbRequestId;


    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 唯一标识码
     */
    @TableField("ref_num")
    private String refNum;

    /**
     * 产品类型
     */
    @TableField("detail_product_type")
    private String detailProductType;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * otb 请求装运状态
     */
    @TableField("otb_request_shipment_status")
    private String otbRequestShipmentStatus;

    /**
     * 请求快照 发货窗口开始时间
     */
    @TableField("request_snapshot_ship_window_start")
    private LocalDateTime requestSnapshotShipWindowStart;

    /**
     * 请求快照 订单号
     */
    @TableField("request_snapshot_order_num")
    private String requestSnapshotOrderNum;

    /**
     * 请求快照 发货窗口结束时间
     */
    @TableField("request_snapshot_ship_window_end")
    private LocalDateTime requestSnapshotShipWindowEnd;

    /**
     * 产品类型
     */
    @TableField("workorder_product_type")
    private String workorderProductType;

    /**
     * 订单类型，LTL,SmallParcel
     */
    @TableField("otb_workorder_type")
    private String otbWorkorderType;

    /**
     * otb拣货单id
     */
    @TableField("otb_picking_slip_id")
    private Long otbPickingSlipId;


    /**
     * 发货类型
     */
    @TableField("ship_type")
    private ShipTypeEnum shipType;

    /**
     * RI文件数据
     */
    @TableField("routing_instruction_file_file_data")
    private String routingInstructionFileFileData;

    /**
     * RI文件扩展名
     */
    @TableField("routing_instruction_file_file_extension")
    private String routingInstructionFileFileExtension;

    /**
     * RI文件类型
     */
    @TableField("routing_instruction_file_file_type")
    private FileDataTypeEnum routingInstructionFileFileType;

    /**
     * RI文件纸张类型
     */
    @TableField("routing_instruction_file_paper_type")
    private PaperTypeEnum routingInstructionFilePaperType;

}
