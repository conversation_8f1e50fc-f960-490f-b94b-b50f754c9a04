package cn.need.cloud.biz.model.vo.otb.page;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.base.RelatedProductVO;
import cn.need.cloud.biz.model.vo.base.aware.BaseBinLocationAware;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * otb预提工单仓储位置 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTB预提工单仓储位置 vo对象")
public class OtbPrepWorkorderBinLocationPageVO extends BaseSuperVO implements BaseBinLocationAware {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * 预拣货id
     */
    @Schema(description = "预拣货id")
    private Long otbPrepPickingSlipId;

    /**
     * 预拣货
     */
    @Schema(description = "预拣货")
    private RefNumVO otbPrepPickingSlip;

    /**
     * 工单id
     */
    @Schema(description = "工单id")
    private Long otbWorkorderId;

    /**
     * 工单
     */
    @Schema(description = "工单")
    private RefNumVO otbWorkOrder;

    /**
     * 工单详情id
     */
    @Schema(description = "工单详情id")
    private Long otbWorkorderDetailId;

    /**
     * 工单详情
     */
    @Schema(description = "工单详情")
    private RelatedProductVO otbWorkOrderDetail;

    /**
     * 预工单id
     */
    @Schema(description = "预工单id")
    private Long otbPrepWorkorderId;

    /**
     * Prep工单
     */
    @Schema(description = "Prep工单")
    private RefNumVO otbPrepWorkOrder;

    /**
     * 预工单详情id
     */
    @Schema(description = "预工单详情id")
    private Long otbPrepWorkorderDetailId;

    /**
     * Prep工单详情
     */
    @Schema(description = "Prep工单详情")
    private RelatedProductVO otbPrepWorkOrderDetail;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

}