package cn.need.cloud.biz.model.param.base.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * OtcPickingSlipRollbackUpdateParam
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Data
@Schema(description = "拣货单-cancel参数")
public class PickingSlipCancelUpdateParam implements Serializable {


    @NotNull(message = "idList is must not null")
    @NotEmpty(message = "idList is must not empty")
    private List<Long> idList;

    @NotNull(message = "note is must not null")
    @NotBlank(message = "note is must not empty")
    private String note;
}
