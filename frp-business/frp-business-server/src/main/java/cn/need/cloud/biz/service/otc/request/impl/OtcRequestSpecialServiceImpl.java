package cn.need.cloud.biz.service.otc.request.impl;

import cn.need.cloud.biz.client.constant.ErrorConstant;
import cn.need.cloud.biz.client.constant.enums.base.RequestStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcWorkorderStatusEnum;
import cn.need.cloud.biz.model.entity.otc.OtcRequest;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorder;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestCancelContext;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestCancelParam;
import cn.need.cloud.biz.model.query.RequestListQuery;
import cn.need.cloud.biz.model.vo.base.request.RequestConfirmDetailVO;
import cn.need.cloud.biz.model.vo.base.request.RequestConfirmVO;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcRequestAuditLogHelper;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageSpecialService;
import cn.need.cloud.biz.service.otc.request.OtcRequestDetailService;
import cn.need.cloud.biz.service.otc.request.OtcRequestService;
import cn.need.cloud.biz.service.otc.request.OtcRequestSpecialService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderSpecialService;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.model.IdModel;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * OTC请求 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@AllArgsConstructor(onConstructor_ = @Lazy)
public class OtcRequestSpecialServiceImpl implements OtcRequestSpecialService {

    // ======================================== 常量定义 ========================================

    private static final String OPERATION_FAILED_UPDATE_REQUEST = "Request status update failed";
    private static final String OPERATION_FAILED_UPDATE_REQUEST_BATCH = "Update Request status failed";
    private static final String PARAMETER_CANNOT_BE_EMPTY = "Parameter cannot be empty";

    // ======================================== 依赖注入 ========================================

    private final OtcRequestService otcRequestService;
    private final OtcRequestDetailService otcRequestDetailService;
    private final OtcWorkorderSpecialService otcWorkorderSpecialService;
    private final OtcWorkorderService otcWorkorderService;
    private final OtcPackageSpecialService otcPackageSpecialService;


    // ======================================== 公共方法 ========================================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancel(OtcRequestCancelParam param) {
        // 获取并校验请求实体
        OtcRequest entity = getAndValidateRequest(param.getId());

        // 校验取消状态
        validateCancelStatus(entity);

        param.setRequest(entity);
        // 执行工单取消操作
        List<OtcWorkorder> workorderList = otcWorkorderSpecialService.allCancel(param);

        // 更新请求状态
        updateRequestCancelStatus(entity, workorderList);

        // 记录日志
        OtcRequestAuditLogHelper.recordLog(entity, param.getNote(), null);
        return 1;
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelPro(OtcRequestCancelParam param) {

        // 获取并校验请求实体
        OtcRequest request = getAndValidateRequest(param.getId());

        // 校验取消状态
        String otcRequestStatus = request.getOtcRequestStatus();
        List<String> canCancel = RequestStatusEnum.getCanCancel();

        Validate.isTrue(canCancel.contains(otcRequestStatus),
                ErrorConstant.STATUS_ERROR_FORMAT, request.refNumLog(), "cancel", canCancel, otcRequestStatus);
        //更新请求状态
        request.setOtcRequestStatus(RequestStatusEnum.CANCELLED.getStatus());

        // 记录日志
        OtcRequestAuditLogHelper.recordLog(request, param.getNote(), null);

        OtcRequestCancelContext context = new OtcRequestCancelContext();
        context.setRequest(request);

        otcWorkorderSpecialService.startCancel(context);

        //todo:根据 Workorder cancel 的返回值，来判断  request 是否需要更改为 onhold,or partCancelled


        Validate.isTrue(otcRequestService.update(request) == 1, " [{}] Update request failed", "cancelByRequest");

    }

    /**
     * 获取并校验请求实体
     *
     * @param requestId 请求ID
     * @return 请求实体
     */
    private OtcRequest getAndValidateRequest(Long requestId) {
        OtcRequest entity = otcRequestService.getById(requestId);
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(PARAMETER_CANNOT_BE_EMPTY);
        }
        return entity;
    }

    /**
     * 校验取消状态
     *
     * @param entity 请求实体
     */
    private void validateCancelStatus(OtcRequest entity) {
        String otcRequestStatus = entity.getOtcRequestStatus();
        List<String> canCancel = RequestStatusEnum.getCanCancel();

        Validate.isTrue(canCancel.contains(otcRequestStatus),
                ErrorConstant.STATUS_ERROR_FORMAT, entity.refNumLog(), "cancel", canCancel, otcRequestStatus);
    }

    /**
     * 更新请求取消状态
     *
     * @param entity        请求实体
     * @param workorderList 工单列表
     */
    private void updateRequestCancelStatus(OtcRequest entity, List<OtcWorkorder> workorderList) {
        if (ObjectUtil.isEmpty(workorderList)) {
            // 没有工单，直接取消
            entity.setOtcRequestStatus(RequestStatusEnum.CANCELLED.getStatus());
        } else {
            // 根据工单状态决定请求状态
            boolean allCancelled = workorderList.stream()
                    .allMatch(workorder -> OtcWorkorderStatusEnum.CANCELLED.getStatus().equals(workorder.getOtcWorkorderStatus()));

            entity.setOtcRequestStatus(allCancelled
                    ? RequestStatusEnum.CANCELLED.getStatus()
                    : RequestStatusEnum.ON_HOLD.getStatus());
        }

        int updateResult = otcRequestService.update(entity);
        Validate.isTrue(updateResult == 1, OPERATION_FAILED_UPDATE_REQUEST);
    }

    @Override
    public void finishCancel(List<Long> requestIdList) {
        List<OtcRequest> requests = otcRequestService.listByIds(requestIdList);
        Map<Long, OtcRequest> requestMap = StreamUtils.toMap(requests, IdModel::getId);
        Map<Long, List<OtcWorkorder>> workorderGroupRequestMap = otcWorkorderService.groupByRequestIdList(requestIdList);

        // 更新请求状态并记录日志
        updateRequestStatusBasedOnWorkorders(workorderGroupRequestMap, requestMap);

        // 批量更新请求
        Validate.isTrue(otcRequestService.updateBatch(requests) == requests.size(), OPERATION_FAILED_UPDATE_REQUEST_BATCH);
    }

    /**
     * 根据工单状态更新请求状态
     *
     * @param workorderGroupRequestMap 工单分组映射
     * @param requestMap               请求映射
     */
    private void updateRequestStatusBasedOnWorkorders(Map<Long, List<OtcWorkorder>> workorderGroupRequestMap,
                                                      Map<Long, OtcRequest> requestMap) {
        workorderGroupRequestMap.forEach((requestId, workorders) -> {
            boolean allCancel = workorders.stream()
                    .allMatch(workorder -> OtcWorkorderStatusEnum.CANCELLED.getStatus().equals(workorder.getOtcWorkorderStatus()));

            OtcRequest request = requestMap.get(requestId);
            String oldStatus = request.getOtcRequestStatus();

            if (allCancel) {
                request.setOtcRequestStatus(RequestStatusEnum.CANCELLED.getStatus());
            }
            // 如果不是全部取消，保持原状态

            // 记录状态变更日志
            if (!Objects.equals(oldStatus, request.getOtcRequestStatus())) {
                OtcRequestAuditLogHelper.recordLog(request);
            }
        });
    }

    @Override
    public List<RequestConfirmDetailVO> cancelConfirm(RequestListQuery query) {
        var requests = otcRequestService.listByIds(query.getIdList());
        Validate.notEmpty(requests, "Request Ids {} not exist", query.getIdList());

        var requestMap = StreamUtils.toMap(requests, IdModel::getId);
        return otcRequestDetailService.listByRequestIds(query.getIdList()).stream()
                .map(obj -> {
                    var confirmDetail = BeanUtil.copyNew(obj, RequestConfirmDetailVO.class);
                    var request = requestMap.get(obj.getOtcRequestId());
                    var confirm = BeanUtil.copyNew(request, RequestConfirmVO.class);
                    confirm.setRequestStatus(request.getOtcRequestStatus());
                    confirmDetail.setRequest(confirm);
                    return confirmDetail;
                })
                .toList();

    }
}
