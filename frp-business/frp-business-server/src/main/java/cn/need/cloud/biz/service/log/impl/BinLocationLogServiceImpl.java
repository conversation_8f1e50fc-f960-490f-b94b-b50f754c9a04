package cn.need.cloud.biz.service.log.impl;

import cn.need.cloud.biz.cache.bean.BinLocationCache;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.log.BinLocationLogConverter;
import cn.need.cloud.biz.mapper.log.BinLocationLogMapper;
import cn.need.cloud.biz.model.entity.base.RefNumModel;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.log.BinLocationLog;
import cn.need.cloud.biz.model.query.log.BinLocationLogQuery;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationChangeVO;
import cn.need.cloud.biz.model.vo.log.BinLocationLogPageVO;
import cn.need.cloud.biz.model.vo.log.BinLocationLogVO;
import cn.need.cloud.biz.service.log.BinLocationLogService;
import cn.need.cloud.biz.util.BinLocationCacheUtil;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.log.AuditLogUtil;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
@DS("log_master")
@Service
public class BinLocationLogServiceImpl extends SuperServiceImpl<BinLocationLogMapper, BinLocationLog> implements BinLocationLogService {
    @Resource
    @Lazy
    private BinLocationLogService self;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public int insert(BinLocationLog binLocationLog) {
        // 执行日志信息的插入操作
        return super.insert(binLocationLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public int insertBatch(Collection<? extends BinLocationLog> binLocationLogList) {
        if (ObjectUtil.isEmpty(binLocationLogList)) {
            return DataState.DISABLED;
        }
        log.debug("插入库位日志：{}", JsonUtil.toJson(binLocationLogList));
        // 执行日志信息的插入操作
        return super.insertBatch(binLocationLogList);
    }

    @Override
    public List<BinLocationLogPageVO> listByQuery(BinLocationLogQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<BinLocationLogPageVO> pageByQuery(PageSearch<BinLocationLogQuery> search) {
        //获取分页参数
        Page<BinLocationLog> page = Conditions.page(search, entityClass);
        //获取查询条件
        BinLocationLogQuery condition = search.getCondition();
        //获取分页列表
        List<BinLocationLogPageVO> dataList = mapper.listByQuery(condition, page);
        //获取库位id
        Set<Long> binLocationIdList = dataList
                .stream()
                .flatMap(item -> Stream.of(item.getDestBinLocationId(), item.getSourceBinLocationId()))
                .collect(Collectors.toSet());
        //根据库位id映射库位名称
        Map<Long, String> nameMap = ObjectUtil.toMap(BinLocationCacheUtil.listByIds(binLocationIdList), BinLocationCache::getId, BinLocationCache::getLocationName);
        //遍历日志
        dataList.forEach(item -> {
            item.setSourceBinLocationName(nameMap.get(item.getSourceBinLocationId()));
            item.setDestBinLocationName(nameMap.get(item.getDestBinLocationId()));
        });
        //返回查询条件
        return new PageData<>(dataList, page);
    }

    @Override
    public BinLocationLogVO detailById(Long id) {
        BinLocationLog entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in BinLocationLog");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "BinLocationLog", id));
        }
        return buildBinLocationLogVO(entity);
    }

    /**
     * 保存日志信息到数据库
     * 此方法使用了数据源动态切换机制，以便将日志信息保存到指定的数据库中
     * 选择特定的数据源进行操作是基于日志记录的特殊需求，例如，确保日志数据和主业务数据分离，
     * 提高系统的可维护性和性能
     *
     * @param binLocationLog 要保存的日志对象，包含了日志的所有必要信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void save(BinLocationLog binLocationLog) {
        // 执行日志信息的插入操作
        mapper.insert(binLocationLog);
    }

    /**
     * 保存日志信息到数据库
     * 此方法使用了数据源动态切换机制，以便将日志信息保存到指定的数据库中
     * 选择特定的数据源进行操作是基于日志记录的特殊需求，例如，确保日志数据和主业务数据分离，
     * 提高系统的可维护性和性能
     *
     * @param model    基类
     * @param source   起始库位
     * @param changeVO 变化信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void save(RefNumModel model, BinLocationDetail source, BinLocationChangeVO changeVO) {
        //持久化库位日志
        self.insert(buildBinLocationLog(model, source, changeVO));
    }

    @Override
    public BinLocationLog buildBinLocationLog(RefNumModel model, BinLocationDetail dest, BinLocationChangeVO changeVO) {
        //判空
        return AuditLogUtil.binLocationLog(model, dest)
                .with(BinLocationLog::setSourceAfterInStockQty, dest.getInStockQty())
                .with(BinLocationLog::setSourceChangeInStockQty, changeVO.getChangeInStockQty())
                .with(BinLocationLog::setSourceBeforeInStockQty, dest.getInStockQty() - changeVO.getChangeInStockQty())
                .with(BinLocationLog::setDestAfterInStockQty, dest.getInStockQty())
                .with(BinLocationLog::setDestChangeInStockQty, changeVO.getChangeInStockQty())
                .with(BinLocationLog::setDestBeforeInStockQty, dest.getInStockQty() - changeVO.getChangeInStockQty())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void save(RefNumModel model, BinLocationDetail source, BinLocationChangeVO changeVO, String changeType) {
        //持久化库位日志
        self.insert(buildBinLocationLogWithChangeType(model, source, changeVO, changeType));
    }

    @Override
    public BinLocationLog buildBinLocationLogWithChangeType(RefNumModel model, BinLocationDetail dest, BinLocationChangeVO changeVO, String changeType) {
        //判空
        return AuditLogUtil.binLocationLog(model, dest)
                .with(BinLocationLog::setSourceAfterInStockQty, dest.getInStockQty())
                .with(BinLocationLog::setSourceChangeInStockQty, changeVO.getChangeInStockQty())
                .with(BinLocationLog::setSourceBeforeInStockQty, dest.getInStockQty() - changeVO.getChangeInStockQty())
                .with(BinLocationLog::setDestAfterInStockQty, dest.getInStockQty())
                .with(BinLocationLog::setDestChangeInStockQty, changeVO.getChangeInStockQty())
                .with(BinLocationLog::setDestBeforeInStockQty, dest.getInStockQty() - changeVO.getChangeInStockQty())
                .with(BinLocationLog::setChangeType, changeType)
                .build();
    }

    /**
     * 构建VO对象
     *
     * @param entity 对象
     * @return 返回包含详细信息的VO对象
     */
    private BinLocationLogVO buildBinLocationLogVO(BinLocationLog entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的VO对象
        return Converters.get(BinLocationLogConverter.class).toVO(entity);
    }
}
