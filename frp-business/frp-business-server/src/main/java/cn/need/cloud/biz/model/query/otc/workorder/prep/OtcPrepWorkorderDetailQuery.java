package cn.need.cloud.biz.model.query.otc.workorder.prep;

import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * OTC预提工单详情 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTC预提工单详情 query对象")
public class OtcPrepWorkorderDetailQuery extends SuperQuery {

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 发货到c端预工单id
     */
    @Schema(description = "发货到c端预工单id")
    private Long otcPrepWorkorderId;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 库存锁定id
     */
    @Schema(description = "库存锁定id")
    private Long inventoryLockedId;

    /**
     * 拣货数量
     */
    @Schema(description = "拣货数量")
    private Integer pickedQty;

    /**
     * 上架数量
     */
    @Schema(description = "上架数量")
    private Integer putawayQty;

    /**
     * 父节点id
     */
    @Schema(description = "父节点id")
    private Long parentId;

    /**
     * 预工单详情类型
     */
    @Schema(description = "预工单详情类型")
    private String prepWorkorderDetailType;

    /**
     * 预工单详情产品版本
     */
    @Schema(description = "预工单详情产品版本")
    private Integer prepWorkorderDetailVersionInt;


}