package cn.need.cloud.biz.model.vo.page;

import cn.need.cloud.biz.client.constant.enums.base.ShipTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otb.PaperTypeEnum;
import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipWorkorderVO;
import cn.need.cloud.dfs.client.constant.enums.FileDataTypeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * otb拣货单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "otb拣货单 vo对象")
public class OtbPickingSlipPageVO extends BaseSuperVO {

    @Serial
    private static final long serialVersionUID = -7295893917893965358L;
    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String detailProductType;

    /**
     * otb工单id
     */
    @Schema(description = "otb工单id")
    private Long otbWorkorderId;

    /**
     * otb工单
     */
    @Schema(description = "otb工单")
    private OtbPickingSlipWorkorderVO otbWorkorder;

    /**
     * 拣货到那里
     */
    @Schema(description = "拣货到那里")
    private String pickToStation;


    @Schema(description = "拣货产品类型")
    private String pickingSlipProductType;

    /**
     * otb 拣货单状态
     */
    @Schema(description = "otb 拣货单状态")
    private String otbPickingSlipStatus;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 由谁构建
     */
    @Schema(description = "由谁构建")
    private String buildFromType;

    /**
     * 由谁拣货
     */
    @Schema(description = "由谁拣货")
    private String pickFromType;

    @Schema(description = "流程类型")
    private String processType;

    /**
     * d
     * 发货类型
     */
    @Schema(description = "发货类型")
    private ShipTypeEnum shipType;

    // /**
    //  * RI文件
    //  */
    // @Schema(description = "RI文件")
    // private FrpFileModel routingInstructionFile;

    /**
     * RI文件数据
     */
    @TableField("routing_instruction_file_file_data")
    private String routingInstructionFileFileData;

    /**
     * RI文件扩展名
     */
    @TableField("routing_instruction_file_file_extension")
    private String routingInstructionFileFileExtension;

    /**
     * RI文件类型
     */
    @TableField("routing_instruction_file_file_type")
    private FileDataTypeEnum routingInstructionFileFileType;

    /**
     * RI文件纸张类型
     */
    @TableField("routing_instruction_file_paper_type")
    private PaperTypeEnum routingInstructionFilePaperType;

}