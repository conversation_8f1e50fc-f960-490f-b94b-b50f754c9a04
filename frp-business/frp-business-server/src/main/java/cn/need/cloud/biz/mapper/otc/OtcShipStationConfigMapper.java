package cn.need.cloud.biz.mapper.otc;

import cn.need.cloud.biz.model.entity.otc.OtcShipStationConfig;
import cn.need.cloud.biz.model.query.otc.ship.OtcShipStationConfigQuery;
import cn.need.cloud.biz.model.vo.page.OtcShipStationConfigPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 快递公司配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtcShipStationConfigMapper extends SuperMapper<OtcShipStationConfig> {

    /**
     * 根据条件获取快递公司配置列表
     *
     * @param query 查询条件
     * @return 快递公司配置集合
     */
    default List<OtcShipStationConfigPageVO> listByQuery(OtcShipStationConfigQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取快递公司配置分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 快递公司配置集合
     */
    List<OtcShipStationConfigPageVO> listByQuery(@Param("qo") OtcShipStationConfigQuery query, @Param("page") Page<?> page);
}