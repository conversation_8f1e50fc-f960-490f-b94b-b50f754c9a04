package cn.need.cloud.biz.model.vo.otc;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * OTC运输托盘 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "OTC运输托盘标签 vo对象")
public class OtcShipPalletLabelVO implements Serializable {


    private Long id;

    /**
     * 托盘标签物流追踪码
     */
    @Schema(description = "托盘标签物流追踪码")
    private String labelRefNum;

    /**
     * 原始托盘标签类型
     */
    @Schema(description = "原始托盘标签类型")
    private String rawDataType;
    /**
     * 原始托盘标签数据
     */
    @Schema(description = "原始托盘标签数据")
    private String labelRawData;

    /**
     * 托盘标签纸张类型
     */
    @Schema(description = "托盘标签纸张类型")
    private String paperType;

    /**
     * 文件系统数据类型
     */
    @Schema(description = "文件系统数据类型")
    private String fileIdRawDataType;


}