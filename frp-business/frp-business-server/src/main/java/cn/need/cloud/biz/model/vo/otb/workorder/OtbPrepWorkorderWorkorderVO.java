package cn.need.cloud.biz.model.vo.otb.workorder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * OTB包裹详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "OTB Prep工单 工单 vo对象")
public class OtbPrepWorkorderWorkorderVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 5038613688158674289L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String detailProductType;

    /**
     * request快照RefNum
     */
    @Schema(description = "request快照RefNum")
    private String requestSnapshotRefNum;

    /**
     * request快照requestRefNum
     */
    @Schema(description = "request快照requestRefNum")
    private String requestSnapshotRequestRefNum;

    /**
     * request快照orderNum
     */
    @Schema(description = "request快照orderNum")
    private String requestSnapshotOrderNum;

    /**
     * 请求快照 发货窗口开始时间
     */
    @Schema(description = "请求快照 发货窗口开始时间")
    private LocalDateTime requestSnapshotShipWindowStart;

    /**
     * 请求快照 发货窗口结束时间
     */
    @Schema(description = "请求快照 发货窗口结束时间")
    private LocalDateTime requestSnapshotShipWindowEnd;
}