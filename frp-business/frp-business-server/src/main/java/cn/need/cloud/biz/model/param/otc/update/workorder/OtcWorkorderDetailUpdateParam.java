package cn.need.cloud.biz.model.param.otc.update.workorder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;


/**
 * OTC工单详情 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC工单详情 vo对象")
public class OtcWorkorderDetailUpdateParam implements Serializable {


    @Serial
    private static final long serialVersionUID = -2188935199526192703L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 库存锁定id
     */
    @Schema(description = "库存锁定id")
    private Long inventoryLockedId;

    /**
     * 打包为Package 数量
     */
    @Schema(description = "打包为Package 数量")
    private Integer packedQty;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    @Size(max = 64, message = "hazmatVersionRefNum cannot exceed 64 characters")
    private String hazmatVersionRefNum;

    /**
     * 发货到c端工单id
     */
    @Schema(description = "发货到c端工单id")
    private Long otcWorkorderId;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * finishQty
     */
    @Schema(description = "finishQty")
    private Integer finishQty;

    /**
     * 拣货数量
     */
    @Schema(description = "拣货数量")
    private Integer pickedQty;

    /**
     * readyToShipQty
     */
    @Schema(description = "readyToShipQty")
    private Integer readyToShipQty;

    /**
     * 库存预定id
     */
    @Schema(description = "库存预定id")
    private Long inventoryReserveId;

    /**
     * 预定数量
     */
    @Schema(description = "预定数量")
    private Integer reserveQty;

}