package cn.need.cloud.biz.model.entity.otc;

import cn.need.cloud.biz.model.entity.base.WorkorderModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <p>
 * OTC工单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otc_workorder")
public class OtcWorkorder extends WorkorderModel {


    @Serial
    private static final long serialVersionUID = -2629169825078282542L;
    /**
     * 发货到c端请求id
     */
    @TableField("otc_request_id")
    private Long otcRequestId;

    /**
     * 发货到c端拣货id
     */
    @TableField("otc_picking_slip_id")
    private Long otcPickingSlipId;

    /**
     * 请求快照快递运输标志
     */
    @TableField("request_snapshot_ship_express_flag")
    private Boolean requestSnapshotShipExpressFlag;

    /**
     * 运输方式
     */
    @TableField("ship_method")
    private String shipMethod;

    /**
     * 运输公司
     */
    @TableField("ship_carrier")
    private String shipCarrier;

    /**
     * 订单类型
     */
    @TableField("order_type")
    private String orderType;

    /**
     * 请求快照最后运输时间
     */
    @TableField("request_snapshot_last_ship_date")
    private LocalDateTime requestSnapshotLastShipDate;

    /**
     * 请求快照保险金额
     */
    @TableField("request_snapshot_insurance_amount_amount")
    private BigDecimal requestSnapshotInsuranceAmountAmount;

    /**
     * 请求快照签名类型
     */
    @TableField("request_snapshot_signature_type")
    private String requestSnapshotSignatureType;

    /**
     * 请求快照订单类型
     */
    @TableField("request_snapshot_order_type")
    private String requestSnapshotOrderType;

    /**
     * 请求快照是否有特定运输要求
     */
    @TableField("request_snapshot_has_cus_ship_require")
    private Boolean requestSnapshotHasCusShipRequire;

    /**
     * 请求快照运输方式
     */
    @TableField("request_snapshot_ship_method")
    private String requestSnapshotShipMethod;

    /**
     * 请求快照提供运输标签标志
     */
    @TableField("request_snapshot_provide_shipping_label_flag")
    private Boolean requestSnapshotProvideShippingLabelFlag;

    /**
     * 发货到c端工单状态
     */
    @TableField("otc_workorder_status")
    private String otcWorkorderStatus;

    /**
     * 构建运输打包类型
     */
    @TableField("build_ship_package_type")
    private String buildShipPackageType;


    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 唯一标识码
     */
    @TableField("ref_num")
    private String refNum;

    /**
     * 产品类型
     */
    @TableField("detail_product_type")
    private String detailProductType;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * request快照备注
     */
    @TableField("request_snapshot_note")
    private String requestSnapshotNote;

    /**
     * request快照RefNum
     */
    @TableField("request_snapshot_ref_num")
    private String requestSnapshotRefNum;

    /**
     * 请求快照RequestRefnum
     */
    @TableField("request_snapshot_request_ref_num")
    private String requestSnapshotRequestRefNum;

    /**
     * 请求快照运输公司
     */
    @TableField("request_snapshot_ship_carrier")
    private String requestSnapshotShipCarrier;

    /**
     * request快照transactionPartnerId
     */
    @TableField("request_snapshot_transaction_partner_id")
    private Long requestSnapshotTransactionPartnerId;

    /**
     * request快照渠道
     */
    @TableField("request_snapshot_channel")
    private String requestSnapshotChannel;

    /**
     * 预处理工单状态
     */
    @TableField("workorder_prep_status")
    private String workorderPrepStatus;

    /**
     * 请求快照保险金货币
     */
    @TableField("request_snapshot_insurance_amount_currency")
    private String requestSnapshotInsuranceAmountCurrency;


    /**
     * 快递Api配置RefNum
     */
    @TableField("request_snapshot_ship_api_profile_ref_num")
    private String requestSnapshotShipApiProfileRefNum;


    /**
     * 拣货到那里
     */
    @TableField("pick_to_station")
    private String pickToStation;

    /**
     * 锁定前
     */
    @TableField("locked_before")
    private String lockedBefore;

    /**
     * 产品类型
     */
    @TableField("workorder_product_type")
    private String workorderProductType;

}
