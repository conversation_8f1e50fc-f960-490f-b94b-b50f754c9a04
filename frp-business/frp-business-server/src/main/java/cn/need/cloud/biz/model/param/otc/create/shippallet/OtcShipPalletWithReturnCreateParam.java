package cn.need.cloud.biz.model.param.otc.create.shippallet;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * OTC运输托盘 build Pallet create param对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC运输托盘 build Pallet create param对象")
public class OtcShipPalletWithReturnCreateParam implements Serializable {


    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    @NotBlank(message = "shipCarrier is must not blank")
    @NotNull(message = "shipCarrier is must not null")
    private String shipCarrier;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 详情创建参数
     */
    @Schema(description = "运输托盘详情创建参数")
    @NotEmpty(message = "detailList is must not empty")
    @Valid
    private List<OtcShipPalletDetailCreateParam> detailList;
}