package cn.need.cloud.biz.model.entity.binlocation;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * 预留库位
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bin_location_reserve")
public class BinLocationReserve extends SuperModel {


    /**
     * 数量
     */
    @TableField("qty")
    private Integer qty;

    @TableField("finish_qty")
    private Integer finishQty;

    /**
     * 库位id
     */
    @TableField("bin_location_id")
    private Long binLocationId;


    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 产品版本id
     */
    @TableField("product_version_id")
    private Long productVersionId;

}
