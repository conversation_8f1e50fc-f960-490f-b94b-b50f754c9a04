package cn.need.cloud.biz.model.entity.base;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/***
 * 拥有产品的模型
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProductModel extends SuperModel {

    /**
     * 唯一标识码
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 数量
     */
    @TableField("qty")
    private Integer qty;

    /**
     * 拣货数量
     */
    @TableField("picked_qty")
    private Integer pickedQty;
}
