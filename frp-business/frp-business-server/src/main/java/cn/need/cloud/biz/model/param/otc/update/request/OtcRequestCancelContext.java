package cn.need.cloud.biz.model.param.otc.update.request;

import cn.hutool.core.util.ObjectUtil;
import cn.need.cloud.biz.client.constant.enums.inventory.WorkOrderPrepStatusEnum;
import cn.need.cloud.biz.model.entity.otc.*;
import cn.need.framework.common.mybatis.model.IdModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * OtcRequestCancelParam
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
public class OtcRequestCancelContext {

    /**
     * 取消原因
     */
    @Schema(description = "原因")
    @NotNull(message = "note cannot be null")
    @NotBlank(message = "note cannot be blank")
    private String note;

    /**
     * 请求
     */
    private OtcRequest request;

    /**
     * 工单
     */
    private List<OtcWorkorder> workorderList;

    /**
     * 包裹
     */
    private List<OtcPackage> packageList;

    /**
     * Prep工单
     */
    private List<OtcPrepWorkorder> prepWorkorderList;

    /**
     * 拣货单
     */
    private List<OtcPickingSlip> pickingSlipList;

    /**
     * Prep拣货单
     */
    private List<OtcPrepPickingSlip> prepPickingSlipList;

    public List<Long> getWorkorderIdListByWorkorderList() {

        if (ObjectUtil.isEmpty(workorderList)) {
            return List.of();
        }

        return workorderList.stream().map(IdModel::getId).distinct().toList();
    }

    public List<Long> getPickingSlipIdsByPickingSlipList() {

        if (ObjectUtil.isEmpty(pickingSlipList)) {
            return List.of();
        }

        return pickingSlipList.stream().map(IdModel::getId).distinct().toList();
    }

    public List<Long> getPrepPickingSlipIdsByPrepWorkorderList() {

        if (ObjectUtil.isEmpty(prepWorkorderList)) {
            return List.of();
        }

        return prepWorkorderList.stream().map(OtcPrepWorkorder::getOtcPrepPickingSlipId).distinct().toList();
    }

    public List<Long> getWorkorderPrepIdListByWorkorderList() {
        if (ObjectUtil.isEmpty(workorderList)) {
            return List.of();
        }

        return workorderList.stream()
                .filter(obj -> !Objects.equals(obj.getWorkorderPrepStatus(), WorkOrderPrepStatusEnum.NONE.getStatus()))
                .map(IdModel::getId)
                .toList();
    }
}
