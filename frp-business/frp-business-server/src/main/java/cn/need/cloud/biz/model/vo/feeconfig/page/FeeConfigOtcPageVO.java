package cn.need.cloud.biz.model.vo.feeconfig.page;

import cn.need.cloud.biz.model.entity.base.FeeConfigAble;
import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.feeconfig.RefNumWithNameVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * 仓库报价费用配置otc 分页列表VO对象
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "仓库报价费用配置otc 分页列表VO对象")
public class FeeConfigOtcPageVO extends BaseSuperVO implements FeeConfigAble {

    @Serial
    private static final long serialVersionUID = 8051603457954187719L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效)")
    private Boolean activeFlag;

    /**
     * 计费条件类型（决定区间判断的依据）
     */
    @Schema(description = "计费条件类型（决定区间判断的依据）")
    private String conditionType;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY）")
    private String currency;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 额外计费判断字段
     */
    @Schema(description = "额外计费判断字段")
    private String extraFeeJudgeFields;

    /**
     * 费用计费类型(detail得出的结果，是否要乘condition_type得出的值)
     */
    @Schema(description = "费用计费类型(detail得出的结果，是否要乘condition_type得出的值)")
    private String feeCalculationType;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;
    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 仓库报价id
     */
    @Schema(description = "仓库报价id")
    private Long quoteId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 仓库报价
     */
    @Schema(description = "仓库报价")
    private RefNumWithNameVO quote;

}