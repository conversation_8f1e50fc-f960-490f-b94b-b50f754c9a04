package cn.need.cloud.biz.model.query.otb.pickingslip;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;


/**
 * otb拣货单详情 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "otb拣货单详情 query对象")
public class OtbPickingSlipDetailQuery extends SuperQuery {

    @Serial
    private static final long serialVersionUID = 7638322681056142728L;
    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 拣货数量
     */
    @Schema(description = "拣货数量")
    private Integer pickedQty;

    /**
     * 库位详情id
     */
    @Schema(description = "库位详情id")
    private Long binLocationDetailId;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * otb拣货单id
     */
    @Schema(description = "otb拣货单id")
    private Long otbPickingSlipId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    /**
     * 库位详情锁id
     */
    @Schema(description = "库位详情锁id")
    private Long binLocationDetailLockedId;

    /**
     * 打包为Package 数量
     */
    @Schema(description = "打包为Package 数量")
    private Integer packedQty;

    /**
     * 渠道要求的需要贴的产品标识码
     */
    @Schema(description = "渠道要求的需要贴的产品标识码")
    private String productBarcode;

    /**
     * 渠道要求的需要贴的产品标识码
     */
    @Schema(description = "渠道要求的需要贴的产品标识码集合")
    @Condition(value = Keyword.IN, fields = {"productBarcode"})
    private List<String> productBarcodeList;

    /**
     * relabel状态
     */
    @Schema(description = "relabel状态")
    private String relabelStatus;

    /**
     * relabel状态
     */
    @Schema(description = "relabel状态集合")
    @Condition(value = Keyword.IN, fields = {"relabelStatus"})
    private List<String> relabelStatusList;


}