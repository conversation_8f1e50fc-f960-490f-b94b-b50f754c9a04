package cn.need.cloud.biz.mapper.inbound;

import cn.need.cloud.biz.model.bo.common.DropColumnInfoBO;
import cn.need.cloud.biz.model.entity.inbound.InboundWorkorder;
import cn.need.cloud.biz.model.query.inbound.InboundWorkorderQuery;
import cn.need.cloud.biz.model.vo.page.InboundWorkorderPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 入库工单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface InboundWorkorderMapper extends SuperMapper<InboundWorkorder> {

    /**
     * 根据条件获取入库工单列表
     *
     * @param query 查询条件
     * @return 入库工单集合
     */
    default List<InboundWorkorderPageVO> listByQuery(InboundWorkorderQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取入库工单分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 入库工单集合
     */
    List<InboundWorkorderPageVO> listByQuery(@Param("qo") InboundWorkorderQuery query, @Param("page") Page<?> page);

    /**
     * 入库工单下拉列表
     *
     * @param columnList 查询字段名
     * @param qo         查询条件
     * @return 入库工单下拉列表
     */
    List<Map<String, Object>> dropProList(@Param("columnList") List<DropColumnInfoBO> columnList,
                                          @Param("qo") InboundWorkorderQuery qo);
}