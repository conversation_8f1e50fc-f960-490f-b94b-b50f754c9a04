package cn.need.cloud.biz.model.param.product.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 产品扫描 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "产品扫描 vo对象")
public class ProductScanUpdateParam implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 扫描编号
     */
    @Schema(description = "扫描编号")
    private String scanNum;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;


    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String productType;


}