package cn.need.cloud.biz.model.vo.base;

import cn.need.cloud.biz.model.vo.base.aware.BaseTransactionPartnerShowVO;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "产品 vo对象")
public class BaseProductVO extends BaseTransactionPartnerShowVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long id;

    // /**
    //  * 供应商ID
    //  */
    // @Schema(description = "供应商ID")
    // private Long transactionPartnerId;


    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;
    /**
     * 供应商SKU
     */
    @Schema(description = "供应商SKU")
    private String supplierSku;
    /**
     * UPC码
     */
    @Schema(description = "UPC码")
    private String upc;
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;
    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;
    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;

    //todo: 这个不合理，后面排查前后端引用，规范化取值

    /**
     * 供应商对象
     */
    @Schema(description = "供应商对象")
    private BasePartnerVO getTransactionPartner() {
        return getTransactionPartnerVO();
    }

    @Override
    public String toString() {
        if (ObjectUtil.isEmpty(getTransactionPartnerVO())) {
            return StringUtil.format("Product(refNum:{},supplierSku:{},upc:{}) ", refNum, supplierSku, upc);
        } else {
            return StringUtil.format("Product(refNum:{},supplierSku:{},upc:{},partnerAbbrName:{}) ", refNum, supplierSku, upc, getTransactionPartnerVO().getAbbrName());
        }
    }
}