package cn.need.cloud.biz.service.product;

import cn.need.cloud.biz.client.constant.enums.product.ProductTypeEnum;
import cn.need.cloud.biz.model.entity.product.Product;
import cn.need.cloud.biz.model.param.product.create.ProductCreateParam;
import cn.need.cloud.biz.model.param.product.update.ProductUpdateParam;
import cn.need.cloud.biz.model.query.product.ProductQuery;
import cn.need.cloud.biz.model.query.product.ProductTreeQuery;
import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import cn.need.cloud.biz.model.vo.base.product.PrepFullProductVO;
import cn.need.cloud.biz.model.vo.product.ProductVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 产品 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface ProductService extends SuperService<Product> {

    /**
     * 根据参数新增产品
     *
     * @param createParam 请求创建参数，包含需要插入的产品的相关信息
     * @return 产品
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Product insertByParam(ProductCreateParam createParam);


    /**
     * 根据参数更新产品
     *
     * @param updateParam 请求创建参数，包含需要更新的产品的相关信息
     * @return 产品
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    Product updateByParam(ProductUpdateParam updateParam);

    /**
     * 根据查询条件获取产品列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个产品对象的列表(分页)
     */
    List<ProductVO> listByQuery(ProductQuery query);

    /**
     * 根据查询条件获取产品列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个产品对象的列表(分页)
     */
    PageData<ProductVO> pageByQuery(PageSearch<ProductQuery> search);

    /**
     * 根据ID获取产品
     *
     * @param id 产品ID
     * @return 返回产品VO对象
     */
    ProductVO detailById(Long id);

    /**
     * 根据产品唯一编码获取产品
     *
     * @param refNum 产品唯一编码
     * @return 返回产品VO对象
     */
    ProductVO detailByRefNum(String refNum);


    /**
     * 根据upc，transactionPartnerId校验upc在同供应商下的唯一性
     *
     * @param transactionPartnerId 供应商id
     */
    void checkGlobalUniqueUpc(String upc, Long transactionPartnerId);

    /**
     * 根据产供应商id获取产品
     *
     * @param transactionPartnerId 供应商id
     * @return 返回产品对象
     */
    List<Product> getListByTransactionPartnerId(Long transactionPartnerId);

    /**
     * 根据产品id判断是否是Group
     *
     * @param id 产品id
     * @return 是则true；否则返回false
     */
    boolean isGroup(Long id);

    /**
     * 根据产品id判断是否是Group中的子类
     *
     * @param id 产品id
     * @return 是则true；否则返回false
     */
    boolean isChildGroup(Long id);


    /**
     * 根据产品id判断是否是Assembly
     *
     * @param id 产品id
     * @return 是则true；否则返回false
     */
    boolean isAssembly(Long id);

    /**
     * 根据产品id判断是否是Multibox
     *
     * @param id 产品id
     * @return 是则true；否则返回false
     */
    boolean isMultibox(Long id);

    /**
     * 根据产品id列表更新主件标志
     *
     * @param id 产品id
     */
    void updateAssemblyFlagById(Long id, Boolean flag);

    /**
     * 根据产品id更新多箱标志
     *
     * @param id   产品id，用于指定需要更新多箱标志的产品
     * @param flag 多箱标志，用于更新产品是否为多箱的状态
     */
    void updateMultiboxFlagById(Long id, Boolean flag);

    /**
     * 根据产品id列表更新组类型状态
     *
     * @param idList 产品id列表，用于指定需要更新组类型的产品集合
     * @param type   父子类型
     */
    void updateGroupTypeByIdList(List<Long> idList, String type);


    /**
     * 根据供应商id和产品id集合判断是否是该供应商的所有产品
     *
     * @param transactionPartnerId 供应商id
     * @param idList               产品id集合
     * @return 是否是该供应商的所有产品
     */
    boolean isAllProductInPartnerId(Long transactionPartnerId, List<Long> idList);

    /**
     * 根据产品列表检查 UPC（通用产品代码）的唯一性
     * 此方法确保在特定供应商下，所有产品的UPC是唯一的
     *
     * @param upcList              一组UPC代码，用于检查唯一性
     * @param transactionPartnerId 供应商ID，用于限定检查范围在特定供应商内
     */
    void checkUniqueUpcByList(Set<String> upcList, Long transactionPartnerId);

    /**
     * 根据产品id集合获取产品基础信息映射信息
     *
     * @param productIdList productIds
     * @return 映射信息
     */
    Map<Long, BaseProductVO> baseProductByIds(List<Long> productIdList);

    /**
     * 根据产品id集合获取产品基础信息
     *
     * @param productIdList productIds
     * @return 基础信息
     */
    BaseProductVO baseProductById(Long productIdList);


    /**
     * 获取产品描述信息
     *
     * @param productId               产品id
     * @param prepWorkOrderVersionInt Prep工单版本
     * @param prepWorkOrderType       Prep工单类型
     * @return /
     */
    PrepFullProductVO getPrepProductDescription(Long productId, Integer prepWorkOrderVersionInt, String prepWorkOrderType);

    /**
     * 获取产品描述树
     *
     * @param productTreeQuery 查询条件
     * @return /
     */
    PrepFullProductVO getPrepProductTree(ProductTreeQuery productTreeQuery);

    /**
     * 根据供应商SKU获取产品
     *
     * @param supplierSku          供应商SKU
     * @param transactionPartnerId 交易伙伴ID
     * @return /
     */
    Product getBySupplierSku(String supplierSku, Long transactionPartnerId);

    /**
     * 根据refNum、supplierSku和transactionPartnerId获取产品
     *
     * @param refNum               产品唯一标识
     * @param supplierSku          供应商SKU
     * @param transactionPartnerId 交易伙伴ID
     * @return /
     */
    Product get(String refNum, String supplierSku, Long transactionPartnerId);

    /**
     * 更新产品类型
     *
     * @param productId       产品ID
     * @param productTypeEnum 产品类型枚举
     */
    void updateProductType(Long productId, ProductTypeEnum productTypeEnum);
}