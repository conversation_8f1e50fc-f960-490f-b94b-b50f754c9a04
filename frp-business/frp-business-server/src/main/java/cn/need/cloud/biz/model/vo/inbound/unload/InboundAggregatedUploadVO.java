package cn.need.cloud.biz.model.vo.inbound.unload;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 入库工单卸货表 根据这个来生成上架单 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@Schema(description = "入库工单卸货表 根据这个来生成上架单 vo对象")
public class InboundAggregatedUploadVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 入库工单id
     */
    @Schema(description = "入库工单id")
    private Long inBoundWorkOrderId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 版本产品id
     */
    @Schema(description = "版本产品id")
    private Long productVersionId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 卸货备注
     */
    @Schema(description = "卸货备注")
    private String note;


}
