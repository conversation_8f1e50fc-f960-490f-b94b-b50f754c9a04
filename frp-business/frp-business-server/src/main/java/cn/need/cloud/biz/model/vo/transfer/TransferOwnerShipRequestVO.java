package cn.need.cloud.biz.model.vo.transfer;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 货权转移 VO对象
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "货权转移 VO对象")
public class TransferOwnerShipRequestVO extends BaseSuperVO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 源PartnerId
     */
    @Schema(description = "源PartnerId")
    private Long fromPartnerId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * RefNumType
     */
    @Schema(description = "RefNumType")
    private String refNumType;

    /**
     * RequestRefNum
     */
    @Schema(description = "RequestRefNum")
    private String requestRefNum;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String requestStatus;

    /**
     * 目标PartnerId
     */
    @Schema(description = "目标PartnerId")
    private Long toPartnerId;

    /**
     * 转换类型
     */
    @Schema(description = "转换类型")
    private String transferOwnerShipType;
}