package cn.need.cloud.biz.model.param.otb.update.putawayslip;

import cn.need.cloud.biz.model.param.base.update.PutawaySlipPutAwayUpdateParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * OTC上架单取消对象
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTB上架单上架对象")
public class OtbPutawaySlipPutAwayUpdateParam extends PutawaySlipPutAwayUpdateParam<OtbPutawaySlipPutAwayDetailUpdateParam> {

}
