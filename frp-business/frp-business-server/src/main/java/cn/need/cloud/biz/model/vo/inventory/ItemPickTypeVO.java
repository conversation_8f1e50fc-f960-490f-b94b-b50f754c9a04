package cn.need.cloud.biz.model.vo.inventory;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 更新库存vo对象
 */
@Data
@AllArgsConstructor
public class ItemPickTypeVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    private String ItemNum;

    private String PoNum;

    private Integer PickQty;

    private String LocationID;
}
