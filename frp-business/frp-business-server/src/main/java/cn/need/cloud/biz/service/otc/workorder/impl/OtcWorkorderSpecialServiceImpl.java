package cn.need.cloud.biz.service.otc.workorder.impl;

import cn.need.cloud.biz.client.constant.ErrorConstant;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.ProcessType;
import cn.need.cloud.biz.client.constant.enums.inventory.InventoryLockedStatusEnum;
import cn.need.cloud.biz.client.constant.enums.inventory.WorkOrderPrepStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPackageStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcWorkorderStatusEnum;
import cn.need.cloud.biz.client.constant.special.RollbackConstant;
import cn.need.cloud.biz.model.bo.base.AllocationBO;
import cn.need.cloud.biz.model.bo.base.ChangeQtyLogBO;
import cn.need.cloud.biz.model.bo.base.WorkorderProcessBO;
import cn.need.cloud.biz.model.bo.otc.OtcPackageRollbackSingleWorkorderBO;
import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPutawaySlipPutAwayDetailBO;
import cn.need.cloud.biz.model.bo.otc.workorder.OtcWorkorderSplitBO;
import cn.need.cloud.biz.model.bo.otc.workorder.OtcWorkorderSplitDetailBO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.entity.inventory.InventoryLocked;
import cn.need.cloud.biz.model.entity.otc.*;
import cn.need.cloud.biz.model.param.base.WorkorderFinishUpdateParam;
import cn.need.cloud.biz.model.param.base.WorkorderStartUpdateParam;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReleaseLockedParam;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestCancelContext;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestCancelParam;
import cn.need.cloud.biz.model.query.base.SplitWorkorderDetailParam;
import cn.need.cloud.biz.model.query.base.SplitWorkorderParam;
import cn.need.cloud.biz.model.query.base.WorkorderRollbackListQuery;
import cn.need.cloud.biz.model.vo.base.BaseProductVersionVO;
import cn.need.cloud.biz.model.vo.base.auditlog.BaseProductLogVO;
import cn.need.cloud.biz.model.vo.base.pickingslip.BasePickingSlipUnpickDetailVO;
import cn.need.cloud.biz.model.vo.base.pickingslip.PickingSlipUnpickDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderFinishConfirmVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkorderConfirmDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkorderConfirmVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkorderFinishConfirmVO;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailLockedService;
import cn.need.cloud.biz.service.helper.LockedHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcWorkOrderAuditLogHelper;
import cn.need.cloud.biz.service.helper.workorder.OtcWorkorderHelper;
import cn.need.cloud.biz.service.helper.workorder.WorkorderHelper;
import cn.need.cloud.biz.service.inventory.InventoryLockedService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipSpecialService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageSpecialService;
import cn.need.cloud.biz.service.otc.putawayslip.OtcPutawaySlipDetailService;
import cn.need.cloud.biz.service.otc.putawayslip.OtcPutawaySlipService;
import cn.need.cloud.biz.service.otc.request.OtcRequestSpecialService;
import cn.need.cloud.biz.service.otc.workorder.*;
import cn.need.cloud.biz.util.*;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.model.IdModel;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * OTC工单 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@AllArgsConstructor
public class OtcWorkorderSpecialServiceImpl implements OtcWorkorderSpecialService {

    /**
     * OTC工单特殊业务实现类
     * <p>
     * 负责OTC工单的回滚、取消、拆单、拣货、包裹等特殊流程处理，
     * 涉及工单、工单明细、拣货单、包裹、上架单、锁定等多业务实体的联动。
     * 该类为核心业务服务，流程复杂，需重点关注事务一致性与状态流转。
     * </p>
     * <p>
     * 主要功能：
     * 1. 工单回滚/取消/拆单/批量操作
     * 2. 工单与拣货单、包裹、上架单等的联动状态变更
     * 3. 工单明细、锁定、上架锁等实体的同步处理
     * 4. 业务流程的触发与日志记录
     * <p>
     * 注意：所有操作均需保证数据一致性，异常需回滚事务。
     */

    // ======================================== 常量定义 ========================================

    private static final String OPERATION_FAILED_UPDATE_WORKORDER = "update WorkOrder status failed";
    private static final String OPERATION_FAILED_UPDATE_WORKORDER_DETAIL = "update WorkOrderDetail status failed";
    private static final String OPERATION_FAILED_UPDATE_INVENTORY_LOCKED = "update InventoryLocked failed";
    private static final String OPERATION_FAILED_UPDATE_BIN_LOCATION_LOCKED = "update BinLocationLocked qty failed";
    private final OtcWorkorderService otcWorkorderService;
    private final OtcWorkorderDetailService otcWorkorderDetailService;
    private final OtcPrepWorkorderSpecialService otcPrepWorkorderSpecialService;
    private final OtcWorkorderBinLocationService otcWorkorderBinLocationService;
    private final OtcPickingSlipSpecialService otcPickingSlipSpecialService;
    private final OtcPackageSpecialService otcPackageSpecialService;
    private final OtcPutawaySlipService otcPutawaySlipService;
    private final OtcPutawaySlipDetailService otcPutawaySlipDetailService;
    private final OtcRequestSpecialService otcRequestSpecialService;
    private final InventoryLockedService inventoryLockedService;
    private final BinLocationDetailLockedService binLocationDetailLockedService;

    // ======================================== 工单校验方法 ========================================

    /**
     * 校验工单是否可以启动指定流程
     *
     * @param process       流程参数
     * @param workorderList 工单列表
     * @return 校验通过的工单列表
     */
    @NotNull
    private static List<OtcWorkorder> checkStartWorkorder(WorkorderProcessBO process, List<OtcWorkorder> workorderList) {
        Validate.notEmpty(workorderList, "idList: {} WorkOrder is empty", process.getIdList());

        String type = process.getProcessType().getType();
        workorderList.forEach(workorder -> validateWorkorderForStart(workorder, type));
        return workorderList;
    }

    /**
     * 校验单个工单是否可以启动流程
     *
     * @param workorder 工单
     * @param type      流程类型
     */
    private static void validateWorkorderForStart(OtcWorkorder workorder, String type) {
        Validate.isTrue(OtcWorkorderStatusEnum.canStartStatuses().contains(workorder.getOtcWorkorderStatus()),
                ErrorConstant.STATUS_ERROR_FORMAT,
                workorder.refNumLog(), "start" + type, OtcWorkorderStatusEnum.canStartStatuses(), workorder.getOtcWorkorderStatus()
        );

        ProcessType.checkNormalAvailability(workorder.getProcessType(), workorder.refNumLog(), "start" + type);
    }

    // ======================================== 工单拆分方法 ========================================

    /**
     * 执行工单拆分逻辑
     *
     * @param splitQueryList 拆分参数列表
     * @param workorderMap   工单映射
     * @param detailMap      工单明细映射
     * @return 拆分结果列表
     */
    @NotNull
    private static List<OtcWorkorderSplitBO> split(List<SplitWorkorderParam> splitQueryList,
                                                   Map<Long, OtcWorkorder> workorderMap,
                                                   Map<Long, OtcWorkorderDetail> detailMap) {
        return splitQueryList.stream()
                .map(splitParam -> createSplitWorkorder(splitParam, workorderMap, detailMap))
                .toList();
    }

    /**
     * 创建单个拆分工单
     *
     * @param splitParam   拆分参数
     * @param workorderMap 工单映射
     * @param detailMap    工单明细映射
     * @return 拆分工单对象
     */
    private static OtcWorkorderSplitBO createSplitWorkorder(SplitWorkorderParam splitParam,
                                                            Map<Long, OtcWorkorder> workorderMap,
                                                            Map<Long, OtcWorkorderDetail> detailMap) {
        var workorder = workorderMap.get(splitParam.getId());
        var splitWorkorder = createNewSplitWorkorder(workorder);
        var splitDetails = createSplitDetails(splitParam, detailMap, splitWorkorder.getId());

        var splitHolder = new OtcWorkorderSplitBO();
        splitHolder.setWorkorder(workorder);
        splitHolder.setSplitWorkorder(splitWorkorder);
        splitHolder.setDetailHolders(splitDetails);
        return splitHolder;
    }

    /**
     * 创建新的拆分工单
     *
     * @param originalWorkorder 原工单
     * @return 新的拆分工单
     */
    private static OtcWorkorder createNewSplitWorkorder(OtcWorkorder originalWorkorder) {
        var splitWorkorder = BeanUtil.copyNew(originalWorkorder, OtcWorkorder.class);
        splitWorkorder.setId(IdWorker.getId());
        splitWorkorder.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTC_WORK_ORDER.getCode()));
        splitWorkorder.setOtcPickingSlipId(null);
        return splitWorkorder;
    }

    /**
     * 创建拆分工单明细
     *
     * @param splitParam       拆分参数
     * @param detailMap        工单明细映射
     * @param splitWorkorderId 拆分工单ID
     * @return 拆分明细列表
     */
    private static List<OtcWorkorderSplitDetailBO> createSplitDetails(SplitWorkorderParam splitParam,
                                                                      Map<Long, OtcWorkorderDetail> detailMap,
                                                                      Long splitWorkorderId) {
        return splitParam.getDetailList().stream()
                .map(detailParam -> createSplitDetail(detailParam, detailMap, splitWorkorderId))
                .toList();
    }

    /**
     * 创建单个拆分明细
     *
     * @param detailParam      明细参数
     * @param detailMap        工单明细映射
     * @param splitWorkorderId 拆分工单ID
     * @return 拆分明细对象
     */
    private static OtcWorkorderSplitDetailBO createSplitDetail(SplitWorkorderDetailParam detailParam,
                                                               Map<Long, OtcWorkorderDetail> detailMap,
                                                               Long splitWorkorderId) {
        var originalDetail = detailMap.get(detailParam.getId());
        // 减去拆单数量
        originalDetail.setQty(originalDetail.getQty() - detailParam.getSplitQty());

        // 创建拆单详情
        var splitDetail = BeanUtil.copyNew(originalDetail, OtcWorkorderDetail.class);
        splitDetail.setId(IdWorker.getId());
        splitDetail.setOtcWorkorderId(splitWorkorderId);
        initializeSplitDetailQuantities(splitDetail, detailParam.getSplitQty());

        var splitDetailHolder = new OtcWorkorderSplitDetailBO();
        splitDetailHolder.setSplitDetail(splitDetail);
        splitDetailHolder.setDetail(originalDetail);
        splitDetailHolder.setSplitQty(detailParam.getSplitQty());
        return splitDetailHolder;
    }

    /**
     * 初始化拆分明细的数量字段
     *
     * @param splitDetail 拆分明细
     * @param splitQty    拆分数量
     */
    private static void initializeSplitDetailQuantities(OtcWorkorderDetail splitDetail, Integer splitQty) {
        splitDetail.setQty(splitQty);
        splitDetail.setFinishQty(0);
        splitDetail.setPackedQty(0);
        splitDetail.setPickedQty(0);
        splitDetail.setReadyToShipQty(0);
        splitDetail.setFinishReserveQty(0);
        splitDetail.setReserveQty(0);
    }

    // ======================================== 拆单校验方法 ========================================

    /**
     * 检查拆单条件
     *
     * @param splitQueryList 拆单条件
     * @param detailMap      工单详情
     * @param workorderMap   工单
     */
    private static void checkSplitParam(List<SplitWorkorderParam> splitQueryList,
                                        Map<Long, OtcWorkorderDetail> detailMap,
                                        Map<Long, OtcWorkorder> workorderMap) {
        Map<Long, Integer> splitQtyMap = calculateTotalSplitQuantities(splitQueryList);
        splitQtyMap.forEach((detailId, totalSplitQty) ->
                validateSplitQuantity(detailId, totalSplitQty, detailMap, workorderMap));
    }

    /**
     * 计算每个明细的总拆分数量
     *
     * @param splitQueryList 拆分参数列表
     * @return 明细ID到总拆分数量的映射
     */
    private static Map<Long, Integer> calculateTotalSplitQuantities(List<SplitWorkorderParam> splitQueryList) {
        return splitQueryList.stream()
                .map(SplitWorkorderParam::getDetailList)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(
                        SplitWorkorderDetailParam::getId,
                        Collectors.summingInt(SplitWorkorderDetailParam::getSplitQty)
                ));
    }

    /**
     * 校验单个明细的拆分数量
     *
     * @param detailId     明细ID
     * @param splitQty     拆分数量
     * @param detailMap    明细映射
     * @param workorderMap 工单映射
     */
    private static void validateSplitQuantity(Long detailId, Integer splitQty,
                                              Map<Long, OtcWorkorderDetail> detailMap,
                                              Map<Long, OtcWorkorder> workorderMap) {
        var detail = detailMap.get(detailId);
        Validate.notNull(detail, "Detail id {} is not exist", detailId);

        var productLog = getProductLogString(detail.getProductId());
        var workorderLog = workorderMap.get(detail.getOtcWorkorderId()).refNumLog();

        // 校验是否可以拆单（有拣货且有未拣货的才能拆单）
        Validate.isTrue(detail.getQty() > detail.getPickedQty(),
                "{} Detail {} cannot be split", workorderLog, productLog);

        // 校验拆单数量是否足够
        var availableSplitQty = detail.getQty() - detail.getPickedQty();
        Validate.isTrue(splitQty <= availableSplitQty,
                "{} Detail {} not have enough units to split the WorkOrder, [AvailableSplitQty: {} < SplitQty: {}]",
                workorderLog, productLog, availableSplitQty, splitQty);
    }

    /**
     * 获取产品日志字符串
     *
     * @param productId 产品ID
     * @return 产品日志字符串
     */
    private static String getProductLogString(Long productId) {
        return Optional.ofNullable(productId)
                .map(ProductCacheUtil::getById)
                .map(obj -> BeanUtil.copyNew(obj, BaseProductLogVO.class))
                .map(BaseProductLogVO::toLog)
                .orElse(StringPool.EMPTY);
    }

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startRollback(WorkorderStartUpdateParam query) {
        WorkorderProcessBO process = BeanUtil.copyNew(query, WorkorderProcessBO.class);
        process.setProcessType(ProcessType.ROLLBACKING);
        this.processTriggering(process, this.getAndCheckStartWorkorder(process));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean finishRollback(WorkorderFinishUpdateParam query) {
        WorkorderProcessBO process = BeanUtil.copyNew(query, WorkorderProcessBO.class);
        process.setProcessType(ProcessType.NORMAL);
        this.processTriggering(process, this.getAndCheckFinishWorkorder(process));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startCancel(WorkorderStartUpdateParam query) {
        WorkorderProcessBO process = BeanUtil.copyNew(query, WorkorderProcessBO.class);
        process.setProcessType(ProcessType.CANCELLING);
        this.processTriggering(process, this.getAndCheckStartCancelWorkorder(process));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean finishCancel(WorkorderFinishUpdateParam query) {
        WorkorderProcessBO process = BeanUtil.copyNew(query, WorkorderProcessBO.class);
        process.setProcessType(ProcessType.NORMAL);

        // 获取工单并校验
        List<OtcWorkorder> workorderList = this.getAndCheckFinishCancelWorkorder(process);
        Map<Long, OtcWorkorder> workorderMap = StreamUtils.toMap(workorderList, IdModel::getId);

        // 校验未拣货项目
        validateUnpickedItems(query.getIdList(), workorderMap);

        // 释放库存锁定
        releaseInventoryLocked(query.getIdList());

        // 更新工单状态
        updateWorkorderCancelStatus(query.getIdList(), workorderMap);

        // 触发相关流程
        triggerRelatedProcesses(process, workorderList);

        return true;
    }

    /**
     * 校验未拣货项目
     *
     * @param workorderIds 工单ID列表
     * @param workorderMap 工单映射
     */
    private void validateUnpickedItems(List<Long> workorderIds, Map<Long, OtcWorkorder> workorderMap) {
        List<PickingSlipUnpickDetailVO> unpickList = this.unpickList(workorderIds);
        unpickList.forEach(unpick -> {
            if (unpick.getCanRollbackQty() > 0) {
                String productInfo = getProductVersionInfo(unpick.getProductVersionId());
                String workorderLog = workorderMap.get(unpick.getWorkorderId()).refNumLog();

                Validate.isTrue(false,
                        "{} There are still remaining [{}, CanRollbackQty: {}] unpicked items",
                        workorderLog, productInfo, unpick.getCanRollbackQty());
            }
        });
    }

    /**
     * 获取产品版本信息
     *
     * @param productVersionId 产品版本ID
     * @return 产品版本信息字符串
     */
    private String getProductVersionInfo(Long productVersionId) {
        return Optional.ofNullable(productVersionId)
                .map(ProductVersionCacheUtil::getById)
                .map(pro -> BeanUtil.copyNew(pro, BaseProductVersionVO.class))
                .map(BaseProductVersionVO::toString)
                .orElse(StringPool.EMPTY);
    }

    /**
     * 释放库存锁定
     *
     * @param workorderIds 工单ID列表
     */
    private void releaseInventoryLocked(List<Long> workorderIds) {
        List<OtcWorkorderDetail> allDetails = otcWorkorderDetailService.listByWorkOrderIds(workorderIds);
        List<InventoryReleaseLockedParam> releaseParams = buildReleaseLockedParams(allDetails);

        if (!releaseParams.isEmpty()) {
            inventoryLockedService.releaseLockedInventory(releaseParams);
        }
    }

    /**
     * 构建释放锁定参数
     *
     * @param details 工单明细列表
     * @return 释放锁定参数列表
     */
    private List<InventoryReleaseLockedParam> buildReleaseLockedParams(List<OtcWorkorderDetail> details) {
        return details.stream()
                .filter(detail -> detail.getInventoryLockedId() != null && detail.getQty() != null && detail.getQty() > 0)
                .collect(Collectors.groupingBy(
                        OtcWorkorderDetail::getInventoryLockedId,
                        Collectors.summingInt(OtcWorkorderDetail::getQty)))
                .entrySet().stream()
                .map(entry -> {
                    InventoryReleaseLockedParam param = new InventoryReleaseLockedParam();
                    param.setId(entry.getKey());
                    param.setQty(entry.getValue());
                    return param;
                })
                .collect(Collectors.toList());
    }

    /**
     * 更新工单取消状态
     *
     * @param workorderIds 工单ID列表
     * @param workorderMap 工单映射
     */
    private void updateWorkorderCancelStatus(List<Long> workorderIds, Map<Long, OtcWorkorder> workorderMap) {
        Map<Long, List<OtcWorkorderDetail>> detailsMap = otcWorkorderDetailService.groupByOtcWorkOrderIdList(workorderIds);

        detailsMap.forEach((workorderId, details) -> {
            OtcWorkorder workorder = workorderMap.get(workorderId);
            boolean allCancel = details.stream().allMatch(detail -> detail.getPickedQty() == 0);

            workorder.setOtcWorkorderStatus(allCancel
                    ? OtcWorkorderStatusEnum.CANCELLED.getStatus()
                    : OtcWorkorderStatusEnum.PART_CANCELLED.getStatus());
        });
    }

    /**
     * 触发相关流程
     *
     * @param process       流程参数
     * @param workorderList 工单列表
     */
    private void triggerRelatedProcesses(WorkorderProcessBO process, List<OtcWorkorder> workorderList) {
        // 触发工单流程
        this.processTriggering(process, workorderList);

        // 包裹完成取消
        List<Long> workorderIds = StreamUtils.distinctMap(workorderList, IdModel::getId);
        otcPackageSpecialService.finishCancel(workorderIds);

        // 请求取消
        List<Long> requestIdList = StreamUtils.distinctMap(workorderList, OtcWorkorder::getOtcRequestId);
        otcRequestSpecialService.finishCancel(requestIdList);
    }

    @Override
    public void rollbackByPackage(List<OtcPackageRollbackSingleWorkorderBO> rollbackList) {
        // 执行回滚操作
        rollbackList.forEach(this::processPackageRollback);

        // 批量更新工单明细和工单
        updateRollbackEntities(rollbackList);
    }

    /**
     * 处理单个包裹回滚
     *
     * @param rollback 回滚参数
     */
    private void processPackageRollback(OtcPackageRollbackSingleWorkorderBO rollback) {
        var workorder = rollback.getWorkorder();

        // 校验是否启用异常流程
        ProcessType.checkAbnormal(workorder.getProcessType(), workorder.refNumLog(), "rollbackReadyToShipQty");

        // 执行回滚操作
        this.rollbackPackedQty(rollback);
        this.rollbackReadyToShipQty(rollback);
    }

    /**
     * 更新回滚相关实体
     *
     * @param rollbackList 回滚列表
     */
    private void updateRollbackEntities(List<OtcPackageRollbackSingleWorkorderBO> rollbackList) {
        // 更新工单明细
        List<OtcWorkorderDetail> workorderDetailList = rollbackList.stream()
                .flatMap(rollback -> rollback.getWorkorderDetailList().stream())
                .toList();
        Validate.isTrue(otcWorkorderDetailService.updateBatch(workorderDetailList) == workorderDetailList.size(),
                String.format(ErrorMessages.OPERATION_FAILED, "update WorkorderDetail rollback"));

        // 更新工单
        List<OtcWorkorder> workorderList = StreamUtils.distinctMap(rollbackList, OtcPackageRollbackSingleWorkorderBO::getWorkorder);
        Validate.isTrue(otcWorkorderService.updateBatch(workorderList) == workorderList.size(),
                String.format(ErrorMessages.OPERATION_FAILED, "update Workorder rollback"));
    }

    @Override
    public void cancelWithPickingSlip(List<Long> pickingSlipIdList) {
        // 获取拣货单下的工单
        List<OtcWorkorder> workorderList = otcWorkorderService.listByPickingSlipIds(pickingSlipIdList);

        // 更新工单状态为BEGIN
        updateWorkorderStatusToBegin(workorderList);

        // 回滚库存锁定状态
        rollbackInventoryLocked(workorderList);
    }

    /**
     * 更新工单状态为BEGIN
     *
     * @param workorderList 工单列表
     */
    private void updateWorkorderStatusToBegin(List<OtcWorkorder> workorderList) {
        workorderList.forEach(workorder -> workorder.setOtcWorkorderStatus(OtcWorkorderStatusEnum.BEGIN.getStatus()));

        Validate.isTrue(otcWorkorderService.updateBatch(workorderList) == workorderList.size(),
                String.format(ErrorMessages.OPERATION_FAILED, "update WorkOrder status"));

        OtcWorkOrderAuditLogHelper.recordLog(workorderList);
    }

    /**
     * 回滚库存锁定状态
     *
     * @param workorderList 工单列表
     */
    private void rollbackInventoryLocked(List<OtcWorkorder> workorderList) {
        List<Long> workorderIds = StreamUtils.distinctMap(workorderList, IdModel::getId);
        List<OtcWorkorderDetail> details = otcWorkorderDetailService.listByWorkOrderIds(workorderIds);
        List<Long> inventoryLockedIds = StreamUtils.distinctMap(details, OtcWorkorderDetail::getInventoryLockedId);

        List<InventoryLocked> inventoryLockedList = inventoryLockedService.listByIds(inventoryLockedIds);
        inventoryLockedList.forEach(locked -> {
            locked.setLockedStatus(InventoryLockedStatusEnum.LOCKED.getStatus());
            locked.setFinishQty(0);
        });

        Validate.isTrue(inventoryLockedService.updateBatch(inventoryLockedList) == inventoryLockedList.size(),
                OPERATION_FAILED_UPDATE_INVENTORY_LOCKED);
    }

    @Override
    public List<WorkorderConfirmDetailVO> confirmDetailList(WorkorderRollbackListQuery query) {
        Map<Long, OtcWorkorder> wkMap = StreamUtils.toMap(otcWorkorderService.listByIds(query.getIdList()), IdModel::getId);
        Map<Long, List<OtcWorkorderDetail>> detailsMap = otcWorkorderDetailService.groupByOtcWorkOrderIdList(query.getIdList());

        return detailsMap.entrySet().stream().flatMap(entry -> {
            OtcWorkorder workorder = wkMap.get(entry.getKey());
            return entry.getValue().stream().map(detail -> {
                WorkorderConfirmDetailVO rollback = BeanUtil.copyNew(detail, WorkorderConfirmDetailVO.class);
                WorkorderConfirmVO confirm = BeanUtil.copyNew(workorder, WorkorderConfirmVO.class);
                confirm.setWorkorderStatus(workorder.getOtcWorkorderStatus());
                rollback.setWorkorder(confirm);
                return rollback;
            });
        }).toList();
    }

    @Override
    public List<PickingSlipUnpickDetailVO> unpickList(List<Long> workorderIds) {
        // 处理已经创建上架单的不让他继续创建
        List<OtcPutawaySlipDetail> details = otcPutawaySlipDetailService.listAvailableByWorkorderIds(workorderIds);
        Map<Long, Integer> hasCreateQtyMap = details.stream().collect(Collectors.groupingBy(OtcPutawaySlipDetail::getWorkorderBinLocationId, Collectors.summingInt(OtcPutawaySlipDetail::getQty)));

        List<OtcWorkorderBinLocation> currenWkPickInfoList = otcWorkorderBinLocationService.listByOtcWorkorderIdList(workorderIds);
        // 扣除上架单占用数量
        currenWkPickInfoList.forEach(obj -> obj.setQty(obj.getQty() - hasCreateQtyMap.getOrDefault(obj.getId(), 0)));

        // 赋值返回参数
        List<PickingSlipUnpickDetailVO> unpickDetails = currenWkPickInfoList.stream().map(obj -> {
            PickingSlipUnpickDetailVO unpick = BeanUtil.copyNew(obj, PickingSlipUnpickDetailVO.class);
            unpick.setWorkorderId(obj.getOtcWorkorderId());
            unpick.setWorkorderDetailId(obj.getOtcWorkorderDetailId());
            unpick.setWorkorderBinLocationId(obj.getId());
            unpick.setPickingSlipId(obj.getOtcPickingSlipId());
            unpick.setPickingSlipDetailId(obj.getOtcPickingSlipDetailId());
            return unpick;
        }).toList();

        // 拣货数量即 是Unpick时 Rollback时的 qty
        unpickDetails.stream().sorted(Comparator.comparing(BasePickingSlipUnpickDetailVO::getUnpickId)).forEach(detail -> detail.setPickedQty(detail.getQty()));

        // 获取工单详情
        List<Long> wkDetailIds = StreamUtils.distinctMap(currenWkPickInfoList, OtcWorkorderBinLocation::getOtcWorkorderDetailId);
        List<OtcWorkorderDetail> wkDetails = otcWorkorderDetailService.listByIds(wkDetailIds);
        Map<Long, OtcWorkorderDetail> wkDetailMap = StreamUtils.toMap(wkDetails, OtcWorkorderDetail::getId);

        Map<Long, Integer> readyToShipQtyMap = wkDetailMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, obj -> obj.getValue().getReadyToShipQty()));

        // 赋值readyToShipQty、packedQty
        for (PickingSlipUnpickDetailVO unpickDetail : unpickDetails) {
            Integer remaining = readyToShipQtyMap.get(unpickDetail.getWorkorderDetailId());
            if (remaining <= 0) {
                continue;
            }
            int readyToShipQty = Math.min(remaining, unpickDetail.getPickedQty());
            unpickDetail.setReadyToShipQty(readyToShipQty);
            readyToShipQtyMap.put(unpickDetail.getWorkorderDetailId(), remaining - readyToShipQty);
        }

        return unpickDetails;
    }

    @Override
    public void rollback(OtcPutawaySlipPutAwayBO putawayParam) {
        OtcPutawaySlip putawaySlip = putawayParam.getPutawaySlip();

        List<OtcWorkorderDetail> wkDetails = otcWorkorderDetailService.listByWorkOrderId(putawaySlip.getWorkorderId());
        OtcWorkorder workorder = otcWorkorderService.getById(putawaySlip.getWorkorderId());

        Map<Long, OtcWorkorderDetail> detailMap = StreamUtils.toMap(wkDetails, IdModel::getId);

        // Rollback
        List<ChangeQtyLogBO> changeList = putawayParam.getDetailList().stream().map(paramDetail -> {
            OtcPutawaySlipDetail putawaySlipDetail = paramDetail.getPutawaySlipDetail();
            OtcWorkorderDetail wkDetail = detailMap.get(putawaySlipDetail.getWorkorderDetailId());
            wkDetail.setPickedQty(wkDetail.getPickedQty() - paramDetail.getPutawayQty());

            // 变更数量
            ChangeQtyLogBO change = new ChangeQtyLogBO();
            change.setBeforeQty(wkDetail.getPickedQty() + paramDetail.getPutawayQty());
            change.setAfterQty(wkDetail.getPickedQty());
            change.setProductId(wkDetail.getProductId());

            paramDetail.setWorkorderDetail(wkDetail);
            return change;
        }).toList();

        // 状态变更 Picked/ReadyToShip -> IN_PICKING
        String oldStatus = workorder.getOtcWorkorderStatus();
        workorder.setOtcWorkorderStatus(OtcWorkorderStatusEnum.IN_PICKING.getStatus());


        // rollback工单详情
        List<OtcWorkorderDetail> rollbackDetails = putawayParam.getDetailList()
                .stream()
                .map(OtcPutawaySlipPutAwayDetailBO::getWorkorderDetail)
                .toList();

        Validate.isTrue(otcWorkorderDetailService.updateBatch(rollbackDetails) == rollbackDetails.size(),
                String.format(ErrorMessages.OPERATION_FAILED, "update WorkOrderDetail pickedQty"));
        // 记录日志
        OtcWorkOrderAuditLogHelper.recordLog(workorder, "Rollback PickedQty",
                JsonUtil.toJson(changeList), putawayParam.getNote(), BaseTypeLogEnum.OPERATION.getType()
        );
        if (!Objects.equals(oldStatus, workorder.getOtcWorkorderStatus())) {
            Validate.isTrue(otcWorkorderService.update(workorder) == 1,
                    String.format(ErrorMessages.OPERATION_FAILED, "update WorkOrder status")
            );
            OtcWorkOrderAuditLogHelper.recordLog(workorder, null, putawayParam.getNote());
        }

        // 包裹 Rollback
        otcPackageSpecialService.rollback(workorder, putawayParam);

    }

    @Override
    public WorkorderFinishConfirmVO finishConfirm(WorkorderRollbackListQuery query) {
        // Prep完成确认数据
        PrepWorkorderFinishConfirmVO prepFinishConfirm = otcPrepWorkorderSpecialService.finishRollbackConfirm(query);

        WorkorderFinishConfirmVO finishConfirm = new WorkorderFinishConfirmVO();
        finishConfirm.setWorkorderList(this.confirmDetailList(query));
        finishConfirm.setPutawaySlipList(otcPutawaySlipService.confirmDetailList(query));
        finishConfirm.setPrepWorkorderList(prepFinishConfirm.getPrepWorkorderList());
        finishConfirm.setPrepPutawaySlipList(prepFinishConfirm.getPrepPutawaySlipList());
        return finishConfirm;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean split(List<SplitWorkorderParam> splitParamList) {
        splitWithReturn(splitParamList);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public List<OtcWorkorder> splitWithReturn(List<SplitWorkorderParam> splitParamList) {
        splitParamList = WorkorderHelper.mergeSplitQuery(splitParamList);

        Validate.notEmpty(splitParamList, "splitParamList is must not empty");

        // 拆单逻辑
        var workorderIds = StreamUtils.distinctMap(splitParamList, SplitWorkorderParam::getId);
        var workorderList = otcWorkorderService.listByIds(workorderIds);
        Validate.notEmpty(workorderList, "WorkOrderId: {} is not exist", workorderIds);

        workorderList.forEach(workorder -> {
            // 校验流程是否正确
            ProcessType.checkAbnormal(workorder.getProcessType(), workorder.refNumLog(), "split");
        });

        var details = otcWorkorderDetailService.listByWorkOrderIds(workorderIds);
        var detailMap = StreamUtils.toMap(details, IdModel::getId);
        var workorderMap = StreamUtils.toMap(workorderList, IdModel::getId);

        // 需要拆单的详情
        checkSplitParam(splitParamList, detailMap, workorderMap);

        // Step: 工单 拆单
        var splitHolders = split(splitParamList, workorderMap, detailMap);

        // Workorder & Split Workorder: Update Status
        OtcWorkorderHelper.refreshStatus(workorderList, details);
        OtcWorkorderHelper.refreshStatus(splitHolders.stream()
                        .map(OtcWorkorderSplitBO::getSplitWorkorder)
                        .toList(),
                splitHolders.stream()
                        .flatMap(obj -> obj.getDetailHolders().stream()
                                .map(OtcWorkorderSplitDetailBO::getSplitDetail))
                        .toList()
        );

        // Step: 拣货单：拆单
        otcPickingSlipSpecialService.split(splitHolders);

        // Step: 包裹 拆单
        otcPackageSpecialService.split(splitHolders);

        // Step: Prep工单：拆单
        otcPrepWorkorderSpecialService.split(splitHolders);

        // Step: 锁拆单
        this.lockSplit(splitHolders);

        // 更新工单状态
        Validate.isTrue(otcWorkorderService.updateBatch(workorderList) == workorderList.size(),
                "Update WorkOrder status failed"
        );

        Validate.isTrue(otcWorkorderDetailService.updateBatch(details) == details.size(),
                "Update WorkOrderDetail status failed"
        );

        // 拆单入库
        var splitWorkorderList = StreamUtils.distinctMap(splitHolders, OtcWorkorderSplitBO::getSplitWorkorder);
        otcWorkorderService.insertBatch(splitWorkorderList);

        var splitDetails = splitHolders.stream()
                .flatMap(obj -> obj.getDetailHolders().stream().map(OtcWorkorderSplitDetailBO::getSplitDetail))
                .toList();

        otcWorkorderDetailService.insertBatch(splitDetails);

        return splitWorkorderList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<OtcWorkorder> allCancel(OtcRequestCancelParam param) {

        var workorderList = otcWorkorderService.listByRequestId(param.getId());
        //没有Workorder,外界可以直接 Cancelled 了
        if (ObjectUtil.isEmpty(workorderList)) {
            return Collections.emptyList();
        }

        for (OtcWorkorder otcWorkorder : workorderList) {
            if (!OtcWorkorderStatusEnum.canCancelStatuses().contains(otcWorkorder.getOtcWorkorderStatus())) {
                throw new BusinessException(StringUtil.format("{} current status is {} , can not support cancel", otcWorkorder.refNumLog(), otcWorkorder.getOtcWorkorderStatus()));
            }
        }

        // 开起Cancel
        WorkorderStartUpdateParam query = new WorkorderStartUpdateParam();
        query.setRequestIdList(Collections.singletonList(param.getId()));
        query.setWorkorderList(workorderList);
        query.setRequest(param.getRequest());

        this.startCancel(query);

        var workorderDetails = otcWorkorderDetailService.listByWorkOrderIds(StreamUtils.distinctMap(workorderList, IdModel::getId));

        //todo:// 这里是由于Split方案还有问题，拆单功能延后实现
        return workorderList;

        //todo: 下面的代码待参考

        // // 全部取消
        // var splitWkQueryList = workorderDetails.stream()
        //         .collect(Collectors.groupingBy(OtcWorkorderDetail::getOtcWorkorderId))
        //         .entrySet()
        //         .stream()
        //         .map(entry -> new SplitWorkorderParam(entry.getKey(), entry.getValue().stream()
        //                 // 只拆有拣货的并且还有未拣货的
        //                 .filter(obj -> obj.getPickedQty() > 0 && obj.getQty() > obj.getPickedQty())
        //                 .map(obj -> {
        //                     var splitWorkorderDetailQuery = new SplitWorkorderDetailParam();
        //                     splitWorkorderDetailQuery.setId(obj.getId());
        //                     splitWorkorderDetailQuery.setSplitQty(obj.getQty() - obj.getPickedQty());
        //                     return splitWorkorderDetailQuery;
        //                 })
        //                 .toList()))
        //         .filter(obj -> ObjectUtil.isNotEmpty(obj.getDetailList()))
        //         .toList();
        //
        // if (ObjectUtil.isEmpty(splitWkQueryList)) {
        //     return workorderList;
        // }
        //
        // // 拆单
        // List<OtcWorkorder> splitWorkOrderList = this.splitWithReturn(splitWkQueryList);
        //
        // //这里要返回 拆单后的工单 + 原本工单
        // List<OtcWorkorder> result = new ArrayList<>(workorderList);
        // result.addAll(splitWorkOrderList);
        // return result;
    }

    /**
     * @param context
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startCancel(OtcRequestCancelContext context) {

        var workorderList = otcWorkorderService.listByRequestId(context.getRequest().getId());

        if (ObjectUtil.isEmpty(workorderList)) {
            return;
        }

        context.setWorkorderList(workorderList);

        // 设置流程状态
        for (var obj : workorderList) {
            if (ProcessType.canCancelling(obj.getProcessType())) {
                obj.setProcessType(ProcessType.CANCELLING.getType());
            } else {
                throw new BusinessException(StringUtil.format(" {}, ProcessType is {}, Can not do {}", obj.refNumLog(), obj.getProcessType(), ProcessType.CANCELLING.getType()));
            }
        }

        // 记录日志
        OtcWorkOrderAuditLogHelper.recordLog(workorderList, "Start Cancel", null, context.getNote(), BaseTypeLogEnum.PROCESS_TYPE.getType());

        // 拣货单
        otcPickingSlipSpecialService.startCancel(context);

        // 包裹
        otcPackageSpecialService.startCancel(context);

        // prepWorkorder
        otcPrepWorkorderSpecialService.startCancel(context);

        Validate.isTrue(otcWorkorderService.updateBatch(workorderList) == workorderList.size(), " [{}] Update WorkOrder failed", "cancelByRequest");
    }


    /**
     * 锁 拆单
     *
     * @param splitHolders 拆单信息
     */
    private void lockSplit(List<OtcWorkorderSplitBO> splitHolders) {
        // 工单锁
        this.inventoryLockedSplit(splitHolders);

        // 上架锁
        this.putawayLockedSplit(splitHolders);
    }

    /**
     * 工单锁拆分
     *
     * @param splitHolders splitHolders
     */
    private void inventoryLockedSplit(List<OtcWorkorderSplitBO> splitHolders) {
        // 原单 释放锁
        var lockedIds = splitHolders.stream()
                .flatMap(obj -> obj.getDetailHolders().stream())
                .map(obj -> obj.getSplitDetail().getInventoryLockedId())
                .toList();
        var lockedList = inventoryLockedService.listByIds(lockedIds);
        var lockedMap = StreamUtils.toMap(lockedList, IdModel::getId);

        // InventoryLocked 拆单
        var splitLockedList = splitHolders.stream()
                .flatMap(holder -> holder.getDetailHolders().stream()
                        .map(detailHolder -> {
                            var currentLocked = lockedMap.get(detailHolder.getDetail().getInventoryLockedId());
                            currentLocked.setQty(currentLocked.getQty() - detailHolder.getSplitQty());
                            // FinishQty
                            var splitFinishQty = currentLocked.getFinishQty() > currentLocked.getQty()
                                    ? currentLocked.getFinishQty() - currentLocked.getQty() : 0;
                            currentLocked.setFinishQty(currentLocked.getFinishQty() - splitFinishQty);
                            LockedHelper.statusRefresh(currentLocked);

                            // InventoryLocked 拆单
                            var splitLocked = BeanUtil.copyNew(currentLocked, InventoryLocked.class);
                            splitLocked.setId(IdWorker.getId());

                            // 设置数量更新状态
                            splitLocked.setQty(detailHolder.getSplitQty());
                            splitLocked.setFinishQty(splitFinishQty);
                            LockedHelper.statusRefresh(splitLocked);

                            splitLocked.setRefTableShowRefNum(holder.getSplitWorkorder().getRefNum());
                            var splitDetail = detailHolder.getSplitDetail();
                            splitLocked.setRefTableName(String.valueOf(splitDetail.getLineNum()));
                            splitLocked.setRefTableId(splitDetail.getId());
                            return splitLocked;
                        }))
                .toList();
        inventoryLockedService.insertBatch(splitLockedList);

        Validate.isTrue(inventoryLockedService.updateBatch(lockedList) == lockedList.size(),
                "Update InventoryLocked qty fail"
        );
    }

    /**
     * 上架锁拆分
     *
     * @param splitHolders splitHolders
     */
    private void putawayLockedSplit(List<OtcWorkorderSplitBO> splitHolders) {
        var refTableIds = splitHolders.stream()
                .flatMap(obj -> obj.getDetailHolders().stream().map(OtcWorkorderSplitDetailBO::getDetail).map(IdModel::getId))
                .toList();

        var binLockedList = binLocationDetailLockedService.listByRefTableIds(refTableIds);
        var lockedsMap = StreamUtils.groupBy(binLockedList, BinLocationDetailLocked::getRefTableId);

        // 拆单上锁
        var splitWorkorderMap = splitHolders.stream()
                .map(OtcWorkorderSplitBO::getSplitWorkorder)
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));
        var splitLockedList = splitHolders.stream()
                .flatMap(obj -> obj.getDetailHolders().stream())
                .flatMap(detailHolder -> {
                    var currentLockedList = lockedsMap.get(detailHolder.getDetail().getId());

                    var allocations = BeanUtil.copyNew(currentLockedList, AllocationBO.class);

                    // 分配拆单数量
                    AllocationUtil.checkAndAllocationQty(allocations, detailHolder.getSplitQty());

                    var currentLockedMap = StreamUtils.toMap(currentLockedList, IdModel::getId);
                    return allocations.stream()
                            .filter(allocation -> allocation.getChangeQty() > 0)
                            .map(allocation -> {
                                var changeQty = allocation.getChangeQty();

                                var currentLocked = currentLockedMap.get(allocation.getId());
                                currentLocked.setQty(currentLocked.getQty() - changeQty);
                                // FinishQty
                                var splitFinishQty = currentLocked.getFinishQty() > currentLocked.getQty()
                                        ? currentLocked.getFinishQty() - currentLocked.getQty() : 0;
                                currentLocked.setFinishQty(currentLocked.getFinishQty() - splitFinishQty);
                                LockedHelper.statusRefresh(currentLocked);

                                // 拆单上锁
                                BinLocationDetailLocked locked = BeanUtil.copyNew(currentLocked, BinLocationDetailLocked.class);
                                locked.setId(IdWorker.getId());

                                locked.setQty(changeQty);
                                // 工单状态判断
                                locked.setFinishQty(splitFinishQty);
                                LockedHelper.statusRefresh(locked);

                                var splitDetail = detailHolder.getSplitDetail();
                                locked.setRefTableId(splitDetail.getId());
                                locked.setRefTableRefNum(String.valueOf(splitDetail.getLineNum()));
                                Optional.ofNullable(splitWorkorderMap.get(splitDetail.getOtcWorkorderId()))
                                        .ifPresent(slip -> locked.setRefTableShowRefNum(slip.getRefNum()));

                                return locked;
                            });
                })
                .toList();

        binLocationDetailLockedService.insertBatch(splitLockedList);

        Validate.isTrue(binLocationDetailLockedService.updateBatch(binLockedList) == binLockedList.size(),
                "Update BinLocationLocked qty fail"
        );
    }


    /**
     * 触发工单相关流程，联动拣货单、包裹、Prep等，记录日志
     *
     * @param process       触发工单条件
     * @param workorderList 触发的工单
     */
    private void processTriggering(WorkorderProcessBO process, List<OtcWorkorder> workorderList) {
        String type = process.getProcessType().getType();
        // 设置流程状态
        workorderList.forEach(obj -> {
            obj.setProcessType(type);
            if (ProcessType.abnormal().contains(type)) {
                obj.setNote(process.getNote());
            }
        });

        Validate.isTrue(otcWorkorderService.updateBatch(workorderList) == workorderList.size(), "Update WorkOrder status [{}] failed", type);

        // 记录日志
        OtcWorkOrderAuditLogHelper.recordLog(workorderList, type, null, process.getNote(), BaseTypeLogEnum.PROCESS_TYPE.getType());

        process.setIdList(StreamUtils.distinctMap(workorderList, OtcWorkorder::getId));

        process.setPickingSlipIds(StreamUtils.distinctMap(workorderList, OtcWorkorder::getOtcPickingSlipId));


        // 包裹：流程触发
        otcPackageSpecialService.processTriggering(process);

        // todo: 拣货单Cancel 暂时不做哈
        // 拣货单 流程触发
        otcPickingSlipSpecialService.processTriggering(process);

        // Prep 流程触发
        var workorderPrepIds = workorderList.stream()
                .filter(obj -> !Objects.equals(obj.getWorkorderPrepStatus(), WorkOrderPrepStatusEnum.NONE.getStatus()))
                .map(IdModel::getId)
                .toList();
        process.setHasPrepWorkorderIds(workorderPrepIds);
        otcPrepWorkorderSpecialService.processTriggering(process);

    }

    /**
     * 获取并校验可启动流程的工单
     *
     * @param process 流程条件
     * @return /
     */
    private List<OtcWorkorder> getAndCheckStartWorkorder(WorkorderProcessBO process) {
        List<OtcWorkorder> workorderList = otcWorkorderService.listByIds(process.getIdList());

        return checkStartWorkorder(process, workorderList);
    }

    /**
     * 获取并校验可启动取消流程的工单
     *
     * @param process 流程条件
     * @return /
     */
    private List<OtcWorkorder> getAndCheckStartCancelWorkorder(WorkorderProcessBO process) {
        List<OtcWorkorder> workorderList = otcWorkorderService.listByRequestIds(process.getRequestIdList());

        return checkStartWorkorder(process, workorderList);
    }

    /**
     * 获取并校验可完成流程的工单
     *
     * @param process 流程条件
     * @return /
     */
    private List<OtcWorkorder> getAndCheckFinishWorkorder(WorkorderProcessBO process) {
        List<OtcWorkorder> workorderList = otcWorkorderService.listByIds(process.getIdList());
        return this.checkFinishWorkorder(process, workorderList);
    }

    @NotNull
    private List<OtcWorkorder> checkFinishWorkorder(WorkorderProcessBO process, List<OtcWorkorder> workorderList) {
        Validate.notEmpty(workorderList, "WorkOrderId: {} is not exist", process.getIdList());

        // 校验上架是否全部完成
        otcPutawaySlipService.finishRollback(workorderList);

        workorderList.forEach(obj -> ProcessType.checkAbnormal(obj.getProcessType(), obj.refNumLog(), "finish"));
        return workorderList;
    }

    /**
     * 获取并校验可完成取消流程的工单
     *
     * @param process 流程条件
     * @return /
     */
    private List<OtcWorkorder> getAndCheckFinishCancelWorkorder(WorkorderProcessBO process) {
        List<OtcWorkorder> workorderList = otcWorkorderService.listByIds(process.getIdList());
        return this.checkFinishWorkorder(process, workorderList);
    }

    /**
     * 回滚ReadyToShip数量
     *
     * @param rollback rollback
     */
    private void rollbackReadyToShipQty(OtcPackageRollbackSingleWorkorderBO rollback) {
        List<OtcPackage> readyToShipPkgList = rollback.getPkgList().stream()
                .filter(obj -> Objects.equals(obj.getPackageStatus(), OtcPackageStatusEnum.READY_TO_SHIP.getStatus()))
                .toList();

        Map<Long, OtcPackage> readyToShipMap = StreamUtils.toMap(readyToShipPkgList, OtcPackage::getId);
        Map<Long, Integer> readToShipQtyWithProductMap = rollback.getPkgDetailList().stream()
                .filter(obj -> readyToShipMap.containsKey(obj.getOtcPackageId()))
                .collect(Collectors.groupingBy(OtcPackageDetail::getProductId, Collectors.summingInt(OtcPackageDetail::getPickedQty)));

        OtcWorkorder workorder = rollback.getWorkorder();

        // 回滚ReadyToShip数量
        List<OtcWorkorderDetail> wkDetails = rollback.getWorkorderDetailList();
        List<ChangeQtyLogBO> readyToShipChangeList = wkDetails.stream()
                .filter(obj -> readToShipQtyWithProductMap.containsKey(obj.getProductId()))
                .map(detail -> {
                    Integer rollbackQty = readToShipQtyWithProductMap.get(detail.getProductId());
                    Validate.isTrue(detail.getReadyToShipQty() >= rollbackQty, String.format(ErrorMessages.INSUFFICIENT_QUANTITY,
                            StringUtil.format("{} LineNum: {} ReadyToShipQty is not enough, [ReadyToShipQty: {}] < [RollbackQty: {}]",
                                    workorder.refNumLog(), detail.getLineNum(), detail.getReadyToShipQty(), rollbackQty))
                    );
                    int readyToShipQty = detail.getReadyToShipQty() - rollbackQty;
                    ChangeQtyLogBO change = new ChangeQtyLogBO();
                    change.setBeforeQty(detail.getReadyToShipQty());
                    change.setAfterQty(readyToShipQty);
                    change.setProductId(detail.getProductId());

                    detail.setReadyToShipQty(readyToShipQty);
                    return change;
                }).toList();

        // 工单 Rollback ReadyToShipQty 记录日志
        OtcWorkOrderAuditLogHelper.recordLog(rollback.getWorkorder(), RollbackConstant.ROLLBACK_READY_TO_SHIP_QTY,
                JsonUtil.toJson(readyToShipChangeList), rollback.getNote(), BaseTypeLogEnum.OPERATION.getType()
        );

        // 设置状态
        boolean picked = wkDetails.stream().allMatch(obj -> Objects.equals(obj.getPickedQty(), obj.getQty()));
        String oldStatus = rollback.getWorkorder().getOtcWorkorderStatus();
        rollback.getWorkorder().setOtcWorkorderStatus(picked
                ? OtcWorkorderStatusEnum.PICKED.getStatus()
                : OtcWorkorderStatusEnum.IN_PICKING.getStatus()
        );
        // 状态变更记录日志
        if (!Objects.equals(oldStatus, rollback.getWorkorder().getOtcWorkorderStatus())) {
            OtcWorkOrderAuditLogHelper.recordLog(rollback.getWorkorder());
        }
    }


    /**
     * 回滚打包数量
     *
     * @param rollback rollback
     */
    private void rollbackPackedQty(OtcPackageRollbackSingleWorkorderBO rollback) {
        boolean shippingLabelFlag = ObjectUtil.nullToDefault(rollback.getWorkorder().getRequestSnapshotProvideShippingLabelFlag(), false);

        List<String> warehousePackedStatuses = Arrays.asList(OtcPackageStatusEnum.READY_TO_SHIP.getStatus(), OtcPackageStatusEnum.PICKED.getStatus());
        List<OtcPackage> packedList = rollback.getPkgList().stream().filter(pkg -> shippingLabelFlag
                // 用户指定
                ? Objects.equals(pkg.getPackageStatus(), OtcPackageStatusEnum.READY_TO_SHIP.getStatus())
                // 仓库构建包裹
                : warehousePackedStatuses.contains(pkg.getPackageStatus())).toList();

        Map<Long, OtcPackage> packedMap = StreamUtils.toMap(packedList, OtcPackage::getId);
        Map<Long, Integer> pickedQtyWithProductMap = rollback.getPkgDetailList().stream().filter(obj -> packedMap.containsKey(obj.getOtcPackageId())).collect(Collectors.groupingBy(OtcPackageDetail::getProductId, Collectors.summingInt(OtcPackageDetail::getPickedQty)));

        List<ChangeQtyLogBO> packedChangeList = rollback.getWorkorderDetailList().stream().filter(obj -> pickedQtyWithProductMap.containsKey(obj.getProductId())).map(detail -> {
            Integer rollbackQty = pickedQtyWithProductMap.get(detail.getProductId());
            Validate.isTrue(detail.getReadyToShipQty() >= rollbackQty, "{} LineNum: {} PackedQty is not enough", rollback.getWorkorder().refNumLog(), detail.getLineNum());
            int packedQty = detail.getPackedQty() - rollbackQty;

            ChangeQtyLogBO change = new ChangeQtyLogBO();
            change.setBeforeQty(detail.getReadyToShipQty());
            change.setAfterQty(packedQty);
            change.setProductId(detail.getProductId());

            detail.setPackedQty(packedQty);
            return change;
        }).toList();

        // 工单 Rollback PackedQty 记录日志
        OtcWorkOrderAuditLogHelper.recordLog(rollback.getWorkorder(), RollbackConstant.ROLLBACK_READY_PACKED_QTY, JsonUtil.toJson(packedChangeList), rollback.getNote(), BaseTypeLogEnum.OPERATION.getType());

        rollback.getWorkorder().setOtcWorkorderStatus(OtcWorkorderStatusEnum.PICKED.getStatus());
        OtcWorkOrderAuditLogHelper.recordLog(rollback.getWorkorder());
    }
}
