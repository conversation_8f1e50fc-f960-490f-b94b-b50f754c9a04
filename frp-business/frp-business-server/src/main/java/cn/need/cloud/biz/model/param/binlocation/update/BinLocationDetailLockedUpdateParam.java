package cn.need.cloud.biz.model.param.binlocation.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 锁定 库位详情 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "锁定 库位详情 vo对象")
public class BinLocationDetailLockedUpdateParam implements Serializable {

    /**
     * refTableId
     */
    @Schema(description = "refTableId")
    private Long refTableId;

    /**
     * refTableName
     */
    @Schema(description = "refTableName")
    private String refTableName;

    /**
     * refTableRefNum
     */
    @Schema(description = "refTableRefNum")
    private String refTableRefNum;

    /**
     * refTableShowName
     */
    @Schema(description = "refTableShowName")
    private String refTableShowName;

    /**
     * refTableShowRefNum
     */
    @Schema(description = "refTableShowRefNum")
    private String refTableShowRefNum;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 库位详情id
     */
    @Schema(description = "库位详情id")
    private Long binLocationDetailId;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * finishQty
     */
    @Schema(description = "finishQty")
    private Integer finishQty;

    /**
     * 锁定状态
     */
    @Schema(description = "锁定状态")
    private String lockedStatus;


    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

}