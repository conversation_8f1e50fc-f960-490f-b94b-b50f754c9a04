package cn.need.cloud.biz.model.entity.product;

import cn.need.cloud.biz.model.entity.base.RefNumModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * <p>
 * 产品
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product")
public class Product extends RefNumModel {


    @Serial
    private static final long serialVersionUID = -7049741359449008455L;
    /**
     * 交易伙伴ID
     */
    @TableField("transaction_partner_id")
    private Long transactionPartnerId;

    /**
     * 唯一标识码
     */
    @TableField("ref_num")
    private String refNum;

    /**
     * 供应商SKU
     */
    @TableField("supplier_sku")
    private String supplierSku;

    /**
     * UPC码
     */
    @TableField("upc")
    private String upc;

    /**
     * 组装产品标志
     */
    @TableField("assembly_product_flag")
    private Boolean assemblyProductFlag;


    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 标题
     */
    @TableField("title")
    private String title;


    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 多箱标志
     */
    @TableField("multibox_flag")
    private Boolean multiboxFlag;

    /**
     * 组类型
     */
    @TableField("group_type")
    private String groupType;

    /**
     * 产品类型
     */
    @TableField("product_type")
    private String productType;

    //todo: 只实现了 hazmat 版本号，其他未实现
    //其他类型要优化代码后，在实现！！！！

    /**
     * group 版本号
     */
    @TableField("group_version_ref_num")
    private String groupVersionRefNum;
    /**
     * multibox 版本号
     */
    @TableField("multibox_version_ref_num")
    private String multiboxVersionRefNum;
    /**
     * assembly 版本号
     */
    @TableField("assembly_version_ref_num")
    private String assemblyVersionRefNum;
    /**
     * hazmat 版本号
     */
    @TableField("hazmat_version_ref_num")
    private String hazmatVersionRefNum;

}
