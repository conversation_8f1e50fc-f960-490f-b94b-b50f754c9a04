package cn.need.cloud.biz.model.vo.inbound.putaway;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.BaseWarehouseVO;
import cn.need.cloud.biz.model.vo.base.aware.BaseFullBinLocationAware;
import cn.need.cloud.biz.model.vo.inbound.unload.InboundUnloadVO;
import cn.need.cloud.biz.model.vo.warehouse.PalletTemplateVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 上架 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "上架 vo对象")
public class InboundPutawaySlipVO extends BaseSuperVO implements BaseFullBinLocationAware {

    /**
     * 卸货信息
     */
    @Schema(description = "卸货信息")
    private List<InboundUnloadVO> inBoundUnloads;

    /**
     * 打托模板
     */
    @Schema(description = "打托模板")
    private PalletTemplateVO palletTemplateVO;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * 上架状态
     */
    @Schema(description = "上架状态")
    private String inboundPutawaySlipStatus;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 仓库基本信息
     */
    @Schema(description = "仓库基本信息")
    private BaseWarehouseVO baseWarehouseVO;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

}