package cn.need.cloud.biz.model.query;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;


/**
 * 预请求详情 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "预请求详情 query对象")
public class PrepRequestDetailQuery extends SuperQuery {

    @Serial
    private static final long serialVersionUID = -9198879785294369410L;
    /**
     * 准备请求ID
     */
    @Schema(description = "准备请求ID")
    private Long prepRequestId;

    /**
     * 准备类型
     */
    @Schema(description = "准备类型")
    private String prepType;

    /**
     * 准备类型
     */
    @Schema(description = "准备类型集合")
    @Condition(value = Keyword.IN, fields = {"prepType"})
    private List<String> prepTypeList;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;


}