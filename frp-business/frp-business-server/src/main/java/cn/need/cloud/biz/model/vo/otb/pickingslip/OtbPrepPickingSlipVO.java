package cn.need.cloud.biz.model.vo.otb.pickingslip;

import cn.need.cloud.biz.client.constant.enums.base.ShipTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otb.PaperTypeEnum;
import cn.need.cloud.biz.model.vo.base.BasePartnerVO;
import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.aware.BaseBinLocationAware;
import cn.need.cloud.biz.model.vo.base.aware.BaseProductAware;
import cn.need.cloud.biz.model.vo.base.aware.BaseWarehouseAware;
import cn.need.cloud.dfs.client.constant.enums.FileDataTypeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;


/**
 * otb预拣货单 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "otb预拣货单 vo对象")
public class OtbPrepPickingSlipVO extends BaseSuperVO implements BaseProductAware, BaseBinLocationAware, BaseWarehouseAware {

    @Serial
    private static final long serialVersionUID = -1564716591335164226L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String detailProductType;

    /**
     * otb预拣货单状态
     */
    @Schema(description = "otb预拣货单状态")
    private String otbPrepPickingSlipStatus;

    /**
     * otb预拣货单类型
     */
    @Schema(description = "otb预拣货单类型")
    private String otbPrepPickingSlipType;

    /**
     * otb预拣货单类型
     */
    @Schema(description = "otb预拣货单产品类型")
    private String prepPickingSlipProductType;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    private String hazmatVersionRefNum;

    /**
     * 渠道要求的需要贴的产品标识码
     */
    @Schema(description = "渠道要求的需要贴的产品标识码")
    private String productBarcode;

    /**
     * 渠道要求的需要贴的产品标识码SKU
     */
    @Schema(description = "渠道要求的需要贴的产品标识码SKU")
    private String productChannelSku;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 描述
     */
    @Schema(description = "HowToDo")
    private String description;


    /**
     * 供应商id
     */
    @Schema(description = "供应商id")
    private Long transactionPartnerId;

    /**
     * 供应商
     */
    @Schema(description = "供应商")
    private BasePartnerVO basePartnerVO;

    /**
     * 详情
     */
    @Schema(description = "详情")
    private List<OtbPrepPickingSlipDetailVO> detailList;

    /**
     * 可以上架数量
     */
    @Schema(description = "可以上架数量")
    private Integer canPutawayQty;

    /**
     * d
     * 发货类型
     */
    @Schema(description = "发货类型")
    private ShipTypeEnum shipType;

    // /**
    //  * RI文件
    //  */
    // @Schema(description = "RI文件")
    // private FrpFileModel routingInstructionFile;

    /**
     * RI文件数据
     */
    @TableField("routing_instruction_file_file_data")
    private String routingInstructionFileFileData;

    /**
     * RI文件扩展名
     */
    @TableField("routing_instruction_file_file_extension")
    private String routingInstructionFileFileExtension;

    /**
     * RI文件类型
     */
    @TableField("routing_instruction_file_file_type")
    private FileDataTypeEnum routingInstructionFileFileType;

    /**
     * RI文件纸张类型
     */
    @TableField("routing_instruction_file_paper_type")
    private PaperTypeEnum routingInstructionFilePaperType;


    // ------------------------------ 以下非详情字段 ------------------------------

    /**
     * 拣货到那里
     */
    @Schema(description = "拣货到那里")
    private String pickToStation;


    /**
     * otb拣货单id
     */
    @Schema(description = "otb拣货单id")
    private Long otbPickingSlipId;


    /**
     * 已经分配的上架数量
     */
    @Schema(description = "已经分配的上架数量")
    private Integer allocatePutawayQty;

    /**
     * 上架数量
     */
    @Schema(description = "上架数量")
    private Integer putawayQty;

    @Schema(description = "流程类型")
    private String processType;

}