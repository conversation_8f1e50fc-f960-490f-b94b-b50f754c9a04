package cn.need.cloud.biz.jackson;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.lang.annotation.*;
import java.math.RoundingMode;

/**
 * BigDecimalFormat
 *
 * <AUTHOR>
 * @since 2023-01-31
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@Documented
@JacksonAnnotationsInside
@JsonDeserialize(using = BigDecimalDeserializer.class)
@JsonSerialize(using = BigDecimalSerializer.class)
public @interface BigDecimalFormat {

    /**
     * 默认值, 凡是加了 @BigDecimalFormat 注解, 又没有指定 value 值的, 都会被格式化为下面的形式
     */
    String value() default "#0.00";

    /**
     * 精度
     */
    int scale() default 2;

    /**
     * 舍入模式
     */
    RoundingMode roundingMode() default RoundingMode.HALF_UP;
}
