package cn.need.cloud.biz.service.inventory;

import cn.need.cloud.biz.model.query.inventory.InventoryQuery;
import cn.need.cloud.biz.model.vo.inventory.InventoryInStockVO;
import cn.need.cloud.biz.model.vo.inventory.InventoryVO;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 库存 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface InventoryService {


    /**
     * 根据ProductId 获取inStock产品库存
     *
     * @param productIds 产品ID列表
     * @return inStock库存信息
     */
    List<InventoryInStockVO> getInventoryInStock(List<Long> productIds);

    /**
     * 根据ProductId 获取产品库存
     *
     * @param productIds 产品ID列表
     * @return 库存信息
     */
    List<InventoryVO> getInventory(List<Long> productIds);

    /**
     * 根据 ProductId 列表获取产品库存信息
     *
     * @param productIdList 产品ID列表
     * @return 产品库存信息列表
     */
    Map<Long, InventoryVO> getInventoryReturnAll(List<Long> productIdList);

    /**
     * 根据ProductId，获取所有仓库库存
     *
     * @param productId 产品ID
     * @return 库存信息
     */
    List<InventoryVO> listWarehouseInventoryListByProductId(Long productId);

    /**
     * 分页查询库存信息
     *
     * @param search 包含查询条件和分页信息
     * @return 带分页信息的库存数据
     */
    PageData<InventoryVO> pageByQuery(PageSearch<InventoryQuery> search);
}