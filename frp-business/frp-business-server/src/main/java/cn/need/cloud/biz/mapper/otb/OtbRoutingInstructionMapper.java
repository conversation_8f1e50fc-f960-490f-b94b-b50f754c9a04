package cn.need.cloud.biz.mapper.otb;

import cn.need.cloud.biz.model.bo.common.DropColumnInfoBO;
import cn.need.cloud.biz.model.entity.otb.OtbRoutingInstruction;
import cn.need.cloud.biz.model.query.otb.routing.OtbRoutingInstructionQuery;
import cn.need.cloud.biz.model.vo.otb.page.OtbRoutingInstructionPageVO;
import cn.need.cloud.biz.model.vo.otb.ri.OtbRoutingInstructionVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * otb发货指南 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtbRoutingInstructionMapper extends SuperMapper<OtbRoutingInstruction> {

    /**
     * 根据条件获取otb发货指南列表
     *
     * @param query 查询条件
     * @return otb发货指南集合
     */
    default List<OtbRoutingInstructionPageVO> listByQuery(OtbRoutingInstructionQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取otb发货指南分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return otb发货指南集合
     */
    List<OtbRoutingInstructionPageVO> listByQuery(@Param("qo") OtbRoutingInstructionQuery query, @Param("page") Page<?> page);

    /**
     * 根据条件获取otb发货指南列表
     *
     * @param query 查询条件
     * @return otb发货指南集合
     */
    List<Map<String, Object>> dropProList(@Param("columnList") List<DropColumnInfoBO> columnList, @Param("qo") OtbRoutingInstructionQuery query);

    /**
     * 根据ID获取详细信息
     * <p>
     * 此方法用于通过特定的ID获取OtbRoutingInstructionVO对象的详细信息
     * 它通常用于需要根据唯一标识符检索对象的场景
     *
     * @param id 需要获取详细信息的OtbRoutingInstructionVO对象的ID
     * @return 返回一个包含详细信息的OtbRoutingInstructionVO对象如果找不到对应的对象，则返回null
     */
    OtbRoutingInstructionVO getDetailById(Long id);

    void updateRequestPickupDate(@Param("entity") OtbRoutingInstruction entity);
}