package cn.need.cloud.biz.model.param.otc.create.pkg;

import cn.need.cloud.biz.client.constant.enums.base.DetailProductTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * OTC包裹 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC包裹 vo对象")
public class OtcPackageCreateParam implements Serializable {


    @Serial
    private static final long serialVersionUID = 7874859866516856040L;
    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 快递号
     */
    @Schema(description = "快递号")
    private String trackingNum;

    /**
     * 保险金额
     */
    @Schema(description = "保险金额")
    private BigDecimal insuranceAmountAmount;

    /**
     * 签名类型
     */
    @Schema(description = "签名类型")
    private String signatureType;

    /**
     * 收件人名称
     */
    @Schema(description = "收件人名称")
    private String shipToAddressName;

    /**
     * 收件公司
     */
    @Schema(description = "收件公司")
    private String shipToAddressCompany;

    /**
     * 收件国家
     */
    @Schema(description = "收件国家")
    private String shipToAddressCountry;

    /**
     * 收件州
     */
    @Schema(description = "收件州")
    private String shipToAddressState;

    /**
     * 收件城市
     */
    @Schema(description = "收件城市")
    private String shipToAddressCity;

    /**
     * 收件邮政编码
     */
    @Schema(description = "收件邮政编码")
    private String shipToAddressZipCode;

    /**
     * 收件地址1
     */
    @Schema(description = "收件地址1")
    private String shipToAddressAddr1;

    /**
     * 收件地址2
     */
    @Schema(description = "收件地址2")
    private String shipToAddressAddr2;

    /**
     * 收件地址3
     */
    @Schema(description = "收件地址3")
    private String shipToAddressAddr3;

    /**
     * 收件邮件信息
     */
    @Schema(description = "收件邮件信息")
    private String shipToAddressEmail;

    /**
     * 收件电话信息
     */
    @Schema(description = "收件电话信息")
    private String shipToAddressPhone;

    /**
     * 收件备注
     */
    @Schema(description = "收件备注")
    private String shipToAddressNote;

    /**
     * 发件人名称
     */
    @Schema(description = "发件人名称")
    private String shipFromAddressName;

    /**
     * 发件公司
     */
    @Schema(description = "发件公司")
    private String shipFromAddressCompany;

    /**
     * 发件国家
     */
    @Schema(description = "发件国家")
    private String shipFromAddressCountry;

    /**
     * 发件州
     */
    @Schema(description = "发件州")
    private String shipFromAddressState;

    /**
     * 发件城市
     */
    @Schema(description = "发件城市")
    private String shipFromAddressCity;

    /**
     * 发件邮政编码
     */
    @Schema(description = "发件邮政编码")
    private String shipFromAddressZipCode;

    /**
     * 发件地址1
     */
    @Schema(description = "发件地址1")
    private String shipFromAddressAddr1;

    /**
     * 发件地址2
     */
    @Schema(description = "发件地址2")
    private String shipFromAddressAddr2;

    /**
     * 发件地址3
     */
    @Schema(description = "发件地址3")
    private String shipFromAddressAddr3;

    /**
     * 发件邮件
     */
    @Schema(description = "发件邮件")
    private String shipFromAddressEmail;

    /**
     * 发件电话
     */
    @Schema(description = "发件电话")
    private String shipFromAddressPhone;

    /**
     * 发件备注
     */
    @Schema(description = "发件备注")
    private String shipFromAddressNote;

    /**
     * c端出货工单id
     */
    @Schema(description = "c端出货工单id")
    private Long otcWorkorderId;

    /**
     * 是否为快递运输
     */
    @Schema(description = "是否为快递运输")
    private Boolean shipExpressFlag;

    /**
     * 运输方式
     */
    @Schema(description = "运输方式")
    private String shipMethod;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    /**
     * 运输箱子-长
     */
    @Schema(description = "运输箱子-长")
    private BigDecimal shipSizeLength;

    /**
     * 运输箱子-宽
     */
    @Schema(description = "运输箱子-宽")
    private BigDecimal shipSizeWidth;

    /**
     * 运输箱子-高
     */
    @Schema(description = "运输箱子-高")
    private BigDecimal shipSizeHeight;

    /**
     * 运输箱子-重量
     */
    @Schema(description = "运输箱子-重量")
    private BigDecimal shipSizeWeight;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 构建运输策略
     */
    @Schema(description = "构建运输策略")
    private String buildShipStrategy;

    /**
     * 包裹状态
     */
    @Schema(description = "包裹状态")
    private String packageStatus;


    /**
     * c端拣货id
     */
    @Schema(description = "c端拣货id")
    private Long otcPickingSlipId;

    /**
     * 准备运输时间
     */
    @Schema(description = "准备运输时间")
    private LocalDateTime readyToShipTime;

    /**
     * 已运输时间
     */
    @Schema(description = "已运输时间")
    private LocalDateTime shippedTime;

    /**
     * 住宅地址发件运输
     */
    @Schema(description = "住宅地址发件运输")
    private Boolean shipFromAddressIsResidential;

    /**
     * 运输箱子-长度单位
     */
    @Schema(description = "运输箱子-长度单位")
    private String shipSizeDimensionUnit;

    /**
     * 运输箱子-重量单位
     */
    @Schema(description = "运输箱子-重量单位")
    private String shipSizeWeightUnit;

    /**
     * 收货地址是否为住宅
     */
    @Schema(description = "收货地址是否为住宅")
    private Boolean shipToAddressIsResidential;

    /**
     * 保险金额货币
     */
    @Schema(description = "保险金额货币")
    private String insuranceAmountCurrency;

    /**
     * 快递Api配置RefNum
     */
    @Schema(description = "快递Api配置RefNum")
    private String shipApiProfileRefNum;

    /**
     * 多盒包裹编码
     */
    @Schema(description = "多盒包裹编码")
    private String packageMultiboxUpc;

    /**
     * 包裹类型
     */
    @Schema(description = "包裹类型")
    private String packageType;

    /**
     * 多盒包裹行号
     */
    @Schema(description = "多盒包裹行号")
    private Integer packageMultiboxLineNum;

    /**
     * 多盒包裹产品版本id
     */
    @Schema(description = "多盒包裹产品新品id")
    private Long packageMultiboxProductId;

    /**
     * 多盒包裹版本
     */
    @Schema(description = "多盒包裹版本")
    private Integer packageMultiboxVersionInt;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    @Size(max = 64, message = "detailProductType cannot exceed 64 characters")
    private String detailProductType = DetailProductTypeEnum.NORMAL.getCode();

}