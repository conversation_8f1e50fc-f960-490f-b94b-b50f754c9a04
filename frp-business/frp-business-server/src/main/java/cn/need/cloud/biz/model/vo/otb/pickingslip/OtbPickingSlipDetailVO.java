package cn.need.cloud.biz.model.vo.otb.pickingslip;

import cn.need.cloud.biz.model.vo.base.aware.BaseBinLocationAware;
import cn.need.cloud.biz.model.vo.base.aware.BaseFullProductVersionAware;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * otb拣货单详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "otb拣货单详情 vo对象")
public class OtbPickingSlipDetailVO implements Serializable, BaseFullProductVersionAware, BaseBinLocationAware {

    @Serial
    private static final long serialVersionUID = 6312343239519747655L;
    private Long id;

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id")
    private Long otbPickingSlipId;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    private String hazmatVersionRefNum;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    /**
     * 渠道要求的需要贴的产品标识码
     */
    @Schema(description = "渠道要求的需要贴的产品标识码")
    private String productBarcode;

    /**
     * 渠道sku
     */
    @Schema(description = "渠道sku")
    private String productChannelSku;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 拣货数量
     */
    @Schema(description = "拣货数量")
    private Integer pickedQty;

    /**
     * 打包为Package 数量
     */
    @Schema(description = "打包为Package 数量")
    private Integer packedQty;

    /**
     * 做成ShipmentQty
     */
    @Schema(description = "做成ShipmentQty")
    private Integer shipmentQty;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * relabel状态
     */
    @Schema(description = "relabel状态")
    private String relabelStatus;

    /**
     * 需要拣货数量
     */
    @Schema(description = "需要拣货数量")
    public Integer getNeedPickQty() {
        return qty - pickedQty;
    }

    /**
     * 需要打包为Package 数量
     */
    @Schema(description = "需要打包为Package 数量")
    public Integer getNeedPackedQty() {
        return pickedQty - packedQty;
    }

    /**
     * 总共需要打包为Package 数量
     */
    @Schema(description = "需要打包为Package 数量")
    public Integer getTotalNeedPackQty() {
        return qty - packedQty;
    }
}