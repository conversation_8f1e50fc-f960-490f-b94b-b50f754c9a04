package cn.need.cloud.biz.service.otb.pkg;

import cn.need.cloud.biz.model.bo.base.WorkorderProcessBO;
import cn.need.cloud.biz.model.bo.otb.putawayslip.OtbPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.entity.otb.OtbShipment;
import cn.need.cloud.biz.model.entity.otb.OtbWorkorder;
import cn.need.cloud.biz.model.param.otb.update.pkg.OtbPackageBatchRollbackUpdateParam;
import cn.need.cloud.biz.model.query.base.PackageRollbackListQuery;
import cn.need.cloud.biz.model.vo.base.pkg.PackageRollbackConfirmVO;

import java.util.List;

/**
 * <p>
 * OTC包裹 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbPackageSpecialService {

    /**
     * 触发流程
     *
     * @param process 流程条件
     */
    void processTriggering(WorkorderProcessBO process);

    /**
     * Rollback
     *
     * @param workorder 工单
     * @param param     /
     */
    void rollback(OtbWorkorder workorder, OtbPutawaySlipPutAwayBO param);

    /**
     * 批量Rollback
     *
     * @param param 批量参数
     * @return /
     */
    boolean batchRollback(OtbPackageBatchRollbackUpdateParam param);

    /**
     * 包裹Rollback确认列表
     *
     * @param param rollback列表条件
     * @return /
     */
    PackageRollbackConfirmVO rollbackConfirm(PackageRollbackListQuery param);

    /**
     * 批量Cancel
     *
     * @param param 批量参数
     * @return /
     */
    boolean batchCancel(OtbPackageBatchRollbackUpdateParam param);

    /**
     * Finish Cancel
     *
     * @param workorderList 工单列表
     */
    void finishCancel(List<Long> workorderList);

    /**
     * Rollback By Shipment
     *
     * @param cancelList /
     */
    void rollbackByShipment(List<OtbShipment> cancelList);
}