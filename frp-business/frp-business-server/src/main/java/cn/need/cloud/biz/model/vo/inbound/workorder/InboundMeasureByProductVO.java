package cn.need.cloud.biz.model.vo.inbound.workorder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 入库测量 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "入库测量 vo对象")
public class InboundMeasureByProductVO extends InboundMeasureVO {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 入库工单详情id
     */
    @Schema(description = "入库工单详情id")
    private Long workOrderDetailId;

}
