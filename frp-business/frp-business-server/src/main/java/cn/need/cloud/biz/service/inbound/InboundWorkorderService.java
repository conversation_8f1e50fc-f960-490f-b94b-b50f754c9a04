package cn.need.cloud.biz.service.inbound;

import cn.need.cloud.biz.model.bo.inbound.InboundRequestAuditContextBO;
import cn.need.cloud.biz.model.entity.inbound.InboundWorkorder;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.inbound.InboundPalletQuery;
import cn.need.cloud.biz.model.query.inbound.InboundWorkorderQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.inbound.workorder.InboundMeasureByProductVO;
import cn.need.cloud.biz.model.vo.inbound.workorder.InboundMeasureVO;
import cn.need.cloud.biz.model.vo.inbound.workorder.InboundWorkorderVO;
import cn.need.cloud.biz.model.vo.page.InboundWorkorderPageVO;
import cn.need.cloud.biz.service.base.HeaderPrintedService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 入库工单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface InboundWorkorderService extends SuperService<InboundWorkorder>, HeaderPrintedService<InboundWorkorder, InboundWorkorderService, PrintQuery> {


    /**
     * 根据查询条件获取入库工单列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个入库工单对象的列表(分页)
     */
    List<InboundWorkorderPageVO> listByQuery(InboundWorkorderQuery query);

    /**
     * 根据查询条件获取入库工单列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个入库工单对象的列表(分页)
     */
    PageData<InboundWorkorderPageVO> pageByQuery(PageSearch<InboundWorkorderQuery> search);

    /**
     * 根据ID获取入库工单
     *
     * @param id 入库工单ID
     * @return 返回入库工单VO对象
     */
    InboundWorkorderVO detailById(Long id);

    /**
     * 根据入库工单唯一编码获取入库工单
     *
     * @param refNum 入库工单唯一编码
     * @return 返回入库工单VO对象
     */
    InboundWorkorderVO detailByRefNum(String refNum);

    /**
     * 入库工单字段去重下拉
     */
    List<DropProVO> distinctValue(InboundWorkorderQuery query);

    /**
     * 确认到货
     *
     * @param id 入库工单id
     */
    void productArrived(Long id);

    /**
     * 更新入库工单状态
     *
     * @param id     入库工单id
     * @param status 入库工单状态
     */
    void updateStatus(Long id, String status);

    /**
     * 更新入库工单状态及卸货状态
     *
     * @param id              入库工单id
     * @param workOrderStatus 入库工单状态
     * @param unloadStatus    卸货状态
     */
    void updateStatus(Long id, String workOrderStatus, String unloadStatus);

    /**
     * 测量确认接口
     *
     * @param measureVO 测量信息
     */
    void confirm(InboundMeasureVO measureVO);

    /**
     * 测量确认接口
     *
     * @param measureVO 测量信息
     */
    void confirmByProduct(InboundMeasureByProductVO measureVO);

    /**
     * 根据工单
     *
     * @param condition 查询条件
     * @return 入库工单id
     */
    Set<Long> getInboundWorkOrderIds(InboundPalletQuery condition);

    /**
     * 完成卸货
     *
     * @param id 入库工单
     */
    void finishUnload(Long id);

    /**
     * 生成入库工单
     *
     * @param context 入库审计上下文信息
     */
    void generateWorkOrder(InboundRequestAuditContextBO context);

    /**
     * 更新工单卸货状态
     *
     * @param id     工单id
     * @param status 卸货状态
     */
    void updateUnloadStatus(Long id, String status);

    /**
     * 根据入库请求单id更新入库工单
     *
     * @param id     入库请求单id
     * @param status 状态
     */
    void updateStatusByRequestId(Long id, String status);

    /**
     * 根据请求单id获取工单
     *
     * @param id 请求单id
     * @return 工单信息
     */
    InboundWorkorder getByRequestId(Long id);
}