package cn.need.cloud.biz.model.vo.otb.workorder;

import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipDetailPickVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTC工单拣货详情信息 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTB工单详情拣货 vo对象")
public class OtbWorkorderDetailPickVO extends OtbPickingSlipDetailPickVO {

    /**
     * 发货到c端工单id
     */
    @Schema(description = "发货到c端工单id")
    private Long otbWorkorderId;

    /**
     * 拣货单详情id
     */
    @Schema(description = "拣货单详情id")
    private Long otbPickingSlipDetailId;


    /**
     * 工单分配仓储位置id
     */
    @Schema(description = "工单分配仓储位置id")
    private Long otbWorkorderBinLocationId;

    private String detailSnapshotProductBarcode;

    private String detailSnapshotProductChannelSku;

}