package cn.need.cloud.biz.model.entity.otc;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * <p>
 * OTC请求包裹详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otc_request_package_detail")
public class OtcRequestPackageDetail extends SuperModel {


    @Serial
    private static final long serialVersionUID = 356923126852466326L;
    /**
     * 行序号
     */
    @TableField("line_num")
    private Integer lineNum;

    /**
     * OTC请求ID
     */
    @TableField("otc_request_id")
    private Long otcRequestId;

    /**
     * 数量
     */
    @TableField("qty")
    private Integer qty;


    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * OTC请求包裹ID
     */
    @TableField("otc_request_package_id")
    private Long otcRequestPackageId;

}
