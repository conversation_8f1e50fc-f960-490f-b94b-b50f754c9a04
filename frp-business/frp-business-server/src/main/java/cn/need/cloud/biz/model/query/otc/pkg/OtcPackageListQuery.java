package cn.need.cloud.biz.model.query.otc.pkg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * OTC包裹 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC包裹 列表query对象")
public class OtcPackageListQuery {

    /**
     * OTC包裹 query对象
     */
    @Schema(description = "包裹查询条件")
    private OtcPackageQuery otcPackageQuery;

    /**
     * c端工单 query对象
     */
    @Schema(description = "工单查询条件")
    private OtcPackageWorkOrderQuery otcWorkorderQuery;
}