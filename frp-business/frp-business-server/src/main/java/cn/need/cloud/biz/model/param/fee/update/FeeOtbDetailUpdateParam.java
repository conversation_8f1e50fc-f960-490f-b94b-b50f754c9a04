package cn.need.cloud.biz.model.param.fee.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 费用详情otb UpdateParam对象
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@Schema(description = "费用详情otb UpdateParam对象")
public class FeeOtbDetailUpdateParam implements Serializable {

    @Serial
    private static final long serialVersionUID = 4777953691711683692L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    @Size(max = 512, message = "deletedNote cannot exceed 512 characters")
    private String deletedNote;
    /**
     * 费用
     */
    @Schema(description = "费用")
    @NotEmpty(message = "fee cannot be empty")
    private BigDecimal fee;
    /**
     * 费用配置明细id
     */
    @Schema(description = "费用配置明细id")
    @NotNull(message = "feeConfigDetailId cannot be null")
    private Long feeConfigDetailId;
    /**
     * 费用配置id
     */
    @Schema(description = "费用配置id")
    @NotNull(message = "feeConfigId cannot be null")
    private Long feeConfigId;
    /**
     * header表id
     */
    @Schema(description = "header表id")
    @NotNull(message = "headerId cannot be null")
    private Long headerId;
    /**
     * 行序号
     */
    @Schema(description = "行序号")
    @NotNull(message = "lineNum cannot be null")
    private Integer lineNum;
    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 512, message = "note cannot exceed 512 characters")
    private String note;
    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    @NotEmpty(message = "refNum cannot be empty")
    @Size(max = 50, message = "refNum cannot exceed 50 characters")
    private String refNum;
    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    @NotNull(message = "warehouseId cannot be null")
    private Long warehouseId;

}