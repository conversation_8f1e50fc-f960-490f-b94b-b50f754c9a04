package cn.need.cloud.biz.mapper.otb;

import cn.need.cloud.biz.model.entity.otb.OtbPalletLabel;
import cn.need.cloud.biz.model.query.otb.pallet.OtbPalletLabelQuery;
import cn.need.cloud.biz.model.vo.page.OtbPalletLabelPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * OTB托盘标签 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtbPalletLabelMapper extends SuperMapper<OtbPalletLabel> {

    /**
     * 根据条件获取OTB托盘标签列表
     *
     * @param query 查询条件
     * @return OTB托盘标签集合
     */
    default List<OtbPalletLabelPageVO> listByQuery(OtbPalletLabelQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取OTB托盘标签分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return OTB托盘标签集合
     */
    List<OtbPalletLabelPageVO> listByQuery(@Param("qo") OtbPalletLabelQuery query, @Param("page") Page<?> page);
}