package cn.need.cloud.biz.model.entity.binlocation;

import cn.need.framework.common.annotation.validation.Unique;
import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * 库位
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bin_location")
public class BinLocation extends SuperModel {


    /**
     * 库位名称
     */
    @TableField("location_name")
    @Unique("locationName is existed")
    private String locationName;

    /**
     * 行
     */
    @TableField("lrow")
    private String lrow;

    /**
     * 列
     */
    @TableField("ldepth")
    private String ldepth;

    /**
     * 层
     */
    @TableField("llevel")
    private String llevel;

    /**
     * 格
     */
    @TableField("lsplit")
    private String lsplit;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 是否有效 1：有效，0：无效
     */
    @TableField("active_flag")
    private Boolean activeFlag;

    /**
     * 唯一标识码
     */
    @TableField("ref_num")
    private String refNum;


    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 库位类型（用户可修改）
     */
    @TableField("bin_type")
    private String binType;

    /**
     * 库位上产品的一个状态  Empty   Pallet  散货
     */
    @TableField("bin_product_type")
    private String binProductType;

    /**
     * 库位类型（用户不可修改）
     */
    @TableField("type")
    private String type;

    /**
     * 仓库分区
     */
    @TableField("warehouse_zone_type")
    private String warehouseZoneType;

    /**
     * 是否默认
     */
    @TableField("default_flag")
    private Boolean defaultFlag;

}
