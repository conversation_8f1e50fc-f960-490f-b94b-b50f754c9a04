package cn.need.cloud.biz.model.entity.otc;

import cn.need.cloud.biz.model.entity.base.WorkorderDetailModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * <p>
 * OTC工单详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otc_workorder_detail")
public class OtcWorkorderDetail extends WorkorderDetailModel {


    @Serial
    private static final long serialVersionUID = 767254608978816329L;
    /**
     * 库存锁定id
     */
    @TableField("inventory_locked_id")
    private Long inventoryLockedId;

    /**
     * 打包为Package 数量
     */
    @TableField("packed_qty")
    private Integer packedQty;


    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    // /**
    //  * 产品id
    //  */
    // @TableField("product_id")
    // private Long productId;

    /**
     * 发货到c端工单id
     */
    @TableField("otc_workorder_id")
    private Long otcWorkorderId;

    // /**
    //  * 行序号
    //  */
    // @TableField("line_num")
    // private Integer lineNum;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    //
    // /**
    //  * 数量
    //  */
    // @TableField("qty")
    // private Integer qty;

    @TableField("finish_qty")
    private Integer finishQty;

    // /**
    //  * 拣货数量
    //  */
    // @TableField("picked_qty")
    // private Integer pickedQty;

    @TableField("ready_to_ship_qty")
    private Integer readyToShipQty;

    /**
     * 库存预定id
     */
    @TableField("inventory_reserve_id")
    private Long inventoryReserveId;

    /**
     * 预定数量
     */
    @TableField("reserve_qty")
    private Integer reserveQty;

    /**
     * 完成预定数量
     */
    @TableField("finish_reserve_qty")
    private Integer finishReserveQty;

}
