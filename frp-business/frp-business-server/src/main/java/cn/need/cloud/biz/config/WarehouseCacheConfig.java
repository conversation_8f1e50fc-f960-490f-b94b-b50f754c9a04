package cn.need.cloud.biz.config;


import cn.need.cloud.biz.cache.WarehouseCacheRepertory;
import cn.need.cloud.biz.cache.WarehouseCacheService;
import cn.need.cloud.biz.cache.impl.RedisWarehouseCacheRepertoryImpl;
import cn.need.cloud.biz.cache.impl.RedisWarehouseCacheServiceImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;


/**
 * 日志缓存配置
 *
 * <AUTHOR>
 */
@Configuration
public class WarehouseCacheConfig {

    /**
     * 注入日志配置缓存操作工具
     */
    @Bean
    @SuppressWarnings({"rawtypes", "unchecked"})
    public WarehouseCacheService warehouseCacheService(RedisTemplate redisTemplate) {
        return new RedisWarehouseCacheServiceImpl(redisTemplate);
    }

    /**
     * 注入日志配置缓存操作工具
     */
    @Bean
    @SuppressWarnings({"rawtypes", "unchecked"})
    public WarehouseCacheRepertory warehouseCacheRepertory(RedisTemplate redisTemplate) {
        return new RedisWarehouseCacheRepertoryImpl(redisTemplate);
    }
}
