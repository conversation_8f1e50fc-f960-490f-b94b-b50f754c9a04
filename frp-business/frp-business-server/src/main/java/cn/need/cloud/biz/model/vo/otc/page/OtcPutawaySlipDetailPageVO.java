package cn.need.cloud.biz.model.vo.otc.page;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.aware.BaseBinLocationAware;
import cn.need.cloud.biz.model.vo.base.aware.BaseProductVersionAware;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 上架详情 分页列表VO对象
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "上架详情 分页列表VO对象")
public class OtcPutawaySlipDetailPageVO extends BaseSuperVO implements BaseProductVersionAware, BaseBinLocationAware {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 库位详情id
     */
    @Schema(description = "库位详情id")
    private Long binLocationDetailId;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String putawaySlipStatus;

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id")
    private Long pickingSlipDetailId;

    /**
     * 拣货单详情id
     */
    @Schema(description = "拣货单详情id")
    private Long pickingSlipId;

    /**
     * 工单详情id
     */
    @Schema(description = "工单详情id")
    private Long workorderDetailId;

    /**
     * 工单id
     */
    @Schema(description = "工单id")
    private Long workorderId;

    /**
     * 产品新品id
     */
    @Schema(description = "产品新品id")
    private Long productId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productVersionId;

    /**
     * 上架数量
     */
    @Schema(description = "上架数量")
    private Integer putawayQty;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

}