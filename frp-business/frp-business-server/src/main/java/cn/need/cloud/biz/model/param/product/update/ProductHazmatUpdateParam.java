package cn.need.cloud.biz.model.param.product.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;


/**
 * UpdateParam对象
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@Schema(description = " UpdateParam对象")
public class ProductHazmatUpdateParam implements Serializable {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * packageInstruction
     */
    @Schema(description = "packageInstruction")
    @NotEmpty(message = "packageInstruction cannot be empty")
    @Size(max = 255, message = "packageInstruction cannot exceed 255 characters")
    private String packageInstruction;
    /**
     * 产品id
     */
    @Schema(description = "产品id")
    @NotNull(message = "productId cannot be null")
    private Long productId;
    /**
     * transportationRegulatoryClass
     */
    @Schema(description = "transportationRegulatoryClass")
    @NotEmpty(message = "transportationRegulatoryClass cannot be empty")
    @Size(max = 255, message = "transportationRegulatoryClass cannot exceed 255 characters")
    private String transportationRegulatoryClass;
    /**
     * unRegulatoryId
     */
    @Schema(description = "unRegulatoryId")
    @NotEmpty(message = "unRegulatoryId cannot be empty")
    @Size(max = 255, message = "unRegulatoryId cannot exceed 255 characters")
    private String unRegulatoryId;

}