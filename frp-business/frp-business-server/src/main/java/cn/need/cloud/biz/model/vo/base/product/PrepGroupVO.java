package cn.need.cloud.biz.model.vo.base.product;

import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/***
 * PrepGroupVO.java
 *
 * <AUTHOR>
 * @since 2024-12-02
 */
@Data
@Schema(description = "Prep Group产品 VO对象")
public class PrepGroupVO implements Serializable {

    /**
     * 父产品ID
     */
    @Schema(description = "父产品ID")
    private Long parentProductId;

    /**
     * 父产品
     */
    @Schema(description = "父产品")
    private BaseProductVO parentBaseProductVO;

    /**
     * 子产品ID
     */
    @Schema(description = "子产品ID")
    private Long childProductId;

    /**
     * 子产品
     */
    @Schema(description = "子产品")
    private BaseProductVO childBaseProductVO;

    /**
     * 指令备注
     */
    @Schema(description = "指令备注")
    private String instructionNote;

    /**
     * 回滚指令备注
     */
    @Schema(description = "回滚指令备注")
    private String revertInstructionNote;

    /**
     * 转换组类型
     */
    @Schema(description = "转换组类型")
    private String convertGroupType;

    /**
     * 同类产品版本号
     */
    @Schema(description = "同类产品版本号")
    private Integer groupVersionInt;

}
