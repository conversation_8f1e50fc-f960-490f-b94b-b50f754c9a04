package cn.need.cloud.biz.model.vo.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * 库位 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "基础库位 vo对象")
public class BaseBinLocationFullVO extends BaseBinLocationVO {

    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 行
     */
    @Schema(description = "行")
    private String lrow;

    /**
     * 列
     */
    @Schema(description = "列")
    private String ldepth;

    /**
     * 层
     */
    @Schema(description = "层")
    private String llevel;

    /**
     * 格
     */
    @Schema(description = "格")
    private String lsplit;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 是否有效
     */
    @Schema(description = "是否有效")
    private Boolean activeFlag;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 库位类型（用户可修改）
     */
    @Schema(description = "库位类型（用户可修改）")
    private String binType;

    /**
     * 库位上产品的一个状态  Empty   Pallet  散货
     */
    @Schema(description = "库位上产品的一个状态  Empty   Pallet  散货")
    private String binProductType;

    /**
     * 库位类型（用户不可修改）
     */
    @Schema(description = "库位类型（用户不可修改）")
    private String type;

    /**
     * 仓库分区
     */
    @Schema(description = "仓库分区")
    private String warehouseZoneType;

    /**
     * 是否默认
     */
    @Schema(description = "是否默认")
    private Boolean defaultFlag;

}