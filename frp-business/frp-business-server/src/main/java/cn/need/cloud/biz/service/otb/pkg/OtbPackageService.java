package cn.need.cloud.biz.service.otb.pkg;

import cn.need.cloud.biz.client.constant.enums.otb.OtbPackageStatusEnum;
import cn.need.cloud.biz.model.bo.otb.OtbBuildPalletContextBo;
import cn.need.cloud.biz.model.bo.otb.OtbBuildShipmentContextBO;
import cn.need.cloud.biz.model.entity.otb.OtbPackage;
import cn.need.cloud.biz.model.entity.otb.OtbPackageLabel;
import cn.need.cloud.biz.model.query.otb.pkg.OtbPackageQuery;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbPackageVO;
import cn.need.cloud.biz.model.vo.page.OtbPackagePageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * OTB包裹 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbPackageService extends SuperService<OtbPackage> {

    /**
     * 根据查询条件获取OTB包裹列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTB包裹对象的列表(分页)
     */
    List<OtbPackagePageVO> listByQuery(OtbPackageQuery query);

    /**
     * 根据查询条件获取OTB包裹列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTB包裹对象的列表(分页)
     */
    PageData<OtbPackagePageVO> pageByQuery(PageSearch<OtbPackageQuery> search);

    /**
     * 根据ID获取OTB包裹
     *
     * @param id OTB包裹ID
     * @return 返回OTB包裹VO对象
     */
    OtbPackageVO detailById(Long id);

    /**
     * 根据OTB包裹唯一编码获取OTB包裹
     *
     * @param refNum OTB包裹唯一编码
     * @return 返回OTB包裹VO对象
     */
    OtbPackageVO detailByRefNum(String refNum);

    /**
     * 通过sscc获取
     *
     * @param ssccnumList ssccNum集合
     * @return 包裹集合
     */
    List<OtbPackage> listBySsccNum(Collection<String> ssccnumList);

    /**
     * 更新包裹信息
     *
     * @param contextBo 上下文信息
     */
    void updatePackageInfo(OtbBuildPalletContextBo contextBo);

    /**
     * 根据打托单id获取打托单包裹
     *
     * @param id 打托单id
     * @return 打托单包裹
     */
    List<OtbPackageVO> listByOtbPalletId(Long id);

    /**
     * 根据打托单id获取打托单包裹
     *
     * @param otbPalletIdList 打托单id集合
     * @return 打托单包裹
     */
    List<OtbPackage> listByPalletIdList(List<Long> otbPalletIdList);

    /**
     * 根据打托单id获取打托单包裹
     *
     * @param id 打托单id
     * @return 打托单包裹
     */
    List<OtbPackageVO> listByShipmentId(Long id);

    /**
     * 根据打托单id获取打托单包裹
     *
     * @param id 打托单id
     * @return 打托单包裹
     */
    List<OtbPackageVO> listWithLabelByShipmentId(Long id);

    /**
     * 更新打印状态
     *
     * @param currentPrintStatus 当前打印状态
     * @param note               打印备注
     * @param otbPackageLabel    包裹标签
     */
    void markPrintedAfter(String currentPrintStatus, String note, OtbPackageLabel otbPackageLabel);

    /**
     * 第一次打印后操作
     *
     * @param otbPackageLabel 标签信息
     */
    void firstMarkSuccessPrintedAfter(OtbPackageLabel otbPackageLabel);

    /**
     * 检查包裹是否存在
     *
     * @param otbPackage 包裹
     * @return 是否存在
     */
    boolean exist(OtbPackage otbPackage);

    /**
     * 检查包裹是否存在
     *
     * @param id           包裹id
     * @param otbPackageId 包裹id
     * @return 是否存在
     */
    boolean existNotReadyToShip(Long id, Long otbPackageId);

    /**
     * 检查包裹是否存在
     *
     * @param context 上下文信息
     */
    void checkPackageList(OtbBuildShipmentContextBO context);

    /**
     * 检查包裹状态
     *
     * @param contextBo 打托上下文
     */
    void checkPackageStatus(OtbBuildPalletContextBo contextBo);

    /**
     * 根据ssccNum获取
     *
     * @param packageSsccNum ssccNum
     * @return 包裹
     */
    OtbPackageVO detailBySsccNum(String packageSsccNum);

    /**
     * 根据ssccNum获取
     *
     * @param packageSsccNum ssccNum
     * @return 包裹
     */
    OtbPackage getBySsccNum(String packageSsccNum);

    /**
     * 更新包裹信息
     *
     * @param otbPackageList 包裹集合
     */
    void updateBatchWithNull(List<OtbPackage> otbPackageList);

    /**
     * 更新包裹状态
     *
     * @param context 上下文信息
     */
    void updatePackageStatus(OtbBuildShipmentContextBO context);

    /**
     * 更新包裹状态
     *
     * @param otbPackageList 包裹集合
     * @param statusEnum     包裹状态
     */
    void updateStatusBatch(List<OtbPackage> otbPackageList, OtbPackageStatusEnum statusEnum);

    /**
     * 根据shipmentId获取
     *
     * @param shipmentIdList shipmentId集合
     * @return 包裹集合
     */
    List<OtbPackage> listByShipmentId(List<Long> shipmentIdList);

    /**
     * 根据workorderId获取
     *
     * @param workorderIds workorderId集合
     * @return 包裹集合
     */
    List<OtbPackage> listByWorkOrderIdList(List<Long> workorderIds);

    /**
     * 根据workorder和包裹id获取
     *
     * @param idList      包裹
     * @param workorderId 工单
     * @return /
     */
    List<OtbPackage> listByWorkorderIdAndIds(List<Long> idList, Long workorderId);
}