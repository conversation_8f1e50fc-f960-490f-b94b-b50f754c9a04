package cn.need.cloud.biz.service.fee;

import cn.need.cloud.biz.client.constant.enums.feeconfig.FeeModelTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.StringUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@AllArgsConstructor
public class FeeOriginalDataBuildFactory {

    private final Map<String, FeeOriginalDataBuildService> serviceMap;

    public FeeOriginalDataBuildService getBuilder(FeeModelTypeEnum type) {
        FeeOriginalDataBuildService service = serviceMap.get(StringUtil.format("fodBuild{}", type.getCode()));
        if (service == null) {
            throw new BusinessException("Unknown fee original data build type: " + type);
        }
        return service;
    }
}