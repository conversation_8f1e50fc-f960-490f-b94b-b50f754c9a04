package cn.need.cloud.biz.model.entity.base;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/1/8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WorkorderDetailModel extends BaseWorkorderDetailModel {

    /**
     * 行序号
     */
    @TableField("line_num")
    private Integer lineNum;

    /**
     * 预定数量
     */
    @TableField("reserve_qty")
    private Integer reserveQty;

    /**
     * 库存锁定id
     */
    @TableField("inventory_locked_id")
    private Long inventoryLockedId;

    /**
     * 库存预定id
     */
    @TableField("inventory_reserve_id")
    private Long inventoryReserveId;


    //
    // /**
    //  * 产品id
    //  */
    // @TableField("product_id")
    // private Long productId;


    // /**
    //  * 数量
    //  */
    // @TableField("qty")
    // private Integer qty;
    //
    // /**
    //  * 拣货数量
    //  */
    // @TableField("picked_qty")
    // private Integer pickedQty;
}
