package cn.need.cloud.biz.service.inventory;

import cn.need.cloud.biz.model.entity.base.BaseWorkorderDetailModel;
import cn.need.cloud.biz.model.entity.base.BaseWorkorderModel;
import cn.need.cloud.biz.model.entity.inventory.InventoryLocked;
import cn.need.cloud.biz.model.param.inventory.create.InventoryLockedCreateParam;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReleaseLockedParam;
import cn.need.cloud.biz.model.query.inventory.InventoryLockedQuery;
import cn.need.cloud.biz.model.vo.inventory.InventoryInventoryLockedVO;
import cn.need.cloud.biz.model.vo.inventory.InventoryLockedVO;
import cn.need.cloud.biz.model.vo.page.InventoryLockedPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface InventoryLockedService extends SuperService<InventoryLocked> {

    /**
     * 根据参数新增锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值
     *
     * @param createParam 请求创建参数，包含需要插入的锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值的相关信息
     * @return 锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    InventoryLocked insertByParam(InventoryLockedCreateParam createParam);


    /**
     * 根据查询条件获取锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值对象的列表(分页)
     */
    List<InventoryLockedPageVO> listByQuery(InventoryLockedQuery query);

    /**
     * 根据查询条件获取锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值对象的列表(分页)
     */
    PageData<InventoryLockedPageVO> pageByQuery(PageSearch<InventoryLockedQuery> search);

    /**
     * 根据ID获取锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值
     *
     * @param id 锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值ID
     * @return 返回锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值VO对象
     */
    InventoryLockedVO detailById(Long id);

    /**
     * 根据锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值唯一编码获取锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值
     *
     * @param refNum 锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值唯一编码
     * @return 返回锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值VO对象
     */
    InventoryLockedVO detailByRefNum(String refNum);


    /**
     * 根据产品ID列表查询最新的锁定库存记录。
     *
     * @param productIds 产品ID列表
     * @return 查询到的锁定库存记录列表
     */
    List<InventoryLockedVO> listLockeByProductIds(List<Long> productIds);

    /**
     * 根据产品ID列表查询最新的锁定库存记录。
     *
     * @param productIds 产品ID列表
     * @return 查询到的锁定库存记录列表
     */
    List<InventoryInventoryLockedVO> listInventoryLockeByProductIds(List<Long> productIds);

    /**
     * 根据产品ID列表查询最新的锁定库存记录。
     *
     * @param productIds 产品ID列表
     * @return 查询到的锁定库存记录列表
     */
    List<InventoryLocked> listEntityLockeByProductIds(List<Long> productIds);

    /**
     * 释放锁
     *
     * @param lockedInventoryList 库存锁id集合
     */
    void releaseLockedInventory(List<InventoryReleaseLockedParam> lockedInventoryList);

    /**
     * 根据工单详情和工单模型构建库存锁定对象
     *
     * @param <D>       工单详情模型类型，BaseWorkorderDetailModel
     * @param <W>       工单模型类型，必须是WorkorderModel的子类
     * @param detail    工单详情实例，提供工单详情信息以构建库存锁定
     * @param workOrder 工单模型实例，提供工单相关信息以构建库存锁定
     * @return InventoryLocked实例，根据提供的工单详情和工单模型构建而成
     */
    <D extends BaseWorkorderDetailModel, W extends BaseWorkorderModel> InventoryLocked buildInventoryLocked(D detail, W workOrder);
}