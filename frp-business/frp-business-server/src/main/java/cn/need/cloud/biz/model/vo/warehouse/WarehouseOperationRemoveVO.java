package cn.need.cloud.biz.model.vo.warehouse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Set;

/**
 * 仓库操作人删除 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "仓库操作人删除 vo对象")
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseOperationRemoveVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 操作人集合
     */
    private Set<Long> operationIdList;

    /**
     * 仓库id
     */
    private Long warehouseId;

}
