package cn.need.cloud.biz.service.otc.workorder;

import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorder;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorder;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorderDetail;
import cn.need.cloud.biz.model.query.otc.pickingslip.prep.OtcPrepPickingSlipFilterBuildQuery;
import cn.need.cloud.biz.model.query.otc.workorder.prep.OtcPrepWorkOrderListQuery;
import cn.need.cloud.biz.model.query.otc.workorder.prep.OtcPrepWorkorderQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPrepPickingSlipPickContextVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPrepPickingSlipPutAwayContextVO;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcPrepWorkorderVO;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcWorkorderFilterBuildPickingSlipCountVO;
import cn.need.cloud.biz.model.vo.page.OtcPrepWorkorderPageVO;
import cn.need.cloud.biz.service.base.RefNumService;
import cn.need.cloud.biz.service.inventory.PrepWorkorderService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * OTC预提工单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtcPrepWorkorderService extends
        SuperService<OtcPrepWorkorder>,
        RefNumService<OtcPrepWorkorder, OtcPrepWorkorderService>,
        PrepWorkorderService<OtcPrepWorkorder, OtcWorkorder, OtcWorkorderDetail> {

    /**
     * 根据查询条件获取OTC预提工单列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTC预提工单对象的列表(分页)
     */
    PageData<OtcPrepWorkorderPageVO> pageByQuery(PageSearch<OtcPrepWorkOrderListQuery> search);

    /**
     * 根据ID获取OTC预提工单
     *
     * @param id OTC预提工单ID
     * @return 返回OTC预提工单VO对象
     */
    OtcPrepWorkorderVO detailById(Long id);

    /**
     * 根据OTC预提工单唯一编码获取OTC预提工单
     *
     * @param refNum OTC预提工单唯一编码
     * @return 返回OTC预提工单VO对象
     */
    OtcPrepWorkorderVO detailByRefNum(String refNum);

    /**
     * 过滤条件统计总数
     *
     * @param search 查询条件
     * @return 统计VO
     */
    OtcWorkorderFilterBuildPickingSlipCountVO filterBuildPickingSlipCount(OtcPrepWorkOrderListQuery search);

    /**
     * 根据workOrderIdList查询数据
     *
     * @param workOrderIdList 工单集合
     * @return /
     */
    List<OtcPrepWorkorder> listByWorkOrderIdList(List<Long> workOrderIdList);

    /**
     * 根据Prep工单详情id获取MultiBox数据
     *
     * @param workOrderPrepDetailId Prep工单详情id
     * @return /
     */
    OtcPrepWorkorder multiBoxPrepByWorkOrderDetailId(Long workOrderPrepDetailId);

    /**
     * 工单FilterBuild查询
     *
     * @param query 查询条件
     * @return /
     */
    List<OtcPrepWorkorder> filterBuildByQuery(OtcPrepPickingSlipFilterBuildQuery query);

    /**
     * Prep工单拣货
     *
     * @param context 拣货上下文
     * @return /
     */
    boolean pick(OtcPrepPickingSlipPickContextVO context);

    /**
     * 根据拣货单id获取可PutAway的Prep工单集合
     *
     * @param otcPrepPickingSlipId Prep拣货单id
     * @return /
     */
    List<OtcPrepWorkorder> putAwayListByPrepPickingSlipId(Long otcPrepPickingSlipId);

    /**
     * 设置工单状态未Processed
     *
     * @param context 上下文
     */
    void updateAndSetWorkOrderProcessed(OtcPrepPickingSlipPutAwayContextVO context);

    /**
     * 根据预提拣货单id集合分组
     *
     * @param prepPickingSlipIdList 预提拣货单id集合
     * @return /
     */
    Map<Long, List<OtcPrepWorkorder>> groupByPrepPickingSlipIdList(List<Long> prepPickingSlipIdList);


    /**
     * OTC预工单字段去重下拉
     *
     * @param query 列表查询条件
     * @return 下拉
     */
    List<DropProVO> distinctValuePro(OtcPrepWorkorderQuery query);

    /**
     * 根据Prep拣货单id获取Prep工单集合
     *
     * @param prepPickingSlipId Prep拣货单id
     * @return /
     */
    List<Long> workorderIdListByPrepPickingSlipId(Long prepPickingSlipId);

    /**
     * 根据Prep拣货单id获取Prep工单集合
     *
     * @param prepPickingSlipId Prep拣货单id
     */
    List<OtcPrepWorkorder> listByPrepPickingSlipId(Long prepPickingSlipId);

    /**
     * 根据Prep拣货单id获取Prep工单集合
     *
     * @param prepPickingSlipIdList Prep拣货单
     * @return /
     */
    List<OtcPrepWorkorder> listByPrepPickingSlipIds(List<Long> prepPickingSlipIdList);

    /**
     * 根据工单详情id集合获取工单集合
     *
     * @param longs 工单详情id集合
     * @return /
     */
    List<OtcPrepWorkorder> listByWorkorderDetailIds(Collection<Long> longs);
}