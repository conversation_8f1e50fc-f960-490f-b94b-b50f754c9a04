package cn.need.cloud.biz.model.vo.product;

import cn.need.cloud.biz.model.vo.base.aware.BaseProductVersionAware;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 产品基础 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "产品基础 vo对象")
@AllArgsConstructor
@NoArgsConstructor
public class BaseProductVersionInfoVO implements BaseProductVersionAware {

    private Long productVersionId;

}
