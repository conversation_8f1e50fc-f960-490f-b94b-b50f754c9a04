package cn.need.cloud.biz.model.vo.otc.workorder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * Filter Build Picking Slip 构建拣货单统计VO
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@Schema(description = "OTC工单 Filter Build Picking Slip 统计VO对象")
public class OtcWorkorderFilterBuildPickingSlipCountVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7888689056676234847L;

    /**
     * Filter Sum
     */
    @Schema(description = "Filter Sum")
    private Long filterSum;
}
