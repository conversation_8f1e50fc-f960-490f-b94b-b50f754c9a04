package cn.need.cloud.biz.mapper.inventory;

import cn.need.cloud.biz.model.entity.inventory.InventoryAudit;
import cn.need.cloud.biz.model.query.inventory.InventoryAuditQuery;
import cn.need.cloud.biz.model.vo.page.InventoryAuditPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 库存盘点 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface InventoryAuditMapper extends SuperMapper<InventoryAudit> {

    /**
     * 根据条件获取库存盘点列表
     *
     * @param query 查询条件
     * @return 库存盘点集合
     */
    default List<InventoryAuditPageVO> listByQuery(InventoryAuditQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取库存盘点分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 库存盘点集合
     */
    List<InventoryAuditPageVO> listByQuery(@Param("qo") InventoryAuditQuery query, @Param("page") Page<?> page);
}