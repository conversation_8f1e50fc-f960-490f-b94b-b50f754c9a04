package cn.need.cloud.biz.model.query.otb.pallet;

import cn.need.cloud.biz.model.query.otb.pickingslip.OtbPickingSlipQuery;
import cn.need.cloud.biz.model.query.otb.pkg.OtbPackageQuery;
import cn.need.cloud.biz.model.query.otb.request.OtbRequestQuery;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkorderQuery;
import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Set;


/**
 * OTB托盘 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTB托盘 query对象")
public class OtbPalletQuery extends SuperQuery {

    /**
     * Serial Shipping Container Code，序列化货运容器代码
     */
    @Schema(description = "Serial Shipping Container Code，序列化货运容器代码集合")
    @Condition(value = Keyword.IN, fields = {"ssccNum"})
    private List<String> ssccNumList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private List<String> refNumList;

    /**
     * 工单refNum
     */
    @Schema(description = "工单refNum")
    private Set<String> workOrderRefNumList;

    /**
     * 工单id
     */
    @Schema(description = "工单id")
    private Set<Long> workOrderIdList;

    /**
     * 工单id
     */
    private Long otbWorkorderId;

    /**
     * 包裹ssccNum
     */
    @Schema(description = "工单id")
    private Set<String> packageSsccNumList;

    /**
     * 打托单id
     */
    private Set<Long> otbPalletIdList;

    /**
     * 请求单请求refNum
     */
    @Schema(description = "请求单请求refNum")
    private Set<String> requestOfRequestRefNumList;

    /**
     * 请求单id
     */
    @Schema(description = "请求单id")
    private Set<Long> requestIdList;

    /**
     * 短ssccNum
     */
    @Schema(description = "短ssccnum集合")
    @Condition(value = Keyword.IN, fields = {"shortSsccNum"})
    private List<String> shortSsccNumList;

    /**
     * otb 托盘状态
     */
    @Schema(description = "otb 托盘状态")
    private String otbPalletStatus;

    /**
     * otb 托盘状态
     */
    @Schema(description = "otb 托盘状态集合")
    @Condition(value = Keyword.IN, fields = {"otbPalletStatus"})
    private List<String> otbPalletStatusList;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private Set<String> orderNumList;

    /**
     * 空托盘配置id
     */
    @Schema(description = "空托盘配置id")
    private Long palletEmptyProfileId;

    /**
     * 包裹类型
     */
    @Schema(description = "包裹类型")
    private String packageType;

    @Schema(description = "流程类型")
    private String processType;

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id")
    private Long otbPickingSlipId;

    @Schema(description = "otb工单查询条件")
    private OtbWorkorderQuery otbWorkorderQuery;

    @Schema(description = "otb拣货单查询条件")
    private OtbPickingSlipQuery otbPickingSlipQuery;

    @Schema(description = "otb请求查询条件")
    private OtbRequestQuery otbRequestQuery;

    @Schema(description = "otb包裹查询条件")
    private OtbPackageQuery otbPackageQuery;


}