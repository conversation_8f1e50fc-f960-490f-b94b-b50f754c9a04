package cn.need.cloud.biz.model.vo.page;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 库位 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "库位 vo对象")
public class BinLocationPageVO extends BaseSuperVO {

    /**
     * 库位名称
     */
    @Schema(description = "库位名称")
    private String locationName;

    /**
     * 行
     */
    @Schema(description = "行")
    private String lrow;

    /**
     * 列
     */
    @Schema(description = "列")
    private String ldepth;

    /**
     * 层
     */
    @Schema(description = "层")
    private String llevel;

    /**
     * 格
     */
    @Schema(description = "格")
    private String lsplit;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 是否有效 1：有效，0：无效
     */
    @Schema(description = "是否有效 1：有效，0：无效")
    private Boolean activeFlag;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 库位类型（用户可修改）
     */
    @Schema(description = "库位类型（用户可修改）")
    private String binType;

    /**
     * 库位类型（用户不可修改）
     */
    @Schema(description = "库位类型（用户不可修改）")
    private String type;

    /**
     * 仓库分区
     */
    @Schema(description = "仓库分区")
    private String warehouseZoneType;

    /**
     * 是否默认
     */
    @Schema(description = "是否默认")
    private Boolean defaultFlag;


}