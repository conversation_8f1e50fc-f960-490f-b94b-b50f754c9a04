package cn.need.cloud.biz.service.helper.auditshowlog.otb;

import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.entity.otb.OtbPackage;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.biz.util.log.AuditLogUtil;

import java.util.List;

/**
 * 包裹日志工具
 *
 * <AUTHOR>
 * @since 2024/10/28
 */
public class OtbPackageAuditLogHelper {

    public static void recordLog(List<OtbPackage> otbPackageList, String status, String type, String note, String description) {
        otbPackageList.forEach(item -> recordLog(item, status, type, note, description));
    }

    public static void recordLog(List<OtbPackage> otbPackageList, String type, String note, String description) {
        otbPackageList.forEach(item -> recordLog(item, type, note, description));
    }


    public static void recordLog(List<OtbPackage> otbPackageList) {
        otbPackageList.forEach(OtbPackageAuditLogHelper::recordLog);
    }

    public static void recordLog(List<OtbPackage> otbPackageList, String description) {
        otbPackageList.forEach(item -> recordLog(item, description));
    }


    public static void recordLog(OtbPackage otbPackage) {
        recordLog(otbPackage, BaseTypeLogEnum.STATUS.getType(), null, null);
    }

    public static void recordLog(OtbPackage otbPackage, String description) {
        recordLog(otbPackage, BaseTypeLogEnum.STATUS.getType(), null, description);
    }

    public static void recordLog(OtbPackage otbPackage, String type, String note, String description) {
        recordLog(otbPackage, otbPackage.getOtbPackageStatus(), type, note, description);
    }


    public static void recordLog(OtbPackage otbPackage, String status, String type, String note, String description) {
        AuditShowLog auditShowLog = AuditLogUtil.commonLog(otbPackage)
                .with(AuditShowLog::setEvent, status)
                .with(AuditShowLog::setNote, note)
                .with(AuditShowLog::setDescription, description)
                .with(AuditShowLog::setType, type)
                .build();
        AuditLogHolder.record(auditShowLog);
    }


    public static void recordWithStatus(OtbPackage otbPackage, String status, String note) {
        AuditShowLog auditShowLog = AuditLogUtil.commonLog(otbPackage)
                .with(AuditShowLog::setEvent, status)
                .with(AuditShowLog::setNote, note)
                .build();
        AuditLogHolder.record(auditShowLog);
    }
}
