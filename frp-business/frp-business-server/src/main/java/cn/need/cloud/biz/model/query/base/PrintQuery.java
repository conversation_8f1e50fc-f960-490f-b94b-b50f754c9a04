package cn.need.cloud.biz.model.query.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 打印 query对象
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@Schema(description = "打印 query对象")
public class PrintQuery {

    /**
     * 打印对象id
     */
    @Schema(description = "打印对象id")
    @NotNull(message = "Print Object id is must not null")
    private Long id;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    @NotNull(message = "Print Status is must not null")
    private String printStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;
}
