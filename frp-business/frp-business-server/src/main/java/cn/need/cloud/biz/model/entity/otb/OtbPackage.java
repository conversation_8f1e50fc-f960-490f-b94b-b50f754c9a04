package cn.need.cloud.biz.model.entity.otb;

import cn.need.cloud.biz.model.entity.base.RefNumModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;


/**
 * <p>
 * OTB包裹
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otb_package")
public class OtbPackage extends RefNumModel {


    @Serial
    private static final long serialVersionUID = 7164029114889918742L;
    /**
     * Serial Shipping Container Code，序列化货运容器代码
     */
    @TableField("sscc_num")
    private String ssccNum;

    /**
     * 入库箱子-长
     */
    @TableField("carton_size_length")
    private BigDecimal cartonSizeLength;

    /**
     * 入库箱子-宽
     */
    @TableField("carton_size_width")
    private BigDecimal cartonSizeWidth;

    /**
     * 入库箱子-高
     */
    @TableField("carton_size_height")
    private BigDecimal cartonSizeHeight;

    /**
     * 入库箱子-重量
     */
    @TableField("carton_size_weight")
    private BigDecimal cartonSizeWeight;

    /**
     * 入库箱子-重量单位
     */
    @TableField("carton_size_weight_unit")
    private String cartonSizeWeightUnit;

    /**
     * 入库箱子-长度单位
     */
    @TableField("carton_size_dimension_unit")
    private String cartonSizeDimensionUnit;

    /**
     * 行序号
     */
    @TableField("line_num")
    private Integer lineNum;

    /**
     * otb请求id
     */
    @TableField("otb_request_id")
    private Long otbRequestId;

    /**
     * otb工单id
     */
    @TableField("otb_workorder_id")
    private Long otbWorkorderId;

    /**
     * otb装运id
     */
    @TableField("otb_shipment_id")
    private Long otbShipmentId;


    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * otb托盘id
     */
    @TableField("otb_pallet_id")
    private Long otbPalletId;

    /**
     * 订单号
     */
    @TableField("order_num")
    private String orderNum;

    /**
     * otb包裹状态
     */
    @TableField("otb_package_status")
    private String otbPackageStatus;

    /**
     * otb包裹类型
     */
    @TableField("otb_package_type")
    private String otbPackageType;

    /**
     * otb托盘类型
     */
    @TableField("otb_pallet_type")
    private String otbPalletType;

    /**
     * otb拣货单id
     */
    @TableField("otb_picking_slip_id")
    private Long otbPickingSlipId;

    /**
     * 发货地址1
     */
    @TableField("ship_from_address_addr1")
    private String shipFromAddressAddr1;

    /**
     * 发货地址2
     */
    @TableField("ship_from_address_addr2")
    private String shipFromAddressAddr2;

    /**
     * 发货地址3
     */
    @TableField("ship_from_address_addr3")
    private String shipFromAddressAddr3;

    /**
     * 发货地址城市
     */
    @TableField("ship_from_address_city")
    private String shipFromAddressCity;

    /**
     * 发货地址公司
     */
    @TableField("ship_from_address_company")
    private String shipFromAddressCompany;

    /**
     * 发货地址国家
     */
    @TableField("ship_from_address_country")
    private String shipFromAddressCountry;

    /**
     * 发货地址邮箱
     */
    @TableField("ship_from_address_email")
    private String shipFromAddressEmail;

    /**
     * 发货地址是否为住宅
     */
    @TableField("ship_from_address_is_residential")
    private Boolean shipFromAddressIsResidential;

    /**
     * 发货地址名称
     */
    @TableField("ship_from_address_name")
    private String shipFromAddressName;

    /**
     * 发货地址备注
     */
    @TableField("ship_from_address_note")
    private String shipFromAddressNote;

    /**
     * 发货地址电话
     */
    @TableField("ship_from_address_phone")
    private String shipFromAddressPhone;

    /**
     * 发货地址州/省
     */
    @TableField("ship_from_address_state")
    private String shipFromAddressState;

    /**
     * 发货地址邮编
     */
    @TableField("ship_from_address_zip_code")
    private String shipFromAddressZipCode;

    /**
     * 收货地址1
     */
    @TableField("ship_to_address_addr1")
    private String shipToAddressAddr1;

    /**
     * 收货地址2
     */
    @TableField("ship_to_address_addr2")
    private String shipToAddressAddr2;

    /**
     * 收货地址3
     */
    @TableField("ship_to_address_addr3")
    private String shipToAddressAddr3;

    /**
     * 收货地址城市
     */
    @TableField("ship_to_address_city")
    private String shipToAddressCity;

    /**
     * 收货地址公司
     */
    @TableField("ship_to_address_company")
    private String shipToAddressCompany;

    /**
     * 收货地址国家
     */
    @TableField("ship_to_address_country")
    private String shipToAddressCountry;

    /**
     * 收货地址邮箱
     */
    @TableField("ship_to_address_email")
    private String shipToAddressEmail;

    /**
     * 收货地址是否为住宅
     */
    @TableField("ship_to_address_is_residential")
    private Boolean shipToAddressIsResidential;

    /**
     * 收货地址名称
     */
    @TableField("ship_to_address_name")
    private String shipToAddressName;

    /**
     * 收货地址备注
     */
    @TableField("ship_to_address_note")
    private String shipToAddressNote;

    /**
     * 收货地址电话
     */
    @TableField("ship_to_address_phone")
    private String shipToAddressPhone;

    /**
     * 收货地址州/省
     */
    @TableField("ship_to_address_state")
    private String shipToAddressState;

    /**
     * 收货地址邮编
     */
    @TableField("ship_to_address_zip_code")
    private String shipToAddressZipCode;

    /**
     * 短ssccNum
     */
    @TableField("short_ssccnum")
    private String shortSsccNum;

    /**
     * 发货站
     */
    @TableField("station")
    private String station;

    /**
     * 运输公司
     */
    @TableField("ship_carrier")
    private String shipCarrier;

    /**
     * 运输方式
     */
    @TableField("ship_method")
    private String shipMethod;

    /**
     * 快递Api配置RefNum
     */
    @TableField("ship_api_profile_ref_num")
    private String shipApiProfileRefNum;

    /**
     * 快递号
     */
    @TableField("tracking_num")
    private String trackingNum;

    /**
     * 流程类型
     */
    @TableField
    private String processType;

    /**
     * 产品类型
     */
    @TableField("detail_product_type")
    private String detailProductType;

}
