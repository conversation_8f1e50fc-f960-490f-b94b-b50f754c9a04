package cn.need.cloud.biz.model.vo.page;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * OTC工单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTC工单 vo对象")
public class OtcWorkorderPageVO extends BaseSuperVO {

    @Serial
    private static final long serialVersionUID = 2869442421269908733L;
    @Schema(description = "主键")
    private Long id;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String detailProductType;

    /**
     * 请求快照RequestRefnum
     */
    @Schema(description = "请求快照RequestRefnum")
    private String requestSnapshotRequestRefNum;

    /**
     * 发货到c端拣货id
     */
    @Schema(description = "拣货单id")
    private Long otcPickingSlipId;

    /**
     * 拣货单refNum
     */
    @Schema(description = "拣货单 refNum")
    private RefNumVO pickingSlip;

    /**
     * 发货到c端工单状态
     */
    @Schema(description = "发货到c端工单状态")
    private String otcWorkorderStatus;

    /**
     * 预处理工单状态
     */
    @Schema(description = "预处理工单状态")
    private String workorderPrepStatus;

    /**
     * 请求快照订单类型
     */
    @Schema(description = "请求快照订单类型，orderType")
    private String requestSnapshotOrderType;

    /**
     * request快照渠道
     */
    @Schema(description = "request快照渠道, channel")
    private String requestSnapshotChannel;

    /**
     * 拣货到那里
     */
    @Schema(description = "拣货到那里")
    private String pickToStation;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    /**
     * 运输方式
     */
    @Schema(description = "运输方式")
    private String shipMethod;

    /**
     * 请求快照快递运输标志
     */
    @Schema(description = "请求快照快递运输标志, isShipExpress")
    private Boolean requestSnapshotShipExpressFlag;

    /**
     * 请求快照保险金货币
     */
    @Schema(description = "请求快照保险金货币, currency")
    private String requestSnapshotInsuranceAmountCurrency;

    /**
     * 请求快照保险金额
     */
    @Schema(description = "请求快照保险金额, amount")
    private BigDecimal requestSnapshotInsuranceAmountAmount;

    /**
     * 请求快照提供运输标签标志
     */
    @Schema(description = "请求快照提供运输标签标志, isProvideShippingLabel")
    private Boolean requestSnapshotProvideShippingLabelFlag;

    /**
     * 请求快照是否有特定运输要求
     */
    @Schema(description = "请求快照是否有特定运输要求,hasCusShipRequire")
    private Boolean requestSnapshotHasCusShipRequire;

    /**
     * 请求快照签名类型
     */
    @Schema(description = "请求快照签名类型, signatureType")
    private String requestSnapshotSignatureType;

    /**
     * request快照备注
     */
    @Schema(description = "request快照备注，requestNote")
    private String requestSnapshotNote;

    /**
     * 请求快照最后运输时间
     */
    @Schema(description = "请求快照最后运输时间，lastShipDate")
    private LocalDateTime requestSnapshotLastShipDate;

    /**
     * 请求快照供应商id
     */
    @Schema(description = "请求快照供应商id")
    private Long requestSnapshotTransactionPartnerId;

    /**
     * 构建运输打包类型
     */
    @Schema(description = "构建运输打包类型")
    private String buildShipPackageType;

    /**
     * 订单类型
     */
    @Schema(description = "订单类型")
    private String orderType;

    /**
     * 请求单id
     */
    @Schema(description = "请求单id")
    private Long otcRequestId;

    /**
     * 快递Api配置RefNum
     */
    @Schema(description = "快递Api配置RefNum")
    private String requestSnapshotShipApiProfileRefNum;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String workorderProductType;

    /**
     * 流程类型
     */
    @Schema(description = "流程类型")
    private String processType;

}