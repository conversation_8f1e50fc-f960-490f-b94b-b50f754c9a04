package cn.need.cloud.biz.model.vo.otc.pickingslip;

import cn.need.cloud.biz.model.entity.otc.OtcPickingSlip;
import cn.need.cloud.biz.model.query.otc.pickingslip.OtcPickingSlipProductPickQuery;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcWorkorderDetailPickVO;
import lombok.Data;

import java.util.List;

/***
 * 拣货上下文信息
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
@Data
public class OtcPickingSlipPickContextVO {

    /**
     * 拣货产品条件列表
     */
    List<OtcPickingSlipProductPickQuery> pickList;

    /**
     * 当前拣货单
     */
    OtcPickingSlip pickingSlip;

    /**
     * 拣货单详情 拣货后信息
     */
    List<OtcPickingSlipDetailPickVO> pickAfterDetailList;

    /**
     * 工单详情 拣货后信息
     */
    List<OtcWorkorderDetailPickVO> workOrderPickAfterDetailList;
}
