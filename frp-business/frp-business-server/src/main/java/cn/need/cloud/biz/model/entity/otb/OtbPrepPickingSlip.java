package cn.need.cloud.biz.model.entity.otb;

import cn.need.cloud.biz.client.constant.enums.base.ShipTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otb.PaperTypeEnum;
import cn.need.cloud.biz.model.entity.base.pickingslip.PrepPickingSlipModel;
import cn.need.cloud.dfs.client.constant.enums.FileDataTypeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * <p>
 * otb预拣货单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otb_prep_picking_slip")
public class OtbPrepPickingSlip extends PrepPickingSlipModel {


    @Serial
    private static final long serialVersionUID = -454788610633825513L;
    /**
     * otb预拣货单状态
     */
    @TableField("otb_prep_picking_slip_status")
    private String otbPrepPickingSlipStatus;

    /**
     * otb预拣货单类型
     */
    @TableField("otb_prep_picking_slip_type")
    private String otbPrepPickingSlipType;


    /**
     * 渠道要求的需要贴的产品标识码
     */
    @TableField("product_barcode")
    private String productBarcode;

    /**
     * 渠道要求的需要贴的产品标识SKU
     */
    @TableField("product_channel_sku")
    private String productChannelSku;

    /**
     * 产品类型
     */
    @TableField("detail_product_type")
    private String detailProductType;

    /**
     * 发货类型
     */
    @TableField("ship_type")
    private ShipTypeEnum shipType;

    /**
     * RI文件数据
     */
    @TableField("routing_instruction_file_file_data")
    private String routingInstructionFileFileData;

    /**
     * RI文件扩展名
     */
    @TableField("routing_instruction_file_file_extension")
    private String routingInstructionFileFileExtension;

    /**
     * RI文件类型
     */
    @TableField("routing_instruction_file_file_type")
    private FileDataTypeEnum routingInstructionFileFileType;

    /**
     * RI文件纸张类型
     */
    @TableField("routing_instruction_file_paper_type")
    private PaperTypeEnum routingInstructionFilePaperType;

}
