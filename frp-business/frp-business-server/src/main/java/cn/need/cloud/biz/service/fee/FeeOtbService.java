package cn.need.cloud.biz.service.fee;


import cn.need.cloud.biz.model.entity.fee.FeeOtb;
import cn.need.cloud.biz.model.param.fee.create.FeeOtbCreateParam;
import cn.need.cloud.biz.model.param.fee.update.FeeOtbUpdateParam;
import cn.need.cloud.biz.model.query.fee.FeeOtbQuery;
import cn.need.cloud.biz.model.vo.fee.FeeOtbVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeOtbPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 费用otb service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public interface FeeOtbService extends SuperService<FeeOtb>,
        FeeService<FeeOtb, FeeOtbService> {
    /**
     * 根据参数新增费用otb
     *
     * @param createParam 请求创建参数，包含需要插入的费用otb的相关信息
     * @return 费用otbID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(FeeOtbCreateParam createParam);


    /**
     * 根据参数更新费用otb
     *
     * @param updateParam 请求创建参数，包含需要更新的费用otb的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(FeeOtbUpdateParam updateParam);

    /**
     * 根据查询条件获取费用otb列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个费用otb对象的列表(分页)
     */
    List<FeeOtbPageVO> listByQuery(FeeOtbQuery query);

    /**
     * 根据查询条件获取费用otb列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个费用otb对象的列表(分页)
     */
    PageData<FeeOtbPageVO> pageByQuery(PageSearch<FeeOtbQuery> search);

    /**
     * 根据ID获取费用otb
     *
     * @param id 费用otbID
     * @return 返回费用otbVO对象
     */
    FeeOtbVO detailById(Long id);

    /**
     * 根据费用otb唯一编码获取费用otb
     *
     * @param refNum 费用otb唯一编码
     * @return 返回费用otbVO对象
     */
    FeeOtbVO detailByRefNum(String refNum);


}