package cn.need.cloud.biz.model.vo.inbound.request;

import cn.need.cloud.biz.model.entity.base.RefNumModel;
import cn.need.cloud.biz.model.vo.base.BasePartnerVO;
import cn.need.cloud.biz.model.vo.base.BaseWarehouseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 入库请求 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "入库请求 vo对象")
public class InboundRequestVO extends RefNumModel {


    /**
     * 运输方式类型
     */
    @Schema(description = "运输方式类型")
    private String transportMethodType;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码")
    private String requestRefNum;

    /**
     * 预计到达日期
     */
    @Schema(description = "预计到达日期")
    private LocalDateTime estimateArrivalDate;

    /**
     * 实际到达日期
     */
    @Schema(description = "实际到达日期")
    private LocalDateTime actualArrivalDate;

    /**
     * 快递号
     */
    @Schema(description = "快递号")
    private String trackingNum;

    /**
     * 发件人姓名
     */
    @Schema(description = "发件人姓名")
    private String fromAddressName;

    /**
     * 发件人公司
     */
    @Schema(description = "发件人公司")
    private String fromAddressCompany;

    /**
     * 发件人国家
     */
    @Schema(description = "发件人国家")
    private String fromAddressCountry;

    /**
     * 发件人州
     */
    @Schema(description = "发件人州")
    private String fromAddressState;

    /**
     * 发件人城市
     */
    @Schema(description = "发件人城市")
    private String fromAddressCity;

    /**
     * 发件人邮编
     */
    @Schema(description = "发件人邮编")
    private String fromAddressZipCode;

    /**
     * 发件人地址1
     */
    @Schema(description = "发件人地址1")
    private String fromAddressAddr1;

    /**
     * 发件人地址2
     */
    @Schema(description = "发件人地址2")
    private String fromAddressAddr2;

    /**
     * 发件人地址3
     */
    @Schema(description = "发件人地址3")
    private String fromAddressAddr3;

    /**
     * 发件人邮箱
     */
    @Schema(description = "发件人邮箱")
    private String fromAddressEmail;

    /**
     * 发件人电话
     */
    @Schema(description = "发件人电话")
    private String fromAddressPhone;

    /**
     * 发件人备注
     */
    @Schema(description = "发件人备注")
    private String fromAddressNote;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 交易伙伴ID
     */
    @Schema(description = "交易伙伴ID")
    private Long transactionPartnerId;

    /**
     * 交易伙伴
     */
    @Schema(description = "交易伙伴")
    private BasePartnerVO transactionPartner;

    /**
     * 请求状态
     */
    @Schema(description = "请求状态")
    private String inboundRequestStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 容器类型
     */
    @Schema(description = "容器类型")
    private String containerType;

    /**
     * 发件人地址是否为住宅
     */
    @Schema(description = "发件人地址是否为住宅")
    private Boolean fromAddressIsResidential;

    /**
     * 入库请求单详情
     */
    @Schema(description = "入库请求单详情")
    private List<InboundRequestDetailVO> details;

    /**
     * 仓库基本信息
     */
    @Schema(description = "仓库基本信息")
    private BaseWarehouseVO baseWarehouseVO;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String productType;

    /**
     * 处理开始时间
     */
    @Schema(description = "处理开始时间")
    private LocalDateTime processStartTime;

    /**
     * 处理完成时间
     */
    @Schema(description = "处理完成时间")
    private LocalDateTime processEndTime;

    /**
     * 计费状态
     */
    @Schema(description = "计费状态")
    private String feeStatus;
}