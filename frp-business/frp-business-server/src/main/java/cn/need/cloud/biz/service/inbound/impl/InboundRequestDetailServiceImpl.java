package cn.need.cloud.biz.service.inbound.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.inbound.InboundRequestDetailConverter;
import cn.need.cloud.biz.mapper.inbound.InboundRequestDetailMapper;
import cn.need.cloud.biz.model.entity.inbound.InboundRequestDetail;
import cn.need.cloud.biz.model.query.inbound.InboundRequestDetailQuery;
import cn.need.cloud.biz.model.vo.inbound.request.InboundRequestDetailVO;
import cn.need.cloud.biz.model.vo.page.InboundRequestDetailPageVO;
import cn.need.cloud.biz.service.inbound.InboundRequestDetailService;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 入库请求详情服务实现类
 * </p>
 * <p>
 * 该服务主要处理入库请求的详情信息，包括：
 * 1. 查询和分页查询入库请求详情
 * 2. 根据请求ID获取详情列表
 * 3. 更新、删除和检查详情记录
 * </p>
 * <p>
 * 入库请求详情包含了入库产品的具体信息，是入库请求的核心内容
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class InboundRequestDetailServiceImpl extends SuperServiceImpl<InboundRequestDetailMapper, InboundRequestDetail> implements InboundRequestDetailService {

    /**
     * 根据查询条件获取入库请求详情列表
     * <p>
     * 该方法用于按照指定查询条件获取入库请求详情列表
     * </p>
     *
     * @param query 查询条件
     * @return 入库请求详情页面视图对象列表
     * <p>
     */
    @Override
    public List<InboundRequestDetailPageVO> listByQuery(InboundRequestDetailQuery query) {
        return mapper.listByQuery(query);
    }

    /**
     * 分页查询入库请求详情
     * <p>
     * 该方法按照指定查询条件和分页参数查询入库请求详情列表
     * </p>
     *
     * @param search 包含查询条件和分页参数的搜索对象
     * @return 包含分页信息的入库请求详情页面视图对象数据
     */
    @Override
    public PageData<InboundRequestDetailPageVO> pageByQuery(PageSearch<InboundRequestDetailQuery> search) {
        Page<InboundRequestDetail> page = Conditions.page(search, entityClass);
        List<InboundRequestDetailPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取入库请求详情
     * <p>
     * 该方法用于获取指定ID的入库请求详情，如果找不到对应记录则抛出异常
     * </p>
     *
     * @param id 入库请求详情ID
     * @return 入库请求详情视图对象
     * @throws BusinessException 如果找不到对应ID的记录，则抛出业务异常
     */
    @Override
    public InboundRequestDetailVO detailById(Long id) {
        InboundRequestDetail entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in InboundRequestDetail");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "InboundRequestDetail", id));
        }
        return buildInboundRequestDetailVO(entity);
    }

    /**
     * 根据入库请求ID获取详情列表
     * <p>
     * 该方法用于获取指定入库请求的所有详情记录
     * </p>
     *
     * @param inboundRequestId 入库请求ID
     * @return 入库请求详情视图对象列表
     */
    @Override
    public List<InboundRequestDetailVO> listByInboundRequestId(Long inboundRequestId) {
        List<InboundRequestDetail> list = lambdaQuery().eq(InboundRequestDetail::getInboundRequestId, inboundRequestId).list();
        return Converters.get(InboundRequestDetailConverter.class).toVO(list);
    }

    /**
     * 检查产品版本是否存在于入库请求中
     * <p>
     * 该方法用于检查指定产品版本是否被任何入库请求使用，
     * 常用于验证产品版本是否可以删除或修改
     * </p>
     *
     * @param productVersionId 产品版本ID
     * @return 如果产品版本被任何入库请求使用，则返回true；否则返回false
     * <p>
     * 优化建议：重命名为isProductVersionInUse更简洁明了
     */
    @Override
    public boolean existInboundRequestByProductVersionId(Long productVersionId) {
        return lambdaQuery().eq(InboundRequestDetail::getProductVersionId, productVersionId).count() > 0;
    }

    /**
     * 更新入库请求详情
     * <p>
     * 该方法用于批量更新入库请求详情，支持新增、更新和删除操作：
     * 1. 对于已存在的详情记录（ID匹配）进行更新
     * 2. 对于新的详情记录进行新增
     * 3. 对于不再存在的详情记录进行删除
     * </p>
     *
     * @param inboundRequestDetailList 入库请求详情列表
     * @param requestId                入库请求ID
     * @param warehouseId              仓库ID
     * @throws BusinessException 如果入库请求详情列表为空或请求详情不存在，则抛出业务异常
     */
    @Override
    public void updateDetail(List<InboundRequestDetail> inboundRequestDetailList, Long requestId, Long warehouseId) {
        Validate.notEmpty(inboundRequestDetailList, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "product details"));
        //填充请求单id
        inboundRequestDetailList.forEach(item -> item.setInboundRequestId(requestId));
        //获取当前存在请求详情
        List<InboundRequestDetailVO> requestDetailList = listByInboundRequestId(requestId);
        Validate.notEmpty(requestDetailList, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "request details"));
        //获取入库详情id
        List<Long> existIdList = requestDetailList.stream().map(InboundRequestDetailVO::getId).collect(Collectors.toList());
        //入库请求单更新容器
        List<InboundRequestDetail> insertList = Lists.arrayList();
        List<InboundRequestDetail> updateList = Lists.arrayList();
        //遍历入库请求详情
        inboundRequestDetailList.forEach(item -> {
            // 填充仓库id
            // item.setWarehouseId(warehouseId);
            //若详情行数据已存在则做更新
            if (existIdList.contains(item.getId())) {
                updateList.add(item);
                existIdList.remove(item.getId());
                return;
            }
            //若不存在则做新增
            insertList.add(item);
        });
        //新增入库请求详情
        super.insertBatch(insertList);
        //更新入库请求详情
        super.updateBatch(updateList);
        //删除入库请求详情
        super.removeByIds(existIdList);
    }

    /**
     * 根据请求ID删除详情记录
     * <p>
     * 该方法用于逻辑删除指定入库请求关联的所有详情记录，
     * 通过设置删除标志位而非物理删除，便于数据追踪和恢复
     * </p>
     *
     * @param id 入库请求ID
     */
    @Override
    public void removeByRequestId(Long id) {
        lambdaUpdate()
                .eq(InboundRequestDetail::getInboundRequestId, id)
                .set(InboundRequestDetail::getRemoveFlag, DataState.ENABLED)
                .update();
    }

    @Override
    public List<InboundRequestDetail> list(Long id, List<Integer> lineNumList) {
        return lambdaQuery()
                .eq(InboundRequestDetail::getInboundRequestId, id)
                .in(InboundRequestDetail::getLineNum, lineNumList)
                .list();
    }

    /**
     * 构建入库请求详情视图对象
     * <p>
     * 该方法用于将入库请求详情实体对象转换为视图对象，
     * 如果实体对象为空，则返回null
     * </p>
     *
     * @param entity 入库请求详情实体对象
     * @return 入库请求详情视图对象，如果实体为空则返回null
     */
    private InboundRequestDetailVO buildInboundRequestDetailVO(InboundRequestDetail entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的入库请求详情VO对象
        return Converters.get(InboundRequestDetailConverter.class).toVO(entity);
    }
}
