package cn.need.cloud.biz.service.helper.pickingslip;

import cn.need.cloud.biz.cache.bean.ProductVersionCache;
import cn.need.cloud.biz.model.bo.base.pickingslip.PickingSlipUnpickBO;
import cn.need.cloud.biz.model.entity.base.putawayslip.PutawaySlipDetailModel;
import cn.need.cloud.biz.model.entity.base.putawayslip.PutawaySlipModel;
import cn.need.cloud.biz.model.vo.base.pickingslip.BasePickingSlipUnpickDetailVO;
import cn.need.cloud.biz.util.ProductVersionCacheUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * PickingSlipHelper
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public class PickingSlipHelper {


    /**
     * 校验可回滚数量
     *
     * @param query             unpick参数
     * @param currentUnpickList unpick列表
     */
    public static <T extends PutawaySlipModel, R extends PutawaySlipDetailModel,
            PSUD extends BasePickingSlipUnpickDetailVO> void checkUnpickCanRollback(PickingSlipUnpickBO<T, R, PSUD> query,
                                                                                    List<PSUD> currentUnpickList) {
        Map<Long, PSUD> canUnpickMap = StreamUtils.toMap(currentUnpickList, BasePickingSlipUnpickDetailVO::getUnpickId);
        query.getDetailList()
                .forEach(unpickParam -> {
                    PSUD unpick = canUnpickMap.get(unpickParam.getWorkorderBinLocationId());
                    // 存在且数量大于等于回滚数量
                    boolean canRollback = ObjectUtil.isNotNull(unpick)
                            && unpick.getCanRollbackQty() >= unpickParam.getRollbackQty();
                    Validate.isTrue(canRollback,
                            "{} {} is not enough, [CanRollbackQty: {}] < [RollbackQty: {}]",
                            query.getWorkorder().refNumLog(),
                            Optional.ofNullable(unpick)
                                    .map(PSUD::getProductVersionId)
                                    .map(ProductVersionCacheUtil::getById)
                                    .map(ProductVersionCache::toLog)
                                    .orElse(StringPool.EMPTY),
                            Optional.ofNullable(unpick)
                                    .map(PSUD::getCanRollbackQty)
                                    .orElse(0),
                            unpickParam.getRollbackQty()
                    );
                });
    }
}
