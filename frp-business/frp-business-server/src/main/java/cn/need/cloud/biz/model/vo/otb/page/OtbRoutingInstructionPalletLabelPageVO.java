package cn.need.cloud.biz.model.vo.otb.page;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * otb发货指南托盘标签 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "otb发货指南托盘标签 vo对象")
public class OtbRoutingInstructionPalletLabelPageVO implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 路由指令ID
     */
    @Schema(description = "路由指令ID")
    private Long otbRoutingInstructionId;

    /**
     * 托盘SSCC编号
     */
    @Schema(description = "托盘SSCC编号")
    private String palletSsccNum;

    /**
     * 面单类型
     */
    @Schema(description = "面单类型")
    private String labelType;

    /**
     * label RefNum
     */
    @Schema(description = "label RefNum")
    private String labelRefNum;

    /**
     * 纸张类型
     */
    @Schema(description = "纸张类型")
    private String paperType;

    /**
     * 数据类型
     */
    @Schema(description = "数据类型")
    private String rawDataType;

    /**
     * 文件系统数据类型
     */
    @Schema(description = "文件系统数据类型")
    private String fileIdRawDataType;

    /**
     * label数据类型
     */
    @Schema(description = "label数据类型")
    private String labelRawData;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

}