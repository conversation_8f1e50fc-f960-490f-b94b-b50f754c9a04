package cn.need.cloud.biz.model.query.otb.pickingslip.prep;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * OTC拣货单 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTB拣货单pick query对象")
public class OtbPrepPickingSlipPickQuery {

    /**
     * 拣货单id
     */
    @Schema(description = "Prep拣货单id")
    @NotNull(message = "PrepPickingSlipId id is must not null")
    private Long otbPrepPickingSlipId;


    /**
     * 拣货详情参数
     */
    @Schema(description = "Prep拣货详情参数")
    @NotEmpty(message = "pick detail not null")
    private List<OtbPrepPickingSlipProductPickQuery> pickDetailList;

}