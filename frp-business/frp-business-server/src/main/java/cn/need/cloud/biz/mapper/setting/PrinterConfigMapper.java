package cn.need.cloud.biz.mapper.setting;

import cn.need.cloud.biz.model.entity.setting.PrinterConfig;
import cn.need.cloud.biz.model.query.setting.PrinterConfigQuery;
import cn.need.cloud.biz.model.vo.page.PrinterConfigPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 打印配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface PrinterConfigMapper extends SuperMapper<PrinterConfig> {

    /**
     * 根据条件获取打印配置列表
     *
     * @param query 查询条件
     * @return 打印配置集合
     */
    default List<PrinterConfigPageVO> listByQuery(PrinterConfigQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取打印配置分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 打印配置集合
     */
    List<PrinterConfigPageVO> listByQuery(@Param("qo") PrinterConfigQuery query, @Param("page") Page<?> page);
}