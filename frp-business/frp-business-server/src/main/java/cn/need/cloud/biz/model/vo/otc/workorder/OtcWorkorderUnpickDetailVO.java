package cn.need.cloud.biz.model.vo.otc.workorder;

import cn.need.cloud.biz.model.vo.base.aware.BaseProductAware;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * OTC工单详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "OTC工单详情 Unpick vo对象")
public class OtcWorkorderUnpickDetailVO implements Serializable, BaseProductAware {

    /**
     * id
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 行
     */
    @Schema(description = "行")
    private Integer lineNum;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 打包为Package 数量
     */
    @Schema(description = "打包为Package 数量")
    private Integer packedQty;

    /**
     * 拣货 数量
     */
    @Schema(description = "拣货 数量")
    private Integer pickedQty;

    /**
     * 发货 数量
     */
    @Schema(description = "发货 数量")
    private Integer readyToShipQty;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;
}