package cn.need.cloud.biz.model.vo.otb.pickingslip;

import cn.need.cloud.biz.model.vo.base.RefNumVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


/**
 * OTB拣货单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTB拣货单 PrintSummary 统计结果 vo对象")
public class OtbPickingSlipSummaryPrintVO {

    /**
     * 打印数量
     */
    @Schema(description = "打印数量")
    private Integer count;

    /**
     * 汇总列表
     */
    @Schema(description = "汇总列表")
    private List<OtbPickingSlipSummaryVO> summaryList;
    /**
     * OTB拣货单集合
     */
    @Schema(description = "OTB拣货单集合")
    private List<RefNumVO> otbPickingSlipList;

}