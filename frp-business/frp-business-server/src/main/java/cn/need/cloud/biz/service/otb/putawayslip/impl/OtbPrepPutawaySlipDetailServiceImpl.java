package cn.need.cloud.biz.service.otb.putawayslip.impl;

import cn.need.cloud.biz.mapper.otb.OtbPrepPutawaySlipDetailMapper;
import cn.need.cloud.biz.model.entity.base.putawayslip.PrepPutawaySlipDetailModel;
import cn.need.cloud.biz.model.entity.otb.OtbPrepPutawaySlipDetail;
import cn.need.cloud.biz.service.otb.putawayslip.OtbPrepPutawaySlipDetailService;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import io.jsonwebtoken.lang.Collections;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 上架详情 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Service
public class OtbPrepPutawaySlipDetailServiceImpl extends SuperServiceImpl<OtbPrepPutawaySlipDetailMapper, OtbPrepPutawaySlipDetail>
        implements OtbPrepPutawaySlipDetailService {
    @Override
    public List<OtbPrepPutawaySlipDetail> listAvailableByWorkorderIds(Collection<Long> workorderIds) {
        return mapper.listAvailableByWorkorderIds(workorderIds);
    }

    @Override
    public Map<Long, List<OtbPrepPutawaySlipDetail>> groupByPrepPutawaySlipId(List<Long> prepPutawaySlipIds) {
        if (ObjectUtil.isEmpty(prepPutawaySlipIds)) {
            return Collections.emptyMap();
        }
        return lambdaQuery()
                .in(PrepPutawaySlipDetailModel::getPrepPickingSlipId, prepPutawaySlipIds)
                .list()
                .stream()
                .collect(Collectors.groupingBy(PrepPutawaySlipDetailModel::getPrepPickingSlipId));
    }
}
