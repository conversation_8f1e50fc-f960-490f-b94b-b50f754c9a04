package cn.need.cloud.biz.service.fee;

import cn.need.cloud.biz.model.bo.fee.inbound.FodExtraDataInBoundBO;

/**
 * <AUTHOR>
 * @since 2025/4/9
 */


public interface FeeOriginalDataBuildService {


    FodExtraDataInBoundBO buildFeeOriginalData();

    /**
     * 根据请求id构建费用原始数据
     *
     * @param requestId 请求id
     * @return 返回费用原始数据
     */
    FodExtraDataInBoundBO buildFeeOriginalData(Long requestId);
}
