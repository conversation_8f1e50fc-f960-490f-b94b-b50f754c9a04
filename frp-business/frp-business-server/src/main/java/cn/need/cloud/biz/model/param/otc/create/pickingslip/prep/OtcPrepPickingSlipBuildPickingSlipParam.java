package cn.need.cloud.biz.model.param.otc.create.pickingslip.prep;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * OTC预提货单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC预提货单构建拣货单 param对象")
public class OtcPrepPickingSlipBuildPickingSlipParam implements Serializable {

    /**
     * Prep拣货id
     */
    @Schema(description = "Prep拣货id")
    @NotNull(message = "PrepPickingSlip id is must not null")
    private Long otcPrepPickingSlipId;
}