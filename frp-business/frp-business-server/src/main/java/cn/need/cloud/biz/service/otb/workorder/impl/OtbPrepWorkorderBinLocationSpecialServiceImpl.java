package cn.need.cloud.biz.service.otb.workorder.impl;

import cn.need.cloud.biz.model.bo.otb.putawayslip.OtbPrepPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.bo.otb.putawayslip.OtbPrepPutawaySlipPutAwayDetailBO;
import cn.need.cloud.biz.model.entity.otb.OtbPrepPutawaySlipDetail;
import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorderBinLocation;
import cn.need.cloud.biz.service.otb.workorder.OtbPrepWorkorderBinLocationService;
import cn.need.cloud.biz.service.otb.workorder.OtbPrepWorkorderBinLocationSpecialService;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.model.IdModel;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * OTC工单仓储位置 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@AllArgsConstructor
@Service
public class OtbPrepWorkorderBinLocationSpecialServiceImpl implements OtbPrepWorkorderBinLocationSpecialService {

    private final OtbPrepWorkorderBinLocationService otbWorkorderBinLocationService;

    @Override
    public void rollback(OtbPrepPutawaySlipPutAwayBO param) {
        List<Long> wkBinLocationIds = param.getDetailList().stream()
                .map(OtbPrepPutawaySlipPutAwayDetailBO::getPutawaySlipDetail)
                .map(OtbPrepPutawaySlipDetail::getPrepWorkorderBinLocationId)
                .distinct()
                .toList();

        // workorder_bin_location 需要扣减数量
        List<OtbPrepWorkorderBinLocation> workorderBinLocations = otbWorkorderBinLocationService.listByIds(wkBinLocationIds);
        Map<Long, OtbPrepWorkorderBinLocation> wkBinLocationMap = StreamUtils.toMap(workorderBinLocations, IdModel::getId);

        // Rollback
        List<OtbPrepWorkorderBinLocation> rollbackList = param.getDetailList().stream()
                .map(obj -> {
                    OtbPrepPutawaySlipDetail putawaySlipDetail = obj.getPutawaySlipDetail();
                    OtbPrepWorkorderBinLocation workorderBinLocation = wkBinLocationMap.get(putawaySlipDetail.getPrepWorkorderBinLocationId());
                    workorderBinLocation.setQty(workorderBinLocation.getQty() - obj.getPutawayQty());
                    // 扣减至0直接删除
                    boolean needRemove = workorderBinLocation.getQty() == 0;
                    workorderBinLocation.setDeletedNote(needRemove ? param.getNote() : null);
                    workorderBinLocation.setRemoveFlag(needRemove ? 1 : 0);

                    // 绑定
                    obj.setReadyToGoLockedId(workorderBinLocation.getBinLocationDetailLockedId());
                    return workorderBinLocation;
                })
                .toList();

        List<OtbPrepWorkorderBinLocation> updateList = rollbackList.stream().filter(obj -> obj.getRemoveFlag() == 0).toList();
        Validate.isTrue(otbWorkorderBinLocationService.updateBatch(updateList) == updateList.size(),
                "Update WorkOrderBinLocation qty is fail"
        );

        List<Long> removeIds = rollbackList.stream().filter(obj -> obj.getRemoveFlag() == 1).map(IdModel::getId).toList();
        Validate.isTrue(otbWorkorderBinLocationService.removeByIds(removeIds) == removeIds.size(),
                "Delete WorkOrderBinLocation qty is fail"
        );
    }
}