package cn.need.cloud.biz.model.entity.feeconfig;

import cn.need.cloud.biz.model.entity.base.RefNumModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;


/**
 * <p>
 * 仓库报价费用配置otb详情
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("fee_config_otb_detail")
public class FeeConfigOtbDetail extends RefNumModel {


    @Serial
    private static final long serialVersionUID = -443497243821997786L;
    /**
     * 基础价格
     */
    @TableField("base_fee")
    private BigDecimal baseFee;

    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 计费起始阈值（实际数量超过该值时触发计费）
     */
    @TableField("fee_start_threshold")
    private BigDecimal feeStartThreshold;

    /**
     * 计费单位类型
     */
    @TableField("fee_unit_type")
    private String feeUnitType;

    /**
     * header表id
     */
    @TableField("header_id")
    private Long headerId;

    /**
     * 行序号
     */
    @TableField("line_num")
    private Integer lineNum;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 唯一标识码
     */
    @TableField("ref_num")
    private String refNum;

    /**
     * 结束（不包含）根据condition_type得到值来判断
     */
    @TableField("section_end")
    private Long sectionEnd;

    /**
     * 开始（包含）根据condition_type得到值来判断
     */
    @TableField("section_start")
    private Long sectionStart;

    /**
     * 单价
     */
    @TableField("unit_fee")
    private BigDecimal unitFee;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

}
