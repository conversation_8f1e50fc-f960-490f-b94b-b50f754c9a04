package cn.need.cloud.biz.service.fee.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.fee.FeeInboundConverter;
import cn.need.cloud.biz.mapper.fee.FeeInboundMapper;
import cn.need.cloud.biz.model.entity.fee.FeeInbound;
import cn.need.cloud.biz.model.param.fee.create.FeeInboundCreateParam;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.Serializable;
import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 费用inbound service 实现类测试
 */
@ExtendWith(MockitoExtension.class)
public class FeeInboundServiceImplTest {

    @InjectMocks
    private FeeInboundServiceImpl feeInboundService;

    @Mock
    private FeeInboundMapper feeInboundMapper;

    @Mock
    private FeeInboundConverter feeInboundConverter;

    private FeeInboundCreateParam createParam;
    private FeeInbound feeInbound;

    @BeforeEach
    public void setUp() {
        // 准备测试数据
        createParam = new FeeInboundCreateParam();
        createParam.setCurrency("USD");
        createParam.setFeeOriginalDataId(1L);
        createParam.setNote("Test note");
        createParam.setRefNum("TEST-REF-NUM");
        createParam.setSnapshotRefNum("SNAPSHOT-REF-NUM");
        createParam.setSnapshotRequestId(2L);
        createParam.setSnapshotRequestRefNum("SNAPSHOT-REQUEST-REF-NUM");
        createParam.setTotalFee(new BigDecimal("100.00"));
        createParam.setTransactionPartnerId(3L);
        createParam.setWarehouseId(4L);

        // 准备实体对象
        feeInbound = new FeeInbound();
        feeInbound.setCurrency("USD");
        feeInbound.setFeeOriginalDataId(1L);
        feeInbound.setNote("Test note");
        feeInbound.setSnapshotRefNum("SNAPSHOT-REF-NUM");
        feeInbound.setSnapshotRequestId(2L);
        feeInbound.setSnapshotRequestRefNum("SNAPSHOT-REQUEST-REF-NUM");
        feeInbound.setTotalFee(new BigDecimal("100.00"));
        feeInbound.setTransactionPartnerId(3L);
        feeInbound.setWarehouseId(4L);

        // 设置mapper字段
        ReflectionTestUtils.setField(feeInboundService, "mapper", feeInboundMapper);
    }

    /**
     * 测试正常情况下的insertByParam方法
     */
    @Test
    public void testInsertByParam_Success() {
        // 模拟Converters.get方法
        try (MockedStatic<Converters> convertersMockedStatic = Mockito.mockStatic(Converters.class);
             MockedStatic<IdWorker> idWorkerMockedStatic = Mockito.mockStatic(IdWorker.class);
             MockedStatic<FormatUtil> formatUtilMockedStatic = Mockito.mockStatic(FormatUtil.class)) {

            // 设置静态方法的行为
            convertersMockedStatic.when(() -> Converters.get(FeeInboundConverter.class)).thenReturn(feeInboundConverter);
            idWorkerMockedStatic.when(IdWorker::getId).thenReturn(123456L);
            formatUtilMockedStatic.when(() -> FormatUtil.generateRefNum(RefNumTypeEnum.FEE_INBOUND)).thenReturn("FEE-INBOUND-123456");

            // 设置转换器的行为
            when(feeInboundConverter.toEntity(createParam)).thenReturn(feeInbound);

            // 设置mapper的行为
            when(feeInboundMapper.insert(any(FeeInbound.class))).thenReturn(1);

            // 执行测试方法
            Long result = feeInboundService.insertByParam(createParam);

            // 验证结果
            assertEquals(123456L, result);
            assertEquals(123456L, feeInbound.getId());
            assertEquals("FEE-INBOUND-123456", feeInbound.getRefNum());

            // 验证方法调用
            verify(feeInboundConverter, times(1)).toEntity(createParam);
            verify(feeInboundMapper, times(1)).insert(feeInbound);
        }
    }

    /**
     * 测试参数为空的情况
     */
    @Test
    public void testInsertByParam_NullParam() {
        // 执行测试方法并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            feeInboundService.insertByParam(null);
        });

        // 验证异常消息
        assertEquals(ErrorMessages.PARAMETER_EMPTY, exception.getMessage());

        // 验证方法未被调用
        verify(feeInboundConverter, never()).toEntity((Serializable) any());
        verify(feeInboundMapper, never()).insert(any());
    }

    /**
     * 测试转换器返回空实体的情况
     */
    @Test
    public void testInsertByParam_NullEntity() {
        // 模拟Converters.get方法
        try (MockedStatic<Converters> convertersMockedStatic = Mockito.mockStatic(Converters.class)) {
            // 设置静态方法的行为
            convertersMockedStatic.when(() -> Converters.get(FeeInboundConverter.class)).thenReturn(feeInboundConverter);

            // 设置转换器返回null
            when(feeInboundConverter.toEntity(createParam)).thenReturn(null);

            // 执行测试方法并验证异常
            assertThrows(NullPointerException.class, () -> {
                feeInboundService.insertByParam(createParam);
            });

            // 验证方法调用
            verify(feeInboundConverter, times(1)).toEntity(createParam);
            verify(feeInboundMapper, never()).insert(any());
        }
    }

    /**
     * 测试插入失败的情况
     */
    @Test
    public void testInsertByParam_InsertFailed() {
        // 模拟Converters.get方法
        try (MockedStatic<Converters> convertersMockedStatic = Mockito.mockStatic(Converters.class);
             MockedStatic<IdWorker> idWorkerMockedStatic = Mockito.mockStatic(IdWorker.class);
             MockedStatic<FormatUtil> formatUtilMockedStatic = Mockito.mockStatic(FormatUtil.class)) {

            // 设置静态方法的行为
            convertersMockedStatic.when(() -> Converters.get(FeeInboundConverter.class)).thenReturn(feeInboundConverter);
            idWorkerMockedStatic.when(IdWorker::getId).thenReturn(123456L);
            formatUtilMockedStatic.when(() -> FormatUtil.generateRefNum(RefNumTypeEnum.FEE_INBOUND)).thenReturn("FEE-INBOUND-123456");

            // 设置转换器的行为
            when(feeInboundConverter.toEntity(createParam)).thenReturn(feeInbound);

            // 设置mapper的行为 - 插入失败
            when(feeInboundMapper.insert(any(FeeInbound.class))).thenReturn(0);

            // 执行测试方法
            Long result = feeInboundService.insertByParam(createParam);

            // 验证结果
            assertEquals(123456L, result);
            assertEquals(123456L, feeInbound.getId());
            assertEquals("FEE-INBOUND-123456", feeInbound.getRefNum());

            // 验证方法调用
            verify(feeInboundConverter, times(1)).toEntity(createParam);
            verify(feeInboundMapper, times(1)).insert(feeInbound);
        }
    }
}
