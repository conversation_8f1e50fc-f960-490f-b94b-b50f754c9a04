-- 添加 ship_type 字段
ALTER TABLE `otb_workorder`
    ADD COLUMN `ship_type` VARCHAR(64) NULL DEFAULT NULL COMMENT '发货类型';


-- 添加 routing_instruction_file_file_data 字段
ALTER TABLE `otb_workorder`
    ADD COLUMN `routing_instruction_file_file_data` TEXT NULL COMMENT 'RI文件数据';


-- 添加 routing_instruction_file_file_extension 字段
ALTER TABLE `otb_workorder`
    ADD COLUMN `routing_instruction_file_file_extension` VARCHAR(64) NULL DEFAULT NULL COMMENT 'RI文件扩展名';


-- 添加 routing_instruction_file_file_type 字段
ALTER TABLE `otb_workorder`
    ADD COLUMN `routing_instruction_file_file_type` VARCHAR(64) NULL DEFAULT NULL COMMENT 'RI文件类型';


-- 添加 routing_instruction_file_paper_type 字段
ALTER TABLE `otb_workorder`
    ADD COLUMN `routing_instruction_file_paper_type` VARCHAR(64) NULL DEFAULT NULL COMMENT 'RI文件纸张类型';


-- 添加 ship_type 字段
ALTER TABLE `otb_prep_workorder`
    ADD COLUMN `ship_type` VARCHAR(64) NULL DEFAULT NULL COMMENT '发货类型';


-- 添加 routing_instruction_file_file_data 字段
ALTER TABLE `otb_prep_workorder`
    ADD COLUMN `routing_instruction_file_file_data` TEXT NULL COMMENT 'RI文件数据';


-- 添加 routing_instruction_file_file_extension 字段
ALTER TABLE `otb_prep_workorder`
    ADD COLUMN `routing_instruction_file_file_extension` VARCHAR(64) NULL DEFAULT NULL COMMENT 'RI文件扩展名';


-- 添加 routing_instruction_file_file_type 字段
ALTER TABLE `otb_prep_workorder`
    ADD COLUMN `routing_instruction_file_file_type` VARCHAR(64) NULL DEFAULT NULL COMMENT 'RI文件类型';


-- 添加 routing_instruction_file_paper_type 字段
ALTER TABLE `otb_prep_workorder`
    ADD COLUMN `routing_instruction_file_paper_type` VARCHAR(64) NULL DEFAULT NULL COMMENT 'RI文件纸张类型';


-- 添加 ship_type 字段
ALTER TABLE `otb_picking_slip`
    ADD COLUMN `ship_type` VARCHAR(64) NULL DEFAULT NULL COMMENT '发货类型';


-- 添加 routing_instruction_file_file_data 字段
ALTER TABLE `otb_picking_slip`
    ADD COLUMN `routing_instruction_file_file_data` TEXT NULL COMMENT 'RI文件数据';


-- 添加 routing_instruction_file_file_extension 字段
ALTER TABLE `otb_picking_slip`
    ADD COLUMN `routing_instruction_file_file_extension` VARCHAR(64) NULL DEFAULT NULL COMMENT 'RI文件扩展名';


-- 添加 routing_instruction_file_file_type 字段
ALTER TABLE `otb_picking_slip`
    ADD COLUMN `routing_instruction_file_file_type` VARCHAR(64) NULL DEFAULT NULL COMMENT 'RI文件类型';


-- 添加 routing_instruction_file_paper_type 字段
ALTER TABLE `otb_picking_slip`
    ADD COLUMN `routing_instruction_file_paper_type` VARCHAR(64) NULL DEFAULT NULL COMMENT 'RI文件纸张类型';


-- 添加 ship_type 字段
ALTER TABLE `otb_prep_picking_slip`
    ADD COLUMN `ship_type` VARCHAR(64) NULL DEFAULT NULL COMMENT '发货类型';


-- 添加 routing_instruction_file_file_data 字段
ALTER TABLE `otb_prep_picking_slip`
    ADD COLUMN `routing_instruction_file_file_data` TEXT NULL COMMENT 'RI文件数据';


-- 添加 routing_instruction_file_file_extension 字段
ALTER TABLE `otb_prep_picking_slip`
    ADD COLUMN `routing_instruction_file_file_extension` VARCHAR(64) NULL DEFAULT NULL COMMENT 'RI文件扩展名';


-- 添加 routing_instruction_file_file_type 字段
ALTER TABLE `otb_prep_picking_slip`
    ADD COLUMN `routing_instruction_file_file_type` VARCHAR(64) NULL DEFAULT NULL COMMENT 'RI文件类型';


-- 添加 routing_instruction_file_paper_type 字段
ALTER TABLE `otb_prep_picking_slip`
    ADD COLUMN `routing_instruction_file_paper_type` VARCHAR(64) NULL DEFAULT NULL COMMENT 'RI文件纸张类型';