package cn.need.framework.starter.security.constant;


/**
 * 状态 相关常量
 *
 * <AUTHOR>
 * @since 2021/4/19
 */
public interface SecurityConstants {

    /**
     * 强制下线再次操作状态返回编码
     */
    Integer FORCE_OFFLINE_STATUS_CODE = 4408;

    /**
     * 其它设备登录再次操作状态返回编码
     */
    Integer SQUEEZE_OUT_STATUS_CODE = 4409;

    /**
     * 角色权限缓存前缀
     */
    String CACHE_ROLE_PERMISSIONS_PREFIX = "ROLE:PERMISSIONS:CACHE:";

}
