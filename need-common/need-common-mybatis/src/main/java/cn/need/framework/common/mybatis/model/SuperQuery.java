package cn.need.framework.common.mybatis.model;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Query对象承载公共字段的超类，该类用来定义数据的创建人、创建时间等数据。
 *
 * <AUTHOR>
 * @since 2022/7/14

 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SuperQuery implements Serializable {

    private static final long serialVersionUID = 8628352349152808902L;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 最后更新人名称
     */
    private String updateByName;

    /**
     * 创建时间起
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间至
     */
    private LocalDateTime createTimeEnd;

    /**
     * 最后更新时间起
     */
    private LocalDateTime updateTimeStart;

    /**
     * 最后更新时间至
     */
    private LocalDateTime updateTimeEnd;

    /**
     * distinct 查询字段
     */
    private List<String> columnNameList;

    /**
     * 仓库id
     */
    private List<Long> warehouseIdList;

    /**
     * 时区
     */
    private String timeZone;

    // region id

    /**
     * 主键
     */
    private Long id;

    /**
     * 主键
     */
    @Condition(value = Keyword.IN, fields = {"id"})
    private List<Long> idList;

    /**
     * 主键
     */
    @Condition(value = Keyword.NOT_IN, fields = {"id"})
    private List<Long> idNiList;

    /**
     * 主键
     */
    private List<String> idValueTypeList;

    /**
     * 主键
     */
    @Condition(value = Keyword.GT, fields = {"id"})
    private Long idGt;

    /**
     * 主键
     */
    @Condition(value = Keyword.GE, fields = {"id"})
    private Long idGe;

    /**
     * 主键
     */
    @Condition(value = Keyword.LT, fields = {"id"})
    private Long idLt;

    /**
     * 主键
     */
    @Condition(value = Keyword.LE, fields = {"id"})
    private Long idLe;

    /**
     * 主键
     */
    @Condition(value = Keyword.LIKE, fields = {"id"})
    private Long idLike;

    /**
     * 主键
     */
    @Condition(value = Keyword.LIKE_LEFT, fields = {"id"})
    private Long idLikeLeft;

    /**
     * 主键
     */
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"id"})
    private Long idLikeRight;

    // endregion id

    // region createBy

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建人
     */
    @Condition(value = Keyword.IN, fields = {"createBy"})
    private List<Long> createByList;

    /**
     * 创建人
     */
    @Condition(value = Keyword.NOT_IN, fields = {"createBy"})
    private List<Long> createByNiList;

    /**
     * 创建人
     */
    private List<String> createByValueTypeList;

    /**
     * 创建人
     */
    @Condition(value = Keyword.GT, fields = {"createBy"})
    private Long createByGt;

    /**
     * 创建人
     */
    @Condition(value = Keyword.GE, fields = {"createBy"})
    private Long createByGe;

    /**
     * 创建人
     */
    @Condition(value = Keyword.LT, fields = {"createBy"})
    private Long createByLt;

    /**
     * 创建人
     */
    @Condition(value = Keyword.LE, fields = {"createBy"})
    private Long createByLe;

    /**
     * 创建人
     */
    @Condition(value = Keyword.LIKE, fields = {"createBy"})
    private Long createByLike;

    /**
     * 创建人
     */
    @Condition(value = Keyword.LIKE_LEFT, fields = {"createBy"})
    private Long createByLikeLeft;

    /**
     * 创建人
     */
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"createBy"})
    private Long createByLikeRight;

    // endregion createBy

    // region createTime

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建时间
     */
    @Condition(value = Keyword.IN, fields = {"createTime"})
    private List<LocalDateTime> createTimeList;

    /**
     * 创建时间
     */
    @Condition(value = Keyword.NOT_IN, fields = {"createTime"})
    private List<LocalDateTime> createTimeNiList;

    /**
     * 创建时间
     */
    private List<String> createTimeValueTypeList;

    /**
     * 创建时间
     */
    @Condition(value = Keyword.GT, fields = {"createTime"})
    private LocalDateTime createTimeGt;

    /**
     * 创建时间
     */
    @Condition(value = Keyword.GE, fields = {"createTime"})
    private LocalDateTime createTimeGe;

    /**
     * 创建时间
     */
    @Condition(value = Keyword.LT, fields = {"createTime"})
    private LocalDateTime createTimeLt;

    /**
     * 创建时间
     */
    @Condition(value = Keyword.LE, fields = {"createTime"})
    private LocalDateTime createTimeLe;

    /**
     * 创建时间
     */
    @Condition(value = Keyword.LIKE, fields = {"createTime"})
    private LocalDateTime createTimeLike;

    /**
     * 创建时间
     */
    @Condition(value = Keyword.LIKE_LEFT, fields = {"createTime"})
    private LocalDateTime createTimeLikeLeft;

    /**
     * 创建时间
     */
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"createTime"})
    private LocalDateTime createTimeLikeRight;

    // endregion createTime

    // region updateBy

    /**
     * 最后更新人
     */
    private Long updateBy;

    /**
     * 最后更新人
     */
    @Condition(value = Keyword.IN, fields = {"updateBy"})
    private List<Long> updateByList;

    /**
     * 最后更新人
     */
    @Condition(value = Keyword.NOT_IN, fields = {"updateBy"})
    private List<Long> updateByNiList;

    /**
     * 最后更新人
     */
    private List<String> updateByValueTypeList;

    /**
     * 最后更新人
     */
    @Condition(value = Keyword.GT, fields = {"updateBy"})
    private Long updateByGt;

    /**
     * 最后更新人
     */
    @Condition(value = Keyword.GE, fields = {"updateBy"})
    private Long updateByGe;

    /**
     * 最后更新人
     */
    @Condition(value = Keyword.LT, fields = {"updateBy"})
    private Long updateByLt;

    /**
     * 最后更新人
     */
    @Condition(value = Keyword.LE, fields = {"updateBy"})
    private Long updateByLe;

    /**
     * 最后更新人
     */
    @Condition(value = Keyword.LIKE, fields = {"updateBy"})
    private Long updateByLike;

    /**
     * 最后更新人
     */
    @Condition(value = Keyword.LIKE_LEFT, fields = {"updateBy"})
    private Long updateByLikeLeft;

    /**
     * 最后更新人
     */
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"updateBy"})
    private Long updateByLikeRight;

    // endregion updateBy

    // region updateTime

    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最后更新时间
     */
    @Condition(value = Keyword.IN, fields = {"updateTime"})
    private List<LocalDateTime> updateTimeList;

    /**
     * 最后更新时间
     */
    @Condition(value = Keyword.NOT_IN, fields = {"updateTime"})
    private List<LocalDateTime> updateTimeNiList;

    /**
     * 最后更新时间
     */
    private List<String> updateTimeValueTypeList;

    /**
     * 最后更新时间
     */
    @Condition(value = Keyword.GT, fields = {"updateTime"})
    private LocalDateTime updateTimeGt;

    /**
     * 最后更新时间
     */
    @Condition(value = Keyword.GE, fields = {"updateTime"})
    private LocalDateTime updateTimeGe;

    /**
     * 最后更新时间
     */
    @Condition(value = Keyword.LT, fields = {"updateTime"})
    private LocalDateTime updateTimeLt;

    /**
     * 最后更新时间
     */
    @Condition(value = Keyword.LE, fields = {"updateTime"})
    private LocalDateTime updateTimeLe;

    /**
     * 最后更新时间
     */
    @Condition(value = Keyword.LIKE, fields = {"updateTime"})
    private LocalDateTime updateTimeLike;

    /**
     * 最后更新时间
     */
    @Condition(value = Keyword.LIKE_LEFT, fields = {"updateTime"})
    private LocalDateTime updateTimeLikeLeft;

    /**
     * 最后更新时间
     */
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"updateTime"})
    private LocalDateTime updateTimeLikeRight;

    // endregion updateTime

    // region removeFlag

    /**
     * 逻辑删除标记(0-有效，1-已删除)
     */
    private Boolean removeFlag;

    /**
     * 逻辑删除标记(0-有效，1-已删除)
     */
    @Condition(value = Keyword.IN, fields = {"removeFlag"})
    private List<Boolean> removeFlagList;

    /**
     * 逻辑删除标记(0-有效，1-已删除)
     */
    @Condition(value = Keyword.NOT_IN, fields = {"removeFlag"})
    private List<Boolean> removeFlagNiList;

    /**
     * 逻辑删除标记(0-有效，1-已删除)
     */
    private List<String> removeFlagValueTypeList;

    /**
     * 逻辑删除标记(0-有效，1-已删除)
     */
    @Condition(value = Keyword.GT, fields = {"removeFlag"})
    private Boolean removeFlagGt;

    /**
     * 逻辑删除标记(0-有效，1-已删除)
     */
    @Condition(value = Keyword.GE, fields = {"removeFlag"})
    private Boolean removeFlagGe;

    /**
     * 逻辑删除标记(0-有效，1-已删除)
     */
    @Condition(value = Keyword.LT, fields = {"removeFlag"})
    private Boolean removeFlagLt;

    /**
     * 逻辑删除标记(0-有效，1-已删除)
     */
    @Condition(value = Keyword.LE, fields = {"removeFlag"})
    private Boolean removeFlagLe;

    /**
     * 逻辑删除标记(0-有效，1-已删除)
     */
    @Condition(value = Keyword.LIKE, fields = {"removeFlag"})
    private Boolean removeFlagLike;

    /**
     * 逻辑删除标记(0-有效，1-已删除)
     */
    @Condition(value = Keyword.LIKE_LEFT, fields = {"removeFlag"})
    private Boolean removeFlagLikeLeft;

    /**
     * 逻辑删除标记(0-有效，1-已删除)
     */
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"removeFlag"})
    private Boolean removeFlagLikeRight;

    // endregion removeFlag

    // region version

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 乐观锁版本号
     */
    @Condition(value = Keyword.IN, fields = {"version"})
    private List<Long> versionList;

    /**
     * 乐观锁版本号
     */
    @Condition(value = Keyword.NOT_IN, fields = {"version"})
    private List<Long> versionNiList;

    /**
     * 乐观锁版本号
     */
    private List<String> versionValueTypeList;

    /**
     * 乐观锁版本号
     */
    @Condition(value = Keyword.GT, fields = {"version"})
    private Long versionGt;

    /**
     * 乐观锁版本号
     */
    @Condition(value = Keyword.GE, fields = {"version"})
    private Long versionGe;

    /**
     * 乐观锁版本号
     */
    @Condition(value = Keyword.LT, fields = {"version"})
    private Long versionLt;

    /**
     * 乐观锁版本号
     */
    @Condition(value = Keyword.LE, fields = {"version"})
    private Long versionLe;

    /**
     * 乐观锁版本号
     */
    @Condition(value = Keyword.LIKE, fields = {"version"})
    private Long versionLike;

    /**
     * 乐观锁版本号
     */
    @Condition(value = Keyword.LIKE_LEFT, fields = {"version"})
    private Long versionLikeLeft;

    /**
     * 乐观锁版本号
     */
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"version"})
    private Long versionLikeRight;

    // endregion version

    // region tenantId

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户id
     */
    @Condition(value = Keyword.IN, fields = {"tenantId"})
    private List<Long> tenantIdList;

    /**
     * 租户id
     */
    @Condition(value = Keyword.NOT_IN, fields = {"tenantId"})
    private List<Long> tenantIdNiList;

    /**
     * 租户id
     */
    private List<String> tenantIdValueTypeList;

    /**
     * 租户id
     */
    @Condition(value = Keyword.GT, fields = {"tenantId"})
    private Long tenantIdGt;

    /**
     * 租户id
     */
    @Condition(value = Keyword.GE, fields = {"tenantId"})
    private Long tenantIdGe;

    /**
     * 租户id
     */
    @Condition(value = Keyword.LT, fields = {"tenantId"})
    private Long tenantIdLt;

    /**
     * 租户id
     */
    @Condition(value = Keyword.LE, fields = {"tenantId"})
    private Long tenantIdLe;

    /**
     * 租户id
     */
    @Condition(value = Keyword.LIKE, fields = {"tenantId"})
    private Long tenantIdLike;

    /**
     * 租户id
     */
    @Condition(value = Keyword.LIKE_LEFT, fields = {"tenantId"})
    private Long tenantIdLikeLeft;

    /**
     * 租户id
     */
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"tenantId"})
    private Long tenantIdLikeRight;

    // endregion tenantId

}
