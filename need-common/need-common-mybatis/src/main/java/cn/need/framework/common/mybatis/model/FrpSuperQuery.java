package cn.need.framework.common.mybatis.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.util.List;
import java.util.Set;

/**
 * Query对象承载公共字段的超类，该类用来定义数据的创建人、创建时间等数据。
 *
 * <AUTHOR>
 * @since 2022/7/14
 */
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@Data
@NoArgsConstructor
public class FrpSuperQuery extends SuperQuery {

    @Serial
    private static final long serialVersionUID = 1159915972302891854L;

    private List<FrpBaseQuery<Object>> andConditions;

    private List<FrpBaseQuery<Object>> orConditions;

    @JsonIgnore
    private Set<String> availableFields;

}
