package ${package.Mapper};

import java.util.List;
import ${queryPackage}.${queryName};
import ${package.Entity}.${entity};
import ${pageVoPackage}.${pageVoName};
import org.apache.ibatis.annotations.Param;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
#if(${superMapperClassPackage})
import ${package.Entity}.${entity};
import ${superMapperClassPackage};
#end
#if(${mapperAnnotationClass})
import ${mapperAnnotationClass.name};
#end
#set($alias = "")
#set($tableNameLower = $table.name.toLowerCase())
#set($parts = $tableNameLower.split("_"))
#foreach($part in $parts)
    #if($part.length() > 0)
        #set($alias = $alias + $part.substring(0,1))
    #end
#end
#set($tableAlias = $alias)
/**
 * <p>
 * $!{table.comment} Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
#if(${mapperAnnotationClass})
@${mapperAnnotationClass.simpleName}
#end
#if(${superMapperClass})
public interface ${table.mapperName} extends ${superMapperClass}<${entity}> {
#else
public interface ${table.mapperName} {
#end

    /**
    *  根据条件获取$!{table.comment}列表
    *
    * @param query 查询条件
    * @return $!{table.comment}集合
    */
    default List<${entity}PageVO> listByQuery (@Param("qo$tableAlias") ${entity}Query query){
        return listByQuery(query,null);
    }

    /**
      *  根据条件获取$!{table.comment}分页列表
      *
      * @param query 查询条件
      * @param page 分页
      * @return $!{table.comment}集合
      */
    List<${entity}PageVO> listByQuery (
            @Param("qo$tableAlias") ${entity}Query query,
            @Param("page") Page <?> page);

    /**
     *  根据条件获取$!{table.comment}列表
     *
     * @param query 查询条件
     * @return $!{table.comment}集合
     */
    default List<${entity}PageVO> listByQueryPro (@Param("qo$tableAlias") ${entity}Query query){
        return listByQuery(query,null);
    }

    /**
     *  根据条件获取$!{table.comment}分页列表
     *
     * @param query 查询条件
     * @param page 分页
     * @return $!{table.comment}集合
     */
    List<${entity}PageVO> listByQueryPro (
            @Param("qo$tableAlias") ${entity}Query query,
            @Param("page") Page <?> page);

    /**
     * $!{table.comment}下拉列表
     *
     * @param columnList 查询字段名
     * @param query 查询条件
     * @return $!{table.comment}下拉列表
     */
    List<Map<String, Object>> dropProList(
        @Param("columnList") List<DropColumnInfoBO> columnList,
        @Param("qo$tableAlias") ${entity}Query query
    );
}