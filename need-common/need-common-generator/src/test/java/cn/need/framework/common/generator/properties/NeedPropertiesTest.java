package cn.need.framework.common.generator.properties;

import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.ArrayUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.ValidateException;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.generator.properties.gc.GlobalProperties;
import cn.need.framework.common.generator.properties.sc.StrategyProperties;
import cn.need.framework.common.generator.properties.sc.StrategyServiceImplProperties;
import cn.need.framework.common.generator.properties.sc.StrategyServiceProperties;
import cn.need.framework.common.generator.properties.tp.TemplateJavaProperties;
import cn.need.framework.common.generator.properties.tp.TemplateProperties;
import com.baomidou.mybatisplus.generator.config.TemplateType;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * NeedProperties Tester.
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
class NeedPropertiesTest {

    @Test
    public void testNeedProperties() {
        NeedProperties properties1 = new NeedProperties();
        log.info("----- init properties1 -----");
        log.info(JsonUtil.toJson(properties1));
        assertNull(properties1.tp().java().getEntity());
        assertNull(properties1.tp().getXml());
        assertEquals(StringPool.NULL, ArrayUtil.toString(properties1.tp().getDisable()));
        assertNull(properties1.gc().getAuthor());
        assertFalse(properties1.gc().isSwagger());
        assertTrue(properties1.gc().isSpringdoc());
        assertNull(properties1.gc().getDateType());
        assertLinesMatch(Lists.arrayList(), properties1.sc().getInclude());
        assertLinesMatch(Lists.arrayList(), properties1.sc().getExclude());
        assertNull(properties1.sc().service().impl().getFormatFileName());

        // 创建一个新的NeedProperties，并赋值
        NeedProperties properties2 = new NeedProperties();
        TemplateProperties templateProperties = new TemplateProperties();
        templateProperties.setXml("xml");
        templateProperties.setDisable(new TemplateType[]{TemplateType.ENTITY});
        TemplateJavaProperties javaProperties = new TemplateJavaProperties();
        javaProperties.setEntity("java entity");
        templateProperties.setJavaProperties(javaProperties);
        properties2.setTemplateProperties(templateProperties);
        GlobalProperties globalProperties = new GlobalProperties();
        globalProperties.setAuthor("huangad");
        globalProperties.setSwagger(true);
        globalProperties.setSpringdoc(false);
        globalProperties.setDateType(DateType.ONLY_DATE);
        properties2.setGlobalProperties(globalProperties);
        StrategyServiceImplProperties serviceImplProperties = new StrategyServiceImplProperties();
        serviceImplProperties.setFormatFileName("impl file name");
        StrategyServiceProperties serviceProperties = new StrategyServiceProperties();
        serviceProperties.setServiceImplProperties(serviceImplProperties);
        StrategyProperties strategyProperties = new StrategyProperties();
        strategyProperties.setServiceProperties(serviceProperties);
        strategyProperties.setInclude(Lists.arrayList("1", "2"));
        assertThrowsExactly(ValidateException.class, () -> strategyProperties.setExclude(Lists.arrayList("3", "4")));
        properties2.setStrategyProperties(strategyProperties);

        log.info("----- init properties2 -----");
        log.info(JsonUtil.toJson(properties2));
        assertEquals("java entity", properties2.tp().java().getEntity());
        assertEquals("xml", properties2.tp().getXml());
        assertEquals("[ENTITY]", ArrayUtil.toString(properties2.tp().getDisable()));
        assertEquals("huangad", properties2.gc().getAuthor());
        assertTrue(properties2.gc().isSwagger());
        assertFalse(properties2.gc().isSpringdoc());
        assertEquals(DateType.ONLY_DATE, properties2.gc().getDateType());
        assertLinesMatch(Lists.arrayList("1", "2"), properties2.sc().getInclude());
        assertLinesMatch(Lists.arrayList(), properties2.sc().getExclude());
        assertEquals("impl file name", properties2.sc().service().impl().getFormatFileName());

        // 拷贝属性
        BeanUtil.copy(properties2, properties1);
        log.info("----- copy after properties1 -----");
        log.info(JsonUtil.toJson(properties1));
        assertEquals("java entity", properties1.tp().java().getEntity());
        assertEquals("xml", properties1.tp().getXml());
        assertEquals("[ENTITY]", ArrayUtil.toString(properties1.tp().getDisable()));
        assertEquals("huangad", properties1.gc().getAuthor());
        assertTrue(properties1.gc().isSwagger());
        assertFalse(properties1.gc().isSpringdoc());
        assertEquals(DateType.ONLY_DATE, properties1.gc().getDateType());
        assertLinesMatch(Lists.arrayList("1", "2"), properties1.sc().getInclude());
        assertLinesMatch(Lists.arrayList(), properties1.sc().getExclude());
        assertEquals("impl file name", properties1.sc().service().impl().getFormatFileName());

        log.info("----- copy after properties2 -----");
        log.info(JsonUtil.toJson(properties2));
        assertEquals("java entity", properties2.tp().java().getEntity());
        assertEquals("xml", properties2.tp().getXml());
        assertEquals("[ENTITY]", ArrayUtil.toString(properties2.tp().getDisable()));
        assertEquals("huangad", properties2.gc().getAuthor());
        assertTrue(properties2.gc().isSwagger());
        assertFalse(properties2.gc().isSpringdoc());
        assertEquals(DateType.ONLY_DATE, properties2.gc().getDateType());
        assertLinesMatch(Lists.arrayList("1", "2"), properties2.sc().getInclude());
        assertLinesMatch(Lists.arrayList(), properties2.sc().getExclude());
        assertEquals("impl file name", properties2.sc().service().impl().getFormatFileName());
    }
}