package cn.need.cloud.log.client.dto;


import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 错误日志记录信息 dto对象
 *
 * <AUTHOR>
 */
@Data
public class ErrorRecordDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    private Long id;

    /**
     * 日志记录id，UUID，用来标记记录、请求、响应的关联关系
     */
    private String recordId;

    /**
     * 请求用户
     */
    private String requestUser;

    /**
     * 请求用户角色信息
     */
    private String requestUserRole;

    /**
     * 请求时间
     */
    private LocalDateTime requestTime;

    /**
     * 请求用户
     */
    private String requestUrl;

    /**
     * 请求ip地址
     */
    private String requestIp;

    /**
     * 请求来源
     */
    private String requestSource;

    /**
     * 响应状态
     */
    private String status;

    /**
     * 错误编码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

}