package cn.need.cloud.dfs.model.vo;

import cn.need.framework.common.annotation.dict.DictField;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 模版文件 vo对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "模版文件 VO对象")
public class TemplateFileVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @Schema(description = "id主键")
    private Long id;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String description;

    /**
     * 模版编码
     */
    @Schema(description = "模版编码", required = true)
    private String templateCode;
    /**
     * 模版编码名称
     */
    @Schema(description = "模版编码名称")
    @DictField("EXPORT_IMPORT")
    private String templateCodeName;

    /**
     * 模版名称
     */
    @Schema(description = "模版名称", required = true)
    private String templateName;

    @Schema(description = "模版路径")
    private String path;

    @Schema(description = "接口地址")
    private String api;

    /**
     * 文件地址
     */
    @Schema(description = "文件地址", required = true)
    private String fileUrl;
    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人", index = 8)
    @Schema(description = "创建人")
    private Long createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "更新时间", index = 9)
    @Schema(description = "更新时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人", index = 10)
    @Schema(description = "更新人")
    private Long updateBy;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间", index = 11)
    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "模版文件附件id")
    private Long attachId;

    @Schema(description = "状态 1:有效 0:无效 ")
    private Integer state;
}