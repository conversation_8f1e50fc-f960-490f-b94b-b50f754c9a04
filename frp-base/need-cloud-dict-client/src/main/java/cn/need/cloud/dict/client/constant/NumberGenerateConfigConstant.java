package cn.need.cloud.dict.client.constant;

import cn.need.framework.common.core.constant.StringPool;

/**
 * 字典常量
 *
 * <AUTHOR>
 */
public class NumberGenerateConfigConstant {

    /**
     * 字典在redis中的key值
     */
    public static final String NUMBER_GENERATE_REDIS_KEY = "NUMBER_GENERATE".concat(StringPool.COLON).concat("KEY");

    /**
     * 默认字典的值集$
     */
    public static final String DEFAULT_ROOT = StringPool.DOLLAR;

    /**
     * 配置在redis中的key值
     */
    public static final String CONFIG_REDIS_KEY = "CONF".concat(StringPool.COLON).concat("KEY");

    /**
     * 默认配置的分组$
     */
    public static final String DEFAULT_GROUPING = StringPool.DOLLAR;

    /**
     * 弹窗在redis中的key值
     */
    public static final String POPUP_REDIS_KEY = "POPUP".concat(StringPool.COLON).concat("KEY");

    /**
     * 用来约定字典映射字段命名
     */
    public static final String END_FILE_NAME = "Name";

    /**
     * 控制批量生成编码最大生成数量
     */
    public static final String GENERATE_CODE_MAX = "GENERATE_CODE_MAX";
}
