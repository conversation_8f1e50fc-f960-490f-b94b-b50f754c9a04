package cn.need.cloud.upms.model.vo.permissions;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 业务角色信息 vo对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "业务角色信息 VO对象")
public class BusinessAndRoleVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @Schema(description = "id主键")
    private Long id;

    /**
     * 业务数据类型，可以是用户、组织、职位、分组
     */
    @Schema(description = "业务数据类型，可以是用户、组织、职位、分组")
    private String businessType;

    /**
     * 业务数据id
     */
    @Schema(description = "业务数据id")
    private Long businessId;

    /**
     * 角色id
     */
    @Schema(description = "角色id")
    private Long roleId;

    /**
     * 分组信息，同一分组下的配置编号唯一
     */
    @Schema(description = "分组信息，同一分组下的配置编号唯一")
    private String roleGroup;

}