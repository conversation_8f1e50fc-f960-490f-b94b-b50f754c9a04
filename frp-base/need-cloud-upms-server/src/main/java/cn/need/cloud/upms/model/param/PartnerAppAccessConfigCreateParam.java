package cn.need.cloud.upms.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * PartnerInfoVO vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@Schema(description = "企业应用权限配置新建参数")
public class PartnerAppAccessConfigCreateParam implements Serializable {

    @Serial
    private static final long serialVersionUID = 2382678003490595306L;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称")
    private String name;

    /**
     * 合作伙伴用户ID
     */
    @Schema(description = "合作伙伴用户ID")
    private Long partnerUserId;

    /**
     * 应用ID
     */
    @Schema(description = "应用ID")
    private String appId;


    /**
     * 应用密钥
     */
    @Schema(description = "应用密钥")
    private String appSecret;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型")
    private String serviceType;

    /**
     * 是否激活
     */
    @Schema(description = "是否激活")
    private Boolean activeFlag;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 版本
     */
    @Schema(description = "版本")
    private Long version;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private Long createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Integer removeFlag;
}