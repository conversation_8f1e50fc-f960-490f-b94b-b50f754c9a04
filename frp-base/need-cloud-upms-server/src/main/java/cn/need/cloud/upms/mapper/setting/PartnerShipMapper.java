package cn.need.cloud.upms.mapper.setting;


import cn.need.cloud.upms.model.entity.setting.PartnerShip;
import cn.need.cloud.upms.model.query.PartnerShipQuery;
import cn.need.cloud.upms.model.vo.setting.PartnerShipPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 企业运输 Mapper 接口
 * </p>
 * <p>
 * 该接口主要用于处理与企业运输相关的数据查询操作，提供基于查询对象的分页和不分页查询方法。
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Mapper
public interface PartnerShipMapper extends SuperMapper<PartnerShip> {

    /**
     * 根据查询对象列出企业运输信息
     *
     * @param query 查询条件
     * @return 返回企业运输集合
     */
    List<PartnerShipPageVO> listByQuery(@Param("qo") PartnerShipQuery query);

    /**
     * 根据查询对象和分页对象列出企业运输信息
     *
     * @param query 查询条件
     * @param page  分页对象
     * @return 返回企业运输集合
     */
    List<PartnerShipPageVO> listByQuery(@Param("qo") PartnerShipQuery query, @Param("page") Page<?> page);
}
