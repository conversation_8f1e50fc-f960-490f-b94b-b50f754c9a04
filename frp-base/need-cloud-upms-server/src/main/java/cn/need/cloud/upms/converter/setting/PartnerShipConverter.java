package cn.need.cloud.upms.converter.setting;


import cn.need.cloud.upms.client.dto.setting.PartnerShipDTO;
import cn.need.cloud.upms.model.entity.setting.PartnerShip;
import cn.need.cloud.upms.model.vo.setting.PartnerShipVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 企业运输 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
public class PartnerShipConverter extends AbstractModelConverter<PartnerShip, PartnerShipVO, PartnerShipDTO> {

}
