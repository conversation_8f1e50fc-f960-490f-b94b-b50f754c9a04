package cn.need.cloud.upms.model.query.tenant;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 租户 vo对象
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "租户 VO对象")
public class TenantQuery extends SuperQuery {

    /**
     * 租户编码
     */
    @Schema(description = "租户编码")
    private String tenantCode;

    /**
     * 状态 1:有效 0:无效
     */
    @Schema(description = "状态 1:有效 0:无效")
    private Integer state;

    /**
     * 租户logo
     */
    @Schema(description = "租户logo")
    private String tenantLogo;

    /**
     * 合作伙伴名称
     */
    @Schema(description = "合作伙伴名称")
    private String name;

    /**
     * 合作伙伴名称集合
     */
    @Schema(description = "合作伙伴名称集合")
    @Condition(value = Keyword.IN, fields = "name")
    private List<String> nameList;

    /**
     * 合作伙伴简称
     */
    @Schema(description = "合作伙伴简称")
    private String abbrName;

    /**
     * 合作伙伴简称集合
     */
    @Schema(description = "合作伙伴简称集合")
    @Condition(value = Keyword.IN, fields = "abbrName")
    private List<String> abbrNameList;

    /**
     * 联系人姓名
     */
    @Schema(description = "联系人姓名")
    private String contactName;

    /**
     * 联系人姓名集合
     */
    @Schema(description = "联系人姓名集合")
    @Condition(value = Keyword.IN, fields = "contactName")
    private List<String> contactNameList;

    /**
     * 地址名称
     */
    @Schema(description = "地址名称")
    private String addressName;

    /**
     * 公司地址
     */
    @Schema(description = "公司地址")
    private String addressCompany;

    /**
     * 国家
     */
    @Schema(description = "国家")
    private String addressCountry;

    /**
     * 州/省
     */
    @Schema(description = "州/省")
    private String addressState;

    /**
     * 城市
     */
    @Schema(description = "城市")
    private String addressCity;

    /**
     * 邮编
     */
    @Schema(description = "邮编")
    private String addressZipCode;

    /**
     * 地址1
     */
    @Schema(description = "地址1")
    private String addressAddr1;

    /**
     * 地址2
     */
    @Schema(description = "地址2")
    private String addressAddr2;

    /**
     * 地址3
     */
    @Schema(description = "地址3")
    private String addressAddr3;

    /**
     * 地址邮箱
     */
    @Schema(description = "地址邮箱")
    private String addressEmail;

    /**
     * 地址电话
     */
    @Schema(description = "地址电话")
    private String addressPhone;

    /**
     * 地址备注
     */
    @Schema(description = "地址备注")
    private String addressNote;

    /**
     * 联系人邮箱
     */
    @Schema(description = "联系人邮箱")
    @Condition(value = Keyword.LIKE)
    private String contactEmail;

    /**
     * 联系人邮箱集合
     */
    @Schema(description = "联系人邮箱集合")
    @Condition(value = Keyword.IN, fields = "contactEmail")
    private List<String> contactEmailList;

    /**
     * 伙伴类型筛选
     */
    @Schema(description = "伙伴类型筛选")
    private String partnerType;

    /**
     * 伙伴类型筛选
     */
    @Schema(description = "伙伴类型筛选集合")
    private List<String> partnerTypeList;

}
