package cn.need.cloud.upms.converter.user;

import cn.need.cloud.upms.client.dto.UserTenantDTO;
import cn.need.cloud.upms.model.entity.user.UserTenant;
import cn.need.cloud.upms.model.vo.tenant.UserTenantVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 用户租户信息 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
public class UserTenantConverter extends AbstractModelConverter<UserTenant, UserTenantVO, UserTenantDTO> {

}
