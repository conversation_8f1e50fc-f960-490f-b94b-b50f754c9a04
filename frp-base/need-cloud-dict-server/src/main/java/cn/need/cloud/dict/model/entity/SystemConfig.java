package cn.need.cloud.dict.model.entity;

import cn.need.framework.common.annotation.validation.Unique;
import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 系统配置
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dict_config")
public class SystemConfig extends SuperModel {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 配置编号
     */
    @Unique(message = "变量代码[{}]重复", combined = {"confGroup"})
    @TableField("conf_code")
    private String confCode;

    /**
     * 配置名称
     */
    @TableField("conf_name")
    @Unique(message = "变量名称[{}]重复", combined = {"confGroup"})
    private String confName;

    /**
     * 配置值
     */
    @TableField("conf_value")
    private String confValue;

    /**
     * 分组信息，同一分组下的配置编号唯一
     */
    @TableField("conf_group")
    private String confGroup;

    /**
     * 数据状态，0 - 无效，1 - 有效
     */
    @TableField("state")
    private Integer state;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

}
