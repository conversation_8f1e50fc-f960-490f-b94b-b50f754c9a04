package cn.need.cloud.log.service.impl;

import cn.need.cloud.log.mapper.ErrorRecordMapper;
import cn.need.cloud.log.model.entity.ErrorRecord;
import cn.need.cloud.log.service.ErrorRecordService;
import cn.need.cloud.log.util.LoggerRecordUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 错误日志记录信息 服务实现
 *
 * <AUTHOR>
 */
@Service
public class ErrorRecordServiceImpl extends SuperServiceImpl<ErrorRecordMapper, ErrorRecord> implements ErrorRecordService {

    @Override
    public void deleteByIds(List<Long> ids) {
        LoggerRecordUtil.deleteRecord(ids, this.mapper);
    }

    @Override
    public void clear(QueryWrapper<ErrorRecord> wrapper) {
        this.mapper.truncateTable();
        /*if (isNull(wrapper)) {
            return;
        }
        //根据条件获取数据
        List<ErrorRecord> records = super.list(wrapper);
        //删除错误日志记录
        this.deleteByIds(getPropertyList(records, ErrorRecord::getId));
        //获取记录id集合
        List<String> recordIds = getPropertyList(records, ErrorRecord::getRecordId);
        //删除对应的请求数据
        requestDataService.deleteByRecordIds(recordIds);
        //删除对应的响应数据
        responseDataService.deleteByRecordIds(recordIds);*/
    }

    @Override
    public List<ErrorRecord> listByRecordIds(List<String> recordIds) {
        return LoggerRecordUtil.queryRecord(recordIds, this);
    }

}
