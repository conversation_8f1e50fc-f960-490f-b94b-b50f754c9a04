package cn.need.cloud.log.util;

import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.reflect.ClassUtil;
import cn.need.framework.common.mybatis.base.SuperMapper;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.model.SuperModel;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;
import java.util.List;

import static cn.need.framework.common.core.lang.ObjectUtil.*;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
@Slf4j
public class LoggerRecordUtil {

    /**
     * 分组大小
     */
    private static final int GROUP_SIZE = 1000;

    /**
     * 用来做删除的方法名字
     */
    private static final String DELETE_METHOD = "deleteByIds";

    /**
     * 根据记录id集合，通用获取日志记录的方法，该方法用于批量操作，处理集合参数大于1000的情况
     *
     * @param recordIds 记录id集合
     * @param service   逻辑处理类
     * @param <T>       实体泛型
     * @param <S>       逻辑类泛型
     * @return List<T> 查询后的数据
     */
    public static <T extends SuperModel, S extends SuperService<T>> List<T> queryRecord(List<String> recordIds, S service) {
        List<T> records = Lists.arrayList();
        List<List<String>> group = group(recordIds, GROUP_SIZE);
        for (List<String> list : group) {
            if (isEmpty(list)) {
                continue;
            }
            records.addAll(service.query().in("record_id", list).list());
        }
        return records;
    }

    /**
     * 根据数据id集合，物理删除日志记录，该方法用于批量操作，处理集合参数大于1000的情况
     *
     * @param ids    数据id集合
     * @param mapper mapper处理类
     * @param <T>    实体类泛型
     * @param <M>    mapper处理类泛型
     */
    public static <T extends SuperModel, M extends SuperMapper<T>> void deleteRecord(List<Long> ids, M mapper) {
        //对id集合分组
        List<List<Long>> group = group(ids, GROUP_SIZE);
        Method method = null;
        for (List<Long> list : group) {
            if (isEmpty(list)) {
                continue;
            }
            //反射获取执行方法
            if (isNull(method)) {
                method = ClassUtil.getPublicMethod(mapper.getClass(), DELETE_METHOD, List.class);
                if (isNull(method)) {
                    continue;
                }
            }
            //执行删除操作
            try {
                method.invoke(mapper, list);
            } catch (Exception e) {
                log.error("LoggerRecordUtil执行deleteRecord方法异常：{}", e.getMessage(), e);
            }
        }
    }
}
