package cn.need.cloud.apicenter.frp.model.query.product;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 产品扫描 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "产品扫描 query对象")
public class ProductScanQuery extends SuperQuery {

    /**
     * 扫描编号
     */
    @Schema(description = "扫描编号")
    private String scanNum;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    // /**
    //  * 乐观锁版本号
    //  */
    // @Schema(description = "乐观锁版本号")
    // private Long version;
    //
    // /**
    //  * 租户id
    //  */
    // @Schema(description = "租户id")
    // private Long tenantId;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String productType;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型集合")
    @Condition(value = Keyword.IN, fields = {"productType"})
    private List<String> productTypeList;


}