package cn.need.cloud.apicenter.frp.rest;

import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 健康
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/open/deployment")
@Tag(name = "deployment")
public class DeploymentController extends AbstractController {
    private static final String CURRENT_DEPLOYMENT_MARKER = "v1.0.0-20250512001";

    @GetMapping("/deployment-check")
    public String verifyDeployment() {
        return "Current deployment: " + CURRENT_DEPLOYMENT_MARKER;
    }
}
